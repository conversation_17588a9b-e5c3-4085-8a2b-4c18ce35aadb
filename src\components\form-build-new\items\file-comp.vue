<template>
  <div v-loading="getRouteUrl" class="file-comp-model">
    <el-button
      @click="selectFileType"
      :size="btnsize"
      :disabled="value.length >= maxLength"
    >
      {{ buttonText }}
      <slot v-if="!buttonText" name="button"></slot>
    </el-button>
    <p class="fileInfo">
      仅支持类型为docx/doc/pdf/jpg/png的文件，且文件大小不超过20M
    </p>
    <div v-if="remarks" class="upload-file-remarks">{{ remarks }}</div>
    <slot v-if="!remarks" name="trigger"></slot>
    <ul v-show="value.length" class="selected-list-box">
      <li v-for="(item, index) in value" :key="index" class="file-item">
        <div v-if="item.id">
          <img
            v-oss
            class="file-item-icon"
            :src="getDocIconFun(item.fileType)"
            alt=""
          />
          <div class="file-item-name">
            {{ item.fileName + '.' + item.fileType }}
          </div>
          <!-- <div @click="seeFile(item)" class="file-item-see">查看</div> -->

          <i class="el-icon-circle-check" />
          <!-- <el-buttontype="text"></el-button> -->
          <i
            @click="handleOnRemove(item, index)"
            class="el-icon-circle-close"
          />
        </div>
        <div v-else>
          <el-image
            v-if="srcList.length"
            :src="encodeOSSUrl(item.fileUrl)"
            :preview-src-list="getPreviewList(item.fileUrl)"
            :class="`js_imgPreviewModel${key}${createTime}${index} img-preview-model`"
          >
          </el-image>
          <img
            v-oss
            class="file-item-icon"
            :src="getDocIconFun(item.fileType)"
            alt=""
          />
          <span></span>
          <div class="file-item-name">
            {{ item.fileName }}
            <!-- {{ item.fileName + '.' + item.fileType }} -->
          </div>

          <!-- {{ item.size }} -->
          <i class="el-icon-circle-check" />
          <!-- <div @click="seeFile(item, index)" class="file-item-see">查看</div> -->

          <i
            @click="handleOnRemove(item, index)"
            class="el-icon-circle-close"
          />
        </div>
      </li>
    </ul>
    <!-- :width="isFileTemplate ? '638px' : '528px'" -->
    <el-dialog
      title="附件上传"
      :visible.sync="dialogVisible"
      width="280px"
      :before-close="handleClose"
      :class="{ isFileTemplate: isFileTemplate }"
      append-to-body
    >
      <ul class="file-upload-type">
        <!-- <li
          v-show="isFileTemplate"
          @click="templateDialogShow"
          class="file-upload-type-list"
        >
          <img
            v-oss
            src="/document/document-file-template.png"
            alt=""
            class="documnet-type-template-icon"
          />
          <div class="documnet-type-text">选择合同模板</div>
        </li> -->
        <li class="file-upload-type-list">
          <h-upload
            ref="upload"
            v-model="hValue"
            show-loading
            multiple
            :on-success="handleOnSuccess"
            :on-error="handleOnError"
            :accept="accept"
          >
            <!-- <el-button slot="trigger">从本地上传</el-button> -->
            <img
              v-oss
              src="/document/documnet-local-file-icon.png"
              alt=""
              class="documnet-type-icon-local"
            />
            <div class="documnet-type-text-local">从本地上传</div>
          </h-upload>
          <!-- <el-button v-else disabled slot="trigger">从本地上传</el-button> -->
        </li>
        <!-- <li @click="documentDialogShow" class="file-upload-type-list">
          <img
            v-oss
            src="/document/documnet-file-icon.png"
            alt=""
            class="documnet-type-icon"
          />
          <div class="documnet-type-text">从文档上传</div>
        </li> -->
      </ul>
    </el-dialog>
    <document-dialog
      ref="documentDialog"
      @close="visible = false"
      @updatelist="updateList"
      :selected-num="value.length"
      :visible="visible"
      :max-length="maxLength"
    />
    <template-dialog
      @update-temlate="updateList"
      :template-visible.sync="templateVisible"
    ></template-dialog>
  </div>
</template>

<script>
  import DocumentDialog from '@/components/document-dialog';
  import templateDialog from '@/components/template-dialog';
  import { getDocIcon, encodeOSSUrl } from '@/util/util';
  import { getWpsViewUrl } from '@/api/resource/wps';

  export default {
    name: 'FileComp',
    components: {
      DocumentDialog,
      templateDialog
    },
    props: {
      btnsize: {
        type: String
      },
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      remarks: {
        type: String
      },
      buttonText: {
        type: String
      },
      // 是否显示从模板上传
      isFileTemplate: {
        type: Boolean,
        default: false
      },
      // 文件上传最大个数
      maxLength: {
        type: Number,
        default: 30
      },
      // 文件上传类型
      accept: {
        type: String,
        default:
          '.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .xls, .xlsx, .pdf, .ppt'
      },
      // 文件大小限制 单位MB 取值 1~102.4
      maxFile: {
        type: String,
        default: ''
      },
      // 附件能否编辑
      isFileEdit: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        createTime: null,
        getRouteUrl: false,
        dialogVisible: false, // 选择文档类型弹窗控制
        visible: false, // 选择文档弹窗控制
        localUploadFile: {}, // 本地上传文件存储列表
        documentListObj: {}, // 文档中选择的文件列表
        selectedDoucmentArr: [],
        hValue: [],
        key: null,
        templateVisible: false,
        againDocumentList: [], // 再次发起时带入的数据
        srcList: [],
        encodeOSSUrl
      };
    },
    watch: {
      value() {
        this.initSrcList();
      }
    },
    created() {
      this.createTime = Date.parse(new Date());
      this.key = new Date().getTime();
      this.initSrcList();
    },

    methods: {
      initSrcList() {
        let arr = [];
        this.value.map(({ fileType, fileUrl }) => {
          if (this.imageType(fileType)) {
            arr.push(encodeOSSUrl(fileUrl));
          }
        });
        this.srcList = arr;
      },
      templateDialogShow() {
        this.templateVisible = true;
        this.dialogVisible = false;
      },
      // 打开从文档选择弹窗
      documentDialogShow() {
        this.visible = true;
        this.$refs.documentDialog.getFileListFun();
        this.$refs.documentDialog.getGroupFileListFun();
        this.$refs.documentDialog.initSelectList(
          this.documentListObj,
          this.localUploadFile
        );
        // 关闭选择类型弹窗
        this.dialogVisible = false;
      },
      // 图片类型判定
      imageType(type) {
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(type);
      },
      // 获取图标
      getDocIconFun(type) {
        return getDocIcon(type);
      },
      // 查看文档
      seeFile(item, index) {
        let { fileUrl } = item;
        // 如果是图片格式文件
        if (this.imageType(item.fileType)) {
          let imgBox = document.getElementsByClassName(
            `js_imgPreviewModel${this.key}${this.createTime}${index}`
          )[0];
          imgBox.getElementsByTagName('img')[0].click();
        } else if (this.isFileEdit) {
          // 判断能否跳到wps编辑页面
          const docName = item.originalName || item.docName;
          const fileType = item.type || item.fileType;
          const id = item.id || item.fileId;
          this.$emit('edit-file', { docName, fileType, id });
        } else {
          if (fileUrl) {
            // 获取文件预览url
            getWpsViewUrl(fileUrl).then((res) => {
              // console.log("res", res);
              if (res.data && res.data.success) {
                if (res.data.data) {
                  window.open(res.data.data);
                } else {
                  this.$message.warning('预览地址获取失败');
                }
              }
            });
          }
          // let urlFarst = "https://wps.atila.cn/onlinePreview?url=";
          // if (fileId) {
          //   // window.open(`${urlFarst}${fileUrl}`, "_blank");
          //   let { docName, fileType } = item;
          //   let routeUrl = this.$router.resolve({
          //     // path: `/documentDetail`,
          //     name: "documentDetailPreview",
          //     query: {
          //       url: `${urlFarst}${fileUrl}`,
          //       docName,
          //       fileType,
          //       fileUrl,
          //     },
          //   });
          //   window.open(routeUrl.href);
          // } else {
          //   window.open(`${urlFarst}${fileUrl}`, "_blank");
          // }
        }
      },
      // 更新从文档中选择的文件
      updateList(object) {
        const isAccept = Object.values(object).every((l) =>
          this.accept.includes(
            l.fileUrl.split('.')[l.fileUrl.split('.').length - 1]
          )
        );
        if (!isAccept) {
          this.$message.error('文件格式不正确');
        } else {
          this.documentListObj = { ...this.documentListObj, ...object };
          this.selectedDoucmentUpdate(object);
        }
      },
      selectedDoucmentUpdate(object) {
        let arr = [];
        // let parentFromArr = [];
        Object.keys(object).map((key) => {
          let element = object[key];
          let { docName, fileUrl, id } = element;
          let fileName = docName.substring(0, docName.lastIndexOf('.'));
          let fileType = docName.substring(docName.lastIndexOf('.') + 1);
          arr.push({
            fileName,
            fileType,
            fileUrl,
            fileId: id
          });
        });
        this.$emit('input', [...this.value, ...arr]);
      },
      // 移除已经上传成功文件
      handleOnRemove(item, index) {
        let newValue = JSON.parse(JSON.stringify(this.value));
        newValue.splice(index, 1);
        this.$emit('input', newValue);
        this.$forceUpdate();
      },
      // 上传成功的回调
      handleOnSuccess(response, file, fileList) {
        // 关闭上传方式弹窗
        this.dialogVisible = false;
        if (response.success) {
          if (this.maxFile && file.size / (1024 * 1024) > this.maxFile) {
            this.$message.error('文件大小超过限制');
            return;
          }

          if (this.value.length >= this.maxLength) {
            this.$message({
              message: `已选${this.value.length}，本次最多选择0个`,
              type: 'error'
            });
            return;
          }
          if (this.selectedDoucmentArr.length >= this.maxLength) {
            this.$message({
              message: `文件最多上传${this.selectedDoucmentArr.length}个`,
              type: 'error'
            });
          } else {
            // console.log('response', response);
            // console.log('file', file);
            // console.log('fileList', fileList);
            let optionTime = new Date().getTime();
            // response.data.optionTime = optionTime;
            // let { name } = response.data;
            // this.fileList.push(response.data);
            response.data.size = this.fileSize(file.size);
            response.data.type = fileList[fileList.length - 1].type;
            // this.documentListObj[optionTime] = response.data;
            this.localUploadFile[optionTime] = response.data;
            let { link, originalName } = response.data;
            // let fileName = originalName.substring(
            //   0,
            //   originalName.lastIndexOf('.')
            // );
            response.data.fileName = originalName;
            response.data.fileUrl = link;
            response.data.fileType = originalName.substring(
              originalName.indexOf('.')
            );
            let newValue = JSON.parse(JSON.stringify(this.value));
            newValue.push(response.data);
            this.$emit('input', newValue);
          }
        }
      },
      // 文件大小转换
      fileSize(size) {
        let data = '';
        if (size < 0.1 * 1024) {
          // 如果小于0.1KB转化成B
          data = size.toFixed(2) + 'B';
        } else if (size < 0.1 * 1024 * 1024) {
          // 如果小于0.1MB转化成KB
          data = (size / 1024).toFixed(2) + 'KB';
        } else if (size < 0.1 * 1024 * 1024 * 1024) {
          // 如果小于0.1GB转化成MB
          data = (size / (1024 * 1024)).toFixed(2) + 'MB';
        } else {
          // 其他转化成GB
          data = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
        }
        let sizestr = data + '';
        let len = sizestr.indexOf('.');
        let dec = sizestr.substr(len + 1, 2);
        if (dec === '00') {
          // 当小数点后为00时 去掉小数部分
          return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
        }
        return sizestr;
      },
      handleOnError() {
        // if (this.onError) {
        //   this.onError(err, file, fileList);
        // }
      },
      handleClose() {
        this.dialogVisible = false;
      },
      // 选择附件类型
      selectFileType() {
        this.dialogVisible = true;
      },
      // 选择文档
      selectDocument() {
        this.visible = true;
      },
      getPreviewList(url) {
        let list = [...this.srcList];
        let index = list.indexOf(url);
        if (index > -1) {
          let preList = list.splice(0, index) || [];
          list = list.concat(preList);
        }
        return list;
      }
    }
  };
</script>
<style lang="scss">
  .file-comp-model {
    .upload-file-remarks {
      display: inline-block;
      padding-left: 8px;
      color: #cfcfcf;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
    }

    .selected-list-box {
      margin-bottom: 0;
      padding-left: 0;
    }

    .file-item {
      margin-bottom: 4px;
      padding: 15px 20px 15px 18px;
      list-style: none;
      background: #f7f8fa;
      border-radius: 4px;

      .file-item-icon {
        width: 14px;
        height: 16px;
        padding-right: 8px;
        vertical-align: middle;
      }

      .file-item-name {
        display: inline-block;
        max-width: 322px;
        padding-right: 19px;
        overflow: hidden;
        line-height: 16px;
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: middle;
      }

      .file-item-see {
        display: none;
        color: rgb(64, 158, 255);
        font-size: 14px;
        line-height: 14px;
        line-height: 16px;

        vertical-align: middle;
        cursor: pointer;
      }

      .el-icon-circle-check,
      .el-icon-circle-close {
        float: right;
        padding-top: 3px;
      }

      &:hover {
        .file-item-see {
          display: inline-block;
        }
      }
    }

    .file-item {
      .el-icon-circle-close {
        display: none;
        color: #a3a3a3;
        font-size: 16px;
        cursor: pointer;

        &:hover {
          color: #757575;
        }
      }

      &:hover {
        .el-icon-circle-check {
          display: none;
        }

        .el-icon-circle-close {
          display: inline-block;
        }
      }
    }

    .el-icon-circle-check {
      color: #70cd44;
      font-size: 16px;
    }

    .img-preview-model {
      width: 0;
      height: 0;
    }

    .isFileTemplate {
      .file-upload-type-list {
        width: 170px;
      }
    }

    .el-image-viewer__wrapper {
      .el-icon-circle-close {
        display: block;
        color: #fff;
        font-size: 40px;

        &:hover {
          color: #fff;
        }
      }
    }
  }

  .fileInfo {
    // color: #3c7cff;
    margin-top: 0;
    color: #aeafb3;

    // text-align: center;
    // transform: scale(0.9);
  }

  .file-upload-type {
    margin: 0;
    padding: 16px 0 40px;

    .file-upload-type-list {
      display: inline-block;
      width: 212px;
      height: 150px;
      margin: 0 12px;
      font-size: 0;
      text-align: center;
      vertical-align: top;
      list-style: none;
      background: #f7f8fa;
      border-radius: 8px;
      cursor: pointer;

      &:hover {
        background: #f0f2f5;
      }

      .h-upload-content {
        width: 100%;
        height: 150px;

        .el-upload {
          width: 100%;
          height: 150px;
        }
      }

      .documnet-type-icon {
        width: 42px;
        height: 35px;
        padding-top: 43px;
      }

      .documnet-type-template-icon {
        width: 36px;
        height: 42px;
        padding-top: 43px;
      }

      .documnet-type-text,
      .documnet-type-text-local {
        padding-top: 22px;
        color: #121620;
        font-size: 16px;
      }

      .documnet-type-text-local {
        padding-top: 18px;
      }

      .documnet-type-icon-local {
        width: 42px;
        height: 39px;
        padding-top: 43px;
      }
    }
  }

  .isFileTemplate {
    .file-upload-type-list {
      width: 170px !important;
    }
  }
</style>
