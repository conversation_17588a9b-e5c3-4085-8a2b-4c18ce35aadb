<template>
  <div class="bc-form-build-tips">
    <el-link
      v-if="data.link"
      :href="data.link"
      :underline="false"
      target="_blank"
      class="link"
    >
      <i class="el-icon-link" />
      {{ data.content }}
    </el-link>
    <div v-else :class="{ 'color-danger': data.fontColor }">
      <i class="el-icon-warning" />
      <span>{{ data.content }}</span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TipsComp',
    props: {
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-ui';

  .bc-form-build-tips {
    .link {
      color: $--mobile-button-submit;
    }

    i {
      margin-right: 5px;
      font-size: 16px;
      line-height: 32px;
    }

    .el-icon-warning {
      color: $--color-warning;
    }

    .color-danger {
      color: $--color-danger;

      .el-icon-warning {
        color: $--color-danger;
      }
    }
  }
</style>
