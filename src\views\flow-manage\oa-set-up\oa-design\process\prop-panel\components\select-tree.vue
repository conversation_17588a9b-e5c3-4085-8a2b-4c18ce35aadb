<template>
  <div class="multiple-select-tree">
    <div class="conbox has-tabs">
      <div v-show="tips" class="custom-empty-text">{{ tips }}</div>
      <div v-show="!tips">
        <div class="all-select">
          <el-checkbox
            v-model="checkAll"
            @change="handleCheckAllChange"
            :indeterminate="indeterminate"
            >全选</el-checkbox
          >
        </div>
        <div class="titbox">
          <el-input
            v-model="filterText"
            size="mini"
            placeholder="搜索"
            suffix-icon="el-icon-search"
          ></el-input>
        </div>
        <div class="wordbox">
          <el-tree
            ref="tree"
            @check="getData"
            show-checkbox
            class="filter-tree"
            node-key="id"
            default-expand-all
            :data="treeData"
            :props="defaultProps"
            :check-on-click-node="true"
            :filter-node-method="filterNode"
            :check-strictly="radio === 'dept' ? true : false"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <span>{{ node.hasChildren }}</span>
              <img
                v-if="data.dataType === 1"
                v-oss
                src="/apply/oa-people.png"
              />
              <div v-if="data.dataType === 3">
                <div class="avatar">
                  <img v-if="data.avatar" :src="data.avatar" />
                  <img v-else v-oss src="/launch/default-photo.png" />
                </div>
              </div>
              <span class="tree-label">{{
                radio === 'dept'
                  ? data.title
                  : radio === 'position'
                  ? data.postName
                  : radio === 'job'
                  ? data.jobName
                  : data.label
              }}</span>
            </span>
          </el-tree>
        </div>
      </div>
    </div>
    <div class="conbox">
      <div class="all-select">
        <div>已选 {{ checkedIds.length }}</div>
      </div>
      <div class="wordbox delete-box">
        <div class="dept-selected">
          <div v-for="(item, index) in checkedIds" :key="index">
            <div class="inli">
              <div v-if="item.dataType === 3">
                <div class="avatar">
                  <img v-if="item.avatar" :src="item.avatar" />
                  <img v-else v-oss src="/launch/default-photo.png" />
                </div>
              </div>
              <span v-if="item.dataType === 3" class="tree-label">{{
                item.label
              }}</span>
              <span v-else class="tree-label">
                {{
                  item.type === 'dept'
                    ? '部门'
                    : item.type === 'position'
                    ? '岗位'
                    : item.type === 'job'
                    ? '职务'
                    : ''
                }}:
                {{
                  item[
                    item.type === 'dept'
                      ? 'title'
                      : item.type === 'position'
                      ? 'postName'
                      : item.type === 'job'
                      ? 'jobName'
                      : 'label'
                  ]
                }}</span
              >
              <img
                v-oss
                @click="removeData(item)"
                class="icon-close-circle"
                src="/oa/icon-close-circle.png"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  const getAllNode = (list) => {
    let temp = [];
    const getNode = (array) => {
      array.forEach((item) => {
        temp.push(item);
        if (item.children && item.children.length) {
          getNode(item.children);
        }
      });
    };
    getNode(list);
    return temp;
  };
  import { deepClone } from '@/util/util.js';
  export default {
    props: {
      // 当前选中人的id
      checkedList: {
        type: Array,
        default: () => []
      },
      // 当前接口类型， people人员，dept部门 position岗位 job职务
      radio: {
        type: String,
        default: 'people'
      },
      // 当前数据集合
      treeData: {
        type: Array,
        default: () => []
      },
      // 父子是否强关联
      checkStrictly: {
        type: Boolean,
        default: false
      },
      // 选择的最大数量，0为不限制数量
      maxSelectLength: {
        type: Number,
        default: 0
      },
      tips: String
    },
    data() {
      return {
        checkAll: false,
        indeterminate: false,
        checkedIds: [],
        filterText: '',
        listBg: {},
        defaultProps: {
          children: 'children',
          label: 'label',
          isLeaf: 'hasChildren',
          disabled: (data) => {
            // 单选时而且是人的话屏蔽部门
            return (
              (this.radio === 'people' && data.dataType !== 3) ||
              (this.maxSelectLength && this.checkedIds > this.maxSelectLength)
            );
          }
        }
      };
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      },
      checkedIds() {
        this.checkStatus();
        this.$emit('update:checkedList', this.checkedIds);
      },
      treeData() {
        this.filterText = '';
        this.checkStatus();
        this.setTree();
      }
    },
    mounted() {
      this.setTree();
    },
    methods: {
      setTree() {
        if (!this.treeData.length) {
          return;
        }
        if (this.checkedList.length) {
          let typeList = this.checkedList.filter(
            (item) => item.type === this.radio
          );
          this.$refs.tree.setCheckedKeys(typeList.map((item) => item.id));
          this.checkedIds = deepClone(this.checkedList);
        } else {
          this.$refs.tree.setCheckedKeys(
            this.checkedIds.map((item) => {
              if (item.type === this.radio) {
                return item.id;
              }
            })
          );
        }
      },
      checkStatus() {
        let treeLength = [];
        if (this.radio === 'people') {
          treeLength = getAllNode(this.treeData).filter(
            (item) => item.dataType === 3
          );
        } else {
          treeLength = getAllNode(this.treeData);
        }
        let checkLength = this.checkedIds.filter(
          (item) => item.type === this.radio // 过滤出当前类型的所有值
        );
        this.$refs.tree.setCheckedKeys(checkLength.map((item) => item.id));
        if (treeLength.length === checkLength.length) {
          this.checkAll = true;
          this.indeterminate = false;
        } else if (checkLength.length === 0) {
          this.checkAll = false;
          this.indeterminate = false;
        } else {
          this.indeterminate = true;
        }
      },
      // 全选
      handleCheckAllChange() {
        let allchecked = getAllNode(this.treeData);
        allchecked.map((item) => {
          let own = item;
          item = Object.assign(own, { type: this.radio });
        });
        if (this.radio === 'people') {
          allchecked = allchecked.filter((item) => item.dataType === 3);
        }
        this.checkedIds = this.checkedIds.filter(
          (item) => item.type !== this.radio // 过滤掉当前类型的所有值
        );
        if (this.checkAll) {
          this.checkedIds = [...this.checkedIds, ...allchecked];
          if (
            this.maxSelectLength &&
            this.checkedIds.length > this.maxSelectLength
          ) {
            this.checkedIds = this.checkedIds.slice(0, this.maxSelectLength);
            this.$refs.tree.setCheckedNodes(this.checkedIds);
            this.$message.error('可选项已达上限');
          }
        } else {
          this.$refs.tree.setCheckedNodes(this.checkedIds);
        }
      },
      filterNode(value, data) {
        if (!value) {
          return true;
        }
        const label =
          this.radio === 'dept'
            ? 'title'
            : this.radio === 'position'
            ? 'postName'
            : this.radio === 'job'
            ? 'jobName'
            : 'label';
        return data[label].indexOf(value) !== -1;
      },
      // 点击选择部门
      getData(data) {
        // let type = this.radio;
        let treeDataList = getAllNode(this.treeData);
        treeDataList.map((item) => {
          let own = item;
          item = Object.assign(own, { type: this.radio });
        });
        if (this.radio === 'people') {
          let checkList = this.$refs.tree.getCheckedNodes();
          let ids = checkList.filter((c) => c.dataType === 3);
          let temp = deepClone(this.checkedIds);
          temp = temp.filter((item) => {
            if (item.dataType !== 3) {
              return true;
            }
          });
          this.checkedIds = [...temp, ...ids];
        } else {
          let selectTreeData = treeDataList.filter(
            (item) => data.id === item.id
          );
          let temp = deepClone(this.checkedIds);
          let isHas = temp.some((item) => {
            if (item.type === data.type) {
              return item.id === data.id;
            }
          });
          if (isHas) {
            temp = temp.filter((item) => {
              if (item.type === data.type) {
                return item.id !== data.id;
              } else {
                return true;
              }
            });
          } else {
            temp.push(selectTreeData[0]);
          }

          this.checkedIds = temp;
        }
        this.$emit('update:checkedList', this.checkedIds);

        if (
          this.maxSelectLength &&
          this.checkedIds.length > this.maxSelectLength
        ) {
          this.checkedIds = this.checkedIds.slice(0, this.maxSelectLength);
          this.$refs.tree.setCheckedNodes(this.checkedIds);
          this.$message.error('可选项已达上限');
        }
        let tList = getAllNode(this.treeData);
        this.checkAll = this.checkedIds.length === tList.length;
      },
      clearData() {
        this.checkedIds = [];
        this.checkAll = false;
        this.indeterminate = false;
        this.$refs.tree.setCheckedKeys([]);
      },
      // 删除已选部门
      removeData(data) {
        this.checkedIds = this.checkedList.filter(({ id }) => id !== data.id);
        this.$refs.tree.setCheckedKeys(
          this.checkedIds.map((item) => {
            if (item.type === this.radio) {
              return item.id;
            }
          })
        );
        this.$emit('update:checkedList', this.checkedIds);
        let treeDataList = getAllNode(this.treeData);
        this.checkAll = this.checkedIds.length === treeDataList.length;
      }
    }
  };
</script>

<style lang="scss">
  .multiple-select-tree {
    display: flex;
    justify-content: space-between;
    padding: 12px 24px 0;

    .is-leaf {
      background: #fff !important;
    }

    .el-tree-node__content {
      margin: 6px 0;

      &:hover {
        .is-leaf {
          background: #f5f7fa !important;
        }
      }
    }

    .avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      color: #fff;
      font-size: 14px;
      border-radius: 50%;

      img {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        border-radius: 50%;
      }
    }

    .conbox {
      width: 240px;
      height: 446px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;

      .all-select {
        padding: 12px 16px;
        color: #666;
        font-weight: 400;
        font-size: 14px;
        line-height: 19px;
        border-bottom: 1px solid #d9d9d9;
      }

      .titbox {
        padding: 0 16px;
      }

      .wordbox {
        // padding: 0 16px;
        height: 320px;
        overflow-y: scroll;

        .custom-tree-node {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
          width: 150px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }

          .icon-tree-depart {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            margin-left: 4px;
          }

          .tree-label {
            display: flex;
            display: flow-root;
            align-items: center;
            margin-left: 8px;
            overflow: hidden;
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 23px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .delete-box {
        box-sizing: border-box;
        height: 400px;
        padding-top: 10px;
      }

      .dept-selected {
        // padding: 16px;
        .tree-label {
          display: inline-block;

          // margin-left: 8px;
          width: 140px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .inli {
          position: relative;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          padding: 5px 16px;

          .avatar {
            width: 30px;
            height: 30px;
            margin-right: 8px;
          }

          span {
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 30px !important;
          }

          .icon-close-circle {
            position: absolute;
            right: 16px;
            width: 16px;
            height: 16px;
            cursor: pointer;
          }

          &:hover {
            background: white;
          }
        }
      }
    }

    .has-tabs {
      height: 400px;
      padding-top: 46px;

      .custom-empty-text {
        width: 100%;
        height: 100%;
        color: #999;
        line-height: 400px;
        text-align: center;
      }

      .all-select {
        color: #333;
        border-bottom: none;
      }
    }
  }
</style>
