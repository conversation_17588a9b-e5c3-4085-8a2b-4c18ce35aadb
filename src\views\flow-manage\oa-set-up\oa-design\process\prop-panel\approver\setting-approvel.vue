<template>
  <div class="setting-approvel-person">
    <div class="oa-approvel-person-top">
      <div>审批</div>
      <i @click="deleteApprovelPerson" class="el-icon-delete"></i>
    </div>
    <div class="oa-approvel-person-content">
      <div class="oa-radio-list">
        <h-radio-group
          v-model="form.type"
          style="width: 100%"
          @change="handleChangeRadio"
          :data-source="personList"
        ></h-radio-group>
        <!-- <el-tooltip v-if="isShowIcon" placement="top">
          <div slot="content">
            从发起人的直接上级开始，依次逐级向上审批，<br />直到所设置的审批终点为止
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip> -->
      </div>
      <!-- 发起人自选 -->
      <div v-if="form.type === 1" class="oa-dept-level dept-or-people">
        <div class="oa-dept-level-title">限制</div>
        <div class="oa-dept-level-content">
          <h-select
            v-model="form.scopes"
            :data-source="applyType"
            :props="applyProps"
            placeholder="请选择"
          ></h-select>
        </div>
        <div class="oa-dept-level-title">选择范围</div>
        <div class="oa-dept-level-content">
          <h-select
            v-model="form.scopesType"
            :data-source="applyArea"
            :props="applyProps"
            placeholder="请选择"
          ></h-select>
        </div>
        <div v-if="form.scopesType === '2'" class="oa-choose-staff-list">
          <i @click="openDialogVisible('1')" class="el-icon-plus"></i>
          <el-tag
            v-for="(tag, i) in peopleList"
            :key="i"
            @close="handleDeletePerson(i, tag.id, 'peopleList')"
            type="info"
            size="medium"
            class="oa-choose-staff-tag"
            closable
          >
            {{ approverPeople(tag) }}
          </el-tag>
        </div>
      </div>
      <!-- 指定成员的选择成员 -->
      <div v-if="form.type === 2" class="oa-choose-staff">
        <div class="oa-choose-staff-title">选择成员</div>
        <div class="oa-choose-staff-list">
          <i @click="openChooseStaffDialogVisible()" class="el-icon-plus"></i>
          <el-tag
            v-for="(tag, i) in staffList"
            :key="i"
            @close="handleDeleteStaff(i)"
            type="info"
            size="medium"
            class="oa-choose-staff-tag"
            closable
          >
            成员：{{ tag.label }}
          </el-tag>
        </div>
      </div>
      <!-- 部门负责人指定层级 -->
      <div v-else-if="form.type === 3" class="oa-dept-level">
        <div class="oa-dept-level-title">指定层级</div>
        <div class="oa-dept-level-content">
          <h-radio-group
            v-model="form.startLevelType"
            @change="changeDeptLevel"
            :data-source="approverLevelList"
          ></h-radio-group>
          <h-select
            v-model="form.level"
            :data-source="approverDeptList"
            placeholder="请选择"
          ></h-select>
        </div>
      </div>
      <!-- 直接上级指定层级 -->
      <div v-else-if="form.type === 4" class="oa-dept-level">
        <div class="oa-dept-level-title">指定层级</div>
        <div class="oa-dept-level-content">
          <h-radio-group
            v-model="form.startLevelType"
            @change="changeDeptLevel"
            :data-source="approverParentLevelList"
          ></h-radio-group>
          <h-select
            v-model="form.level"
            :data-source="approverParentList"
            placeholder="请选择"
          ></h-select>
        </div>
      </div>
      <!-- 职务的选择职务 -->
      <div v-if="form.type === 5" class="oa-choose-staff">
        <div class="oa-choose-staff-title">选择职务</div>
        <div class="oa-choose-staff-list">
          <i @click="openDialogVisible('2')" class="el-icon-plus"></i>
          <el-tag
            v-for="(tag, i) in careerList"
            :key="i"
            @close="handleDeletePerson(i, tag.id, 'careerList')"
            type="info"
            size="medium"
            class="oa-choose-staff-tag"
            closable
          >
            职务：{{ tag.name }}
          </el-tag>
        </div>
      </div>
      <!-- 岗位的选择岗位 -->
      <div v-if="form.type === 6" class="oa-choose-staff">
        <div class="oa-choose-staff-title">选择岗位</div>
        <div class="oa-choose-staff-list">
          <i @click="openDialogVisible('4')" class="el-icon-plus"></i>
          <el-tag
            v-for="(tag, i) in jobList"
            :key="i"
            @close="handleDeletePerson(i, tag.id, 'jobList')"
            type="info"
            size="medium"
            class="oa-choose-staff-tag"
            closable
          >
            岗位：{{ tag.name }}
          </el-tag>
        </div>
      </div>

      <!-- 角色的选择角色 -->
      <div v-if="form.type === 7" class="oa-choose-staff">
        <div class="oa-choose-staff-title">选择角色</div>
        <div class="oa-choose-staff-list">
          <i @click="openRoleVisiable" class="el-icon-plus"></i>
          <el-tag
            v-for="(tag, i) in roleList"
            :key="i"
            @close="handleDeletePerson(i, tag.id, 'roleList')"
            type="info"
            size="medium"
            class="oa-choose-staff-tag"
            closable
          >
            角色：{{ tag.name }}
          </el-tag>
        </div>
      </div>

      <!-- 审批终点 -->
      <div v-if="form.type === 8" class="oa-dept-level">
        <div class="oa-dept-level-title">审批终点</div>
        <div class="oa-dept-level-content">
          <h-select
            v-model="form.level"
            :data-source="levelEnd"
            placeholder="请选择"
          ></h-select>
        </div>
      </div>

      <!-- 关联组织 -->
      <div v-if="form.type === 9" class="oa-dept-level">
        <div class="oa-dept-level-title">选择组织</div>
        <div class="oa-dept-level-content">
          <h-select
            v-model="form.orgId"
            v-loading="relationLoading"
            @change="handleOrgIdChange"
            :data-source="relationDepts"
            :props="deptProps"
            :placeholder="relationDepts.length ? '请选择' : '暂无关联组织'"
          ></h-select>
        </div>
        <template v-if="form.orgId">
          <div class="oa-choose-staff-title">审批人</div>
          <div class="oa-choose-staff-list">
            <i @click="openDialogVisible('9')" class="el-icon-plus"></i>
            <el-tag
              v-for="(tag, i) in peopleList"
              :key="i"
              @close="handleDeletePerson(i, tag.id, 'peopleList')"
              type="info"
              size="medium"
              class="oa-choose-staff-tag"
              closable
            >
              {{ approverPeople(tag) }}
            </el-tag>
          </div>
        </template>
      </div>
    </div>
    <select-staff-tree
      @rangeSave="getStaffData"
      :range-visible.sync="dialogVisible"
      :title="`选择${staffTitle}`"
      :is-radio="false"
      :radio="staffType"
      :ids="ids"
      :max-select-length="20"
    ></select-staff-tree>
    <choose-staff-tree
      v-if="chooseStaffVisible"
      @choose-save="getChooseList"
      :choose-visible.sync="chooseStaffVisible"
      :ids="staffIds"
    ></choose-staff-tree>
    <choose-roles-person
      @change="getRoles"
      :visible.sync="roleVisible"
      :default-checked-codes="defaultRoles"
    ></choose-roles-person>
    <multiple-staff-tree
      v-if="choosePeopleVisible"
      @rangeSave="getCheckList"
      :range-visible.sync="choosePeopleVisible"
      :ids="peopleIds"
      :org-id="form.orgId"
    ></multiple-staff-tree>
  </div>
</template>

<script>
  import { deepClone } from '@/util/util.js';
  import { getLinkDepts } from '@/api/system/dept';

  import chooseRolesPerson from '../components/choose-roles';
  import chooseStaffTree from '../components/choose-staff';
  import selectStaffTree from '@/components/select-tree/select-people';
  import arrDesgin from '@/util/approvel-desgin';
  import multipleStaffTree from '../components/multiple-staff';

  const APPROVER_PERSON = {
    type: 1,
    dataId: '',
    scopeIds: [],
    startLevelType: '',
    level: '',
    scopes: '2',
    scopesType: '1',
    orgId: ''
  };

  export default {
    components: {
      selectStaffTree,
      chooseRolesPerson,
      chooseStaffTree,
      multipleStaffTree
    },
    props: {
      data: {
        type: Object,
        default: () => {}
      },
      isNullPorperties: {
        type: Object,
        default: () => {}
      },
      index: {
        type: [Number, String],
        default: 0
      },
      dataLength: {
        type: [Number, String],
        default: 0
      }
    },
    data() {
      return {
        form: { ...APPROVER_PERSON },
        applyProps: {
          label: 'dictValue',
          value: 'dictKey'
        },
        applyType: [],
        applyArea: [],
        ids: [],
        staffIds: [],
        peopleIds: [],
        dialogVisible: false,
        roleVisible: false,
        chooseStaffVisible: false, // 选择成员
        choosePeopleVisible: false, // 发起人自选选择成员
        defaultRoles: [],
        staffType: '1', // 成员1 职务2 岗位4
        staffTitle: '成员',
        staffList: [],
        copyStaff: [],
        careerList: [],
        jobList: [],
        peopleList: [],
        roleList: [],
        personList: [
          { label: '发起人自选', value: 1 },
          { label: '指定成员', value: 2 },
          // { label: '部门负责人', value: 3 },
          // { label: '直接上级', value: 4 },
          // { label: '职务', value: 5 },
          { label: '岗位', value: 6 },
          { label: '角色', value: 7 }
          // { label: '连续多级上级', value: 8 },
          // { label: '关联组织', value: 9 }
        ],
        approverLevelList: [
          { label: '自下而上（以发起人的直接部门负责人为第一级）', value: 2 },
          {
            label: '自上而下（以发起人所在的组织层级中最高部门负责人为第一级）',
            value: 1
          }
        ],
        approverParentLevelList: [
          { label: '自下而上（以发起人的直接上级为第一级）', value: 2 },
          {
            label: '自上而下（以发起人所在的组织层级中最高上级为第一级）',
            value: 1
          }
        ],
        approverDeptList: [],
        approverParentList: [],
        levelEnd: [],
        relationDepts: [],
        deptProps: {
          value: 'orgId',
          label: 'orgName'
        },
        relationLoading: false,
        isShowIcon: true
      };
    },
    computed: {
      approverPeople() {
        return (tag) => {
          const title = {
            people: ['成员', 'label'],
            dept: ['部门', 'title'],
            position: ['岗位', 'postName'],
            job: ['职务', 'jobName']
          };
          const [typeName, label] = title[tag.type];
          return `${typeName}: ${tag[label]}`;
        };
      }
    },
    watch: {
      dataLength: {
        handler() {
          const length = this.personList.length;
          if (this.dataLength > 1 && length === 9) {
            this.personList.splice(7, 1);
            this.isShowIcon = false;
          } else if (this.dataLength === 1 && length === 8) {
            this.isShowIcon = true;
            this.personList.splice(7, 0, { label: '连续多级上级', value: 8 });
          }
        },
        immediate: true
      },
      form: {
        handler(val) {
          this.$emit('get-one-person', { val: val, index: this.index });
        },
        deep: true
      },
      index: {
        immediate: true,
        handler() {
          if (Object.keys(this.data).length !== 0 && this.data.type) {
            const {
              type,
              dataId,
              scopeIds,
              startLevelType,
              level,
              scopes,
              scopesType,
              orgId
            } = this.data;
            this.form = {
              type,
              dataId,
              scopeIds: scopeIds || [],
              startLevelType,
              level,
              scopes: scopes || '2',
              scopesType: scopesType || '1',
              orgId
            };
            this.changeDeptLevel(this.data);
            let obj = { 5: 'careerList', 6: 'jobList' };
            if ([1, 9].includes(this.data.type)) {
              this.peopleIds = this.data.scopeIds || [];
              this.peopleList = this.data.scopeIds || [];
            } else if (this.data.type === 2) {
              this.staffIds = this.data.dataId;
              this.staffList = this.data.dataId;
            } else if (this.data.type === 7) {
              this.defaultRoles = this.data.dataId.map((item) => item.id);
              this.roleList = this.data.dataId;
            } else if (Object.keys(obj).includes(this.data.type.toString())) {
              this[obj[this.form.type]] = this.data.dataId;
              this.ids = this.data.dataId.map((item) => item.id);
            }
          } else {
            this.form = { ...APPROVER_PERSON };
          }
        }
      }
    },
    created() {
      this.levelEnd = Array(20)
        .fill()
        .map((v, i) => {
          return {
            value: i + 1,
            label: `${arrDesgin.arrRevse[i]}上级`
          };
        });
      this.$store.dispatch('GetDictByCode', 'scopes').then((res) => {
        this.applyType = res;
      });
      this.$store.dispatch('GetDictByCode', 'scopesType').then((res) => {
        this.applyArea = res;
      });
      this.relationLoading = true;
      getLinkDepts()
        .then((res) => {
          this.relationLoading = false;
          this.relationDepts = res.data.data || [];
        })
        .catch(() => {
          this.relationLoading = false;
        });
    },
    methods: {
      deleteApprovelPerson() {
        this.$emit('delete-approvel', this.index);
      },
      // 选择成员
      openChooseStaffDialogVisible() {
        if (this.staffList.length) {
          this.staffIds = deepClone(this.staffList);
        }
        this.chooseStaffVisible = true;
      },
      openDialogVisible(type) {
        this.staffType = type;
        if (['1', '9'].includes(type)) {
          this.choosePeopleVisible = true;
          return;
        }
        this.staffTitle =
          type === '4' ? '岗位' : type === '2' ? '职务' : '成员';
        this.dialogVisible = true;
      },
      openRoleVisiable() {
        this.roleVisible = true;
        this.defaultRoles = this.roleList.map((item) => item.id);
      },
      getChooseList(data) {
        this.staffList = data;
        this.staffIds = deepClone(this.staffList);
        this.form.dataId = data;
      },
      getCheckList(data) {
        this.peopleList = data;
        this.peopleIds = data;
        this.form.scopeIds = data.map((d) => {
          return {
            dataType: d.dataType,
            id: d.id,
            label: d.label,
            type: d.type,
            userId: d.userId,
            title: d.title,
            postName: d.postName,
            jobName: d.jobName
          };
        });
      },
      handleDeleteStaff(index) {
        this.staffList.splice(index, 1);
      },
      handleChangeRadio(val) {
        if (val === 1) {
          this.isNullPorperties.type = 1;
        }
        this.form = {
          ...APPROVER_PERSON,
          type: val
        };
        this.staffList = [];
        this.careerList = [];
        this.jobList = [];
        this.peopleList = [];
        this.roleList = [];
      },
      changeDeptLevel(val) {
        let type = 0;
        if (Object.keys(val).length > 0) {
          type = val.startLevelType;
          this.form.level = val.level;
        } else {
          type = val;
          this.form.level = '';
        }
        let appName = '',
          appStr = '';
        if (this.form.type === 3) {
          appName = '部门负责人';
          appStr = 'approverDeptList';
        } else if (this.form.type === 4) {
          appName = '上级';
          appStr = 'approverParentList';
        }
        this[appStr] = Array(20)
          .fill()
          .map((v, i) => {
            return {
              value: i + 1,
              label: `${
                type === 1 ? arrDesgin.arrSort[i] : arrDesgin.arrRevse[i]
              }${appName}`
            };
          });
      },
      getRoles(data) {
        let arr = [];
        data.map((item) => {
          arr.push({
            name: item.roleName,
            id: item.id
          });
        });
        this.form.dataId = arr;
        this.roleList = arr;
      },
      getStaffData(data) {
        let obj = {
          1: 'peopleList',
          5: 'careerList',
          6: 'jobList'
        };
        if (Object.keys(obj).includes(this.form.type.toString())) {
          let arr = [];
          data.map((item) => {
            arr.push({
              name: item.label || item.title || item.jobName || item.postName,
              id: item.id
            });
          });
          this.form.dataId = arr;
          this[obj[this.form.type]] = arr;
          this.ids = arr.map((item) => item.id);
        }
      },
      handleDeletePerson(index, id, type) {
        this[type].splice(index, 1);
        let arr = this.form.dataId.filter((item) => item.id !== id);
        this.form.dataId = arr;
        this.ids = arr.map((i) => i.id);
      },
      handleOrgIdChange() {
        this.peopleList = [];
        this.peopleIds = [];
        this.form.scopeIds = [];
      }
    }
  };
</script>
<style lang="scss" scoped>
  .setting-approvel-person {
    margin: 6px 0 12px;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    .oa-approvel-person-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 12px;
      color: #333;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      background: #fafafa;
      border-bottom: 1px solid #d9d9d9;

      i {
        color: #8e8e8e;
        font-size: 16px;
        line-height: 16px;

        &:hover {
          color: #ff4648;
          cursor: pointer;
        }
      }
    }

    .oa-approvel-person-content {
      padding: 16px 24px;

      .oa-radio-list {
        position: relative;

        i {
          position: absolute;
          right: 152px;
          bottom: 9px;
          color: #c9c8c9;
          font-size: 16px;
        }
      }

      .dept-or-people {
        .oa-choose-staff-list {
          margin-top: 12px;
        }
      }

      // 指定成员的选择成员
      .oa-choose-staff,
      .oa-dept-level {
        .oa-choose-staff-title {
          margin-top: 12px;
          margin-bottom: 10px;
          color: #333;
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
        }

        .oa-choose-staff-list {
          i {
            width: 56px;
            height: 26px;
            margin-right: 12px;
            line-height: 26px;
            text-align: center;
            border: 1px dashed #999;
            border-radius: 16px;
            cursor: pointer;

            &:hover {
              color: #409eff;
              border-color: #409eff;
            }
          }

          .oa-choose-staff-tag {
            margin: 6px 12px 6px 0;
            margin-right: 12px;
            color: #333;
            background: #f5f5f5;
            border-color: #d9d9d9;
            border-radius: 16px;
          }
        }
      }

      // 指定层级
      .oa-dept-level {
        .oa-dept-level-title {
          margin-top: 12px;
          margin-bottom: 10px;
          color: #333;
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
        }
      }
    }
  }
</style>
