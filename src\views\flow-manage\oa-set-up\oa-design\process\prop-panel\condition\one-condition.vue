<template>
  <div>
    <h-select
      v-model="obj.form.condition"
      @change="conditionChange"
      :data-source="typeList"
      :props="typeProp"
      :clearable="false"
      size="small"
      class="condition-name"
      placeholder="请选择条件"
    ></h-select>
    <h-select
      v-if="relationList.length && obj.type !== 'selectPerson'"
      v-model="obj.form.relation"
      :data-source="relationList"
      :clearable="false"
      class="condition-relation"
      size="small"
      placeholder="请选择关系"
    ></h-select>
    <el-input
      v-if="
        ['inputMoney', 'inputNumber', 'computed', 'text'].includes(obj.type) ||
        !obj.type
      "
      v-model.trim="obj.form.value"
      placeholder="请输入"
      size="small"
      type="number"
    ></el-input>
    <h-select
      v-else-if="['checkbox'].includes(obj.type)"
      v-model="obj.form.value"
      :clearable="false"
      :data-source="valueList"
      placeholder="请选择"
      no-data-text="暂无数据"
      multiple
    ></h-select>
    <h-select
      v-else-if="['radio'].includes(obj.type)"
      v-model="obj.form.value"
      :clearable="false"
      :data-source="valueList"
      placeholder="请选择"
      no-data-text="暂无数据"
    ></h-select>

    <el-input
      v-else-if="isStaff"
      v-model="staffValue"
      @focus="toChoosePerson"
      size="small"
      placeholder="请选择具体人员/角色/部门"
      suffix-icon="el-icon-arrow-down"
    >
    </el-input>
    <multiple-staff-tree
      v-if="chooseAdminVisible"
      @rangeSave="getCheckList"
      :range-visible.sync="chooseAdminVisible"
      :ids="ids"
    ></multiple-staff-tree>
  </div>
</template>

<script>
  import { deepClone } from '@/util/util.js';
  import { getRuleList } from '@/api/desk/flow';
  import multipleStaffTree from '../components/multiple-staff';
  const getAllNode = (list) => {
    let temp = [];
    const getNode = (array) => {
      array.forEach((item) => {
        temp.push(item);
        if (item.children && item.children.length) {
          getNode(item.children);
        }
      });
    };
    getNode(list);
    return temp;
  };
  export default {
    components: {
      multipleStaffTree
    },
    props: {
      oneList: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        obj: {
          type: '',
          name: '',
          form: {
            condition: '',
            relation: '',
            value: ''
          }
        },
        formJson: [],
        typeList: [],
        typeProp: {
          value: 'id',
          label: 'name'
        },
        relationList: [],
        valueList: [],
        isStaff: true,
        chooseAdminVisible: false,
        ids: [],
        staffValue: '',
        copyStaffList: [],
        ruleList: []
      };
    },
    computed: {
      formDesignList() {
        return this.$store.state.oaSetUp.formDesignList;
      }
    },
    watch: {
      obj: {
        handler(val) {
          let oVal = deepClone(val);
          if (oVal.type === 'radio' && oVal.form.relation === '') {
            oVal.form.relation = 'radioEq';
          }
          this.$emit('get-one-condition', oVal);
        },
        deep: true
      },
      oneList: {
        immediate: true,
        handler(val) {
          this.formJson = this.formDesignList;
          this.typeFilter();
          if (val.type) {
            if (val.type === 'selectPerson') {
              this.obj = {
                type: val.type,
                name: val.name,
                form: {
                  condition: val.form.condition,
                  relation: val.form.relation,
                  value: val.form.value
                }
              };
              let arr = [];
              let ids = [];
              if (val.form.value.length) {
                let type = ['people', 'dept', 'position', 'job'];
                let tLists = [
                  'startUserId',
                  'startDeptId',
                  'startPosId',
                  'startJobId'
                ];
                let obj = {
                  startUserId: 'label',
                  startDeptId: 'title',
                  startPosId: 'postName',
                  startJobId: 'jobName'
                };
                val.form.value.map((item) => {
                  item.valueList.map((res) => {
                    arr.push(res.name);
                    ids.push({
                      dataType:
                        type[tLists.indexOf(item.type)] === 'people' ? 3 : 1,
                      id: res.id,
                      type: type[tLists.indexOf(item.type)],
                      name: res.name
                    });
                  });
                  ids.map((l) => {
                    l[obj[item.type]] = l.name;
                    delete l.name;
                  });
                });
              }
              this.ids = ids;
              this.staffValue = arr.join('/');
            } else {
              let objConditon = this.typeList.filter(
                (item) => item.id === val.form.condition
              )[0];
              if (objConditon.id === 'rest1') {
                this.valueList = this.ruleList;
              }
              if (objConditon) {
                this.changeRelation(objConditon.type, objConditon.valueList);
              }
              this.obj = {
                type: val.type,
                name: val.name,
                form: {
                  condition: val.form.condition,
                  relation: val.form.relation,
                  value: val.form.value
                }
              };
            }
          } else {
            this.staffValue = '';
            this.obj = {
              type: '',
              name: '',
              form: {
                condition: '',
                relation: '',
                value: ''
              }
            };
          }
        },
        deep: true
      },
      formDesignList(val) {
        this.formJson = val;
        this.typeFilter();
      }
    },
    created() {
      // this.getRule(); // 20221025
    },
    methods: {
      typeFilter() {
        let tList = this.formJson.filter((l) => {
          return (
            ([
              'checkbox',
              'radio',
              'inputMoney',
              'inputNumber',
              'computed'
            ].includes(l.type) &&
              l.valueJson.required) ||
            ['rest', 'work', 'out', 'trip'].includes(l.type)
          );
        });
        let arr = [];
        let rList = this.ruleList;
        tList.forEach((item) => {
          // rest4、work4、out3、trip8
          let types = ['rest1', 'rest4', 'work4', 'out3', 'trip8'];
          if (['rest', 'work', 'out', 'trip'].includes(item.type)) {
            let list = getAllNode(item.children);
            let oneObj = list.filter((i) => types.includes(i.id));
            if (oneObj) {
              oneObj.forEach(({ type, id, valueJson }) => {
                arr.push({
                  type,
                  id,
                  name: valueJson.name || '',
                  valueList: id === 'rest1' ? rList : ''
                });
              });
            }
          } else {
            arr.push({
              type: item.type || '',
              id: item.id || '',
              name: item.valueJson.name || '',
              valueList: item.valueJson.options || []
            });
          }
        });
        arr.unshift({
          type: 'selectPerson',
          id: 'startUser',
          name: '发起人'
        });
        this.typeList = [...arr];
      },
      getRule() {
        getRuleList().then((res) => {
          let { data } = res.data;
          data.map((item) => {
            this.ruleList.push({ value: item.ruleName });
          });
        });
      },
      conditionChange(con) {
        // 根据选中的value去typeList中找对应的项
        let relation = {};
        if (con === 'startUser') {
          relation = {
            type: 'selectPerson',
            valueList: []
          };
          this.isStaff = true;
          this.obj.name = '发起人';
        } else {
          relation = this.typeList.filter(
            (res) => res.id === this.obj.form.condition
          )[0];
          this.isStaff = false;
          this.obj.name = relation.name;
        }
        this.obj.type = relation.type;
        this.obj.form.relation = '';
        this.obj.form.value = '';
        // 重新计算关系选择框中的下拉项
        this.changeRelation(relation.type, relation.valueList);
      },
      changeRelation(type, valueList) {
        if (['inputMoney', 'inputNumber', 'computed', 'text'].includes(type)) {
          this.relationList = [
            { value: '>=', label: '大于等于' },
            { value: '>', label: '大于' },
            { value: '==', label: '等于' },
            { value: '<=', label: '小于等于' },
            { value: '<', label: '小于' }
          ];
        } else if (['checkbox', 'radio'].includes(type)) {
          if (type === 'checkbox') {
            this.relationList = [
              { value: 'multipleAll', label: '同时选中' },
              { value: 'multipleAny', label: '选中任意' }
            ];
          } else if (type === 'radio') {
            this.relationList = [];
          }
          if (valueList.length) {
            let arr = [];
            valueList.map((item) => {
              if (item.value) {
                arr.push({ value: item.value, label: item.value });
              }
            });
            this.valueList = deepClone(arr);
          }
        } else if (['selectPerson'].includes(type)) {
          this.relationList = [];
        }
      },
      toChoosePerson() {
        if (this.staffValue === '') {
          this.ids = [];
        }
        if (this.copyStaffList.length) {
          this.ids = deepClone(this.copyStaffList);
        }
        this.chooseAdminVisible = true;
      },
      getCheckList(data) {
        this.copyStaffList = data;
        this.staffValue = data
          .map(
            (item) => item.label || item.title || item.postName || item.jobName
          )
          .join('/');
        this.obj.form.relation = 'multipleAny';
        // startUserId成员 startDeptId部门 startPosId岗位 startJobId职务
        let arr = ['people', 'dept', 'position', 'job'];
        const obj = [
          { type: 'startUserId', valueList: [] },
          { type: 'startDeptId', valueList: [] },
          { type: 'startPosId', valueList: [] },
          { type: 'startJobId', valueList: [] }
        ];
        data.map((item) => {
          if (arr.includes(item.type)) {
            obj[arr.indexOf(item.type)].valueList.push({
              id: item.id,
              name: item.label || item.title || item.postName || item.jobName
            });
          }
        });
        const values = obj.filter((l) => l.valueList.length);
        this.obj.form.value = values;
        this.obj.form.condition = 'startUser';
      }
    }
  };
</script>
<style lang="scss" scoped>
  .oa-approvel-condition-content {
    padding: 16px 24px;

    .oa-condition-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 15px;
      margin-bottom: 16px;

      .oa-condition-and-hr {
        color: #333;
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
      }

      i {
        color: #8e8e8e;
        font-size: 16px;
        line-height: 16px;
      }
    }

    .oa-condition-and-top {
      margin-top: 0;
    }

    .oa-condition-choose-staff {
      width: 100%;
      margin-top: 12px;
    }

    .condition-name,
    .condition-relation {
      margin-bottom: 12px;
    }
  }
</style>
