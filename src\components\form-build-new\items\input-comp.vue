<template>
  <el-input
    v-model="hValue"
    :maxlength="data.maxLength"
    show-word-limit
    :placeholder="data.placeholder"
    clearable
  />
</template>

<script>
  export default {
    name: 'InputComp',
    props: {
      value: {
        type: String,
        default: ''
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    }
  };
</script>
