<template>
  <div class="personalSettingsCreateOrganization">
    <div @click="createOrg" class="create">
      <header>
        创建组织
        <img
          src="https://attila-static.oss-cn-beijing.aliyuncs.com/web/portal/infomationArrow.png"
        />
      </header>
      <div>
        创新新组织后，你将成为主管理员，可用管理组织架构、添加成员，使用本系统提供企业管理应用。
      </div>
    </div>
    <div>
      <header>加入组织</header>
      <div>
        如果你的组织（企业/团队）已经在使用本系统，请联系管理员获取加入企业的二维码或链接加入。
      </div>
    </div>
    <create-organization
      :dialog-visible.sync="dialogVisible"
      :is-child-btn="false"
    ></create-organization>
  </div>
</template>
<script>
  import createOrganization from '@/components/create-organization';

  export default {
    components: { createOrganization },
    data() {
      return {
        dialogVisible: false
      };
    },
    methods: {
      createOrg() {
        this.dialogVisible = true;
      }
    }
  };
</script>
<style lang="scss">
  .personalSettingsCreateOrganization {
    display: flex;
    flex-direction: column;
    align-items: center;
    @keyframes mymove {
      form {
        margin-left: 16px;
      }

      to {
        margin-left: 22px;
      }
    }

    .create {
      background-image: url('https://attila-static.oss-cn-beijing.aliyuncs.com/web/oa/create-group.png');
      cursor: pointer;

      &:hover {
        border-color: #409eff;

        img {
          animation: mymove 0.5s;
          animation-timing-function: linear;
          animation-fill-mode: forwards;
        }
      }
    }

    & > div {
      box-sizing: border-box;
      width: 500px;
      margin-top: 24px;
      padding: 31px 32px;
      background-image: url('https://attila-static.oss-cn-beijing.aliyuncs.com/web/oa/join-group.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border: 2px solid #fff;
      border-radius: 4px;

      header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        color: #409eff;
        font-size: 20px;

        img {
          width: 30px;
          height: 6px;
          margin-left: 16px;
        }
      }

      div {
        width: 240px;
        color: #666;
        line-height: 22px;
      }
    }
  }
</style>
