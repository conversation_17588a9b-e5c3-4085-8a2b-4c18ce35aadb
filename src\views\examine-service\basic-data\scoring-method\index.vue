<template>
  <basic-container>
    <div v-show="!open">
      <search @query="queryFn" ref="search" />
      <div>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          :disabled="multiple.length === 0"
          type="danger"
          size="small"
          icon="el-icon-delete"
          @click="handleMultipleDelete"
          >删除</el-button
        >
      </div>
      <table-list :source="source" @dispatch="dispatch" />
      <yk-pagination
        small
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.current"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />
    </div>
    <transition-group name="fade">
      <yk-local-model :key="2" :title="modelTitle" :open="open" @close="close">
        <detail v-if="status" :id="id" @close="close" />
        <form-list v-else :id="id" @close="close" @update="updateFn" />
      </yk-local-model>
    </transition-group>
  </basic-container>
</template>

<script>
  import { Search, TableList, FormList, Detail } from './components';
  import { getList, postDelete } from '@/api/examine/scoring-method';
  import { mapMutations } from 'vuex';

  // 打分方式
  export default {
    name: 'ScoringMethod',
    components: { Search, TableList, FormList, Detail },
    data() {
      return {
        total: 0,
        queryParams: {
          current: 1,
          size: 10
        },
        open: false,
        status: false,
        modelTitle: '',
        id: '',
        source: [],
        multiple: []
      };
    },
    methods: {
      ...mapMutations(['SOURCE_CLEAR']),
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 查询
      queryFn(params) {
        Object.assign(this.queryParams, params, {
          current: 1,
          size: 10
        });
        this.request();
      },
      // api接口查询
      async request() {
        try {
          const res = await getList(this.queryParams);
          const { records, total } = res.data.data;
          this.source = records;
          this.total = total;
        } catch (e) {
          console.error(e);
        }
      },
      // 编辑，详情关闭
      close(bool) {
        this.open = bool;
        this.id = '';
        this.modelTitle = '';
        this.status = false;
        this.SOURCE_CLEAR();
      },
      // 更新列表
      updateFn() {
        this.$refs.search.onReset();
      },
      // 表格操作
      dispatch(type, row) {
        switch (type) {
          case 'detail':
            return this.getDetail(row);
          case 'edit':
            return this.handleEdit(row);
          case 'delete':
            return this.handleDelete(row);
          case 'multiple':
            return this.handleMultiple(row);
          default:
            return false;
        }
      },
      // 新增
      handleAdd() {
        this.open = true;
        this.id = '';
        this.status = false;
        this.modelTitle = '新增';
      },
      // 编辑
      handleEdit({ id }) {
        this.open = true;
        this.id = id;
        this.status = false;
        this.modelTitle = '编辑';
      },
      // 删除
      async handleDelete(row) {
        try {
          const res = await postDelete({
            codeList: [row.code]
          });
          const code = res.data.code;
          if (code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            await this.request();
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 获取详情
      getDetail({ id }) {
        this.open = true;
        this.id = id;
        this.status = true;
        this.modelTitle = '详情';
      },
      // 列表多选
      handleMultiple(ids = []) {
        this.multiple = ids;
      },
      // 多选删除
      handleMultipleDelete() {
        this.$confirm('此将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              const res = await postDelete({
                codeList: this.multiple.map((item) => item.code)
              });
              const code = res.data.code;
              if (code === 200) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                });
                await this.request();
              }
            } catch (e) {
              console.error(e);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      }
    }
  };
</script>
