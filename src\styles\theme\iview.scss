.theme-iview {
  .avue-logo{
    text-align: center;
    background: #001529;
    box-shadow: none;

    .avue-logo_title{
      display: inline;
      padding: 5px 8px 8px;
      color: #fff;
      font-weight: 500;
      font-size: 20px;
      background-color: #409EFF;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 3px;
      /* stylelint-disable-next-line declaration-block-no-redundant-longhand-properties */
      border-bottom-left-radius: 3px;
    }
  }

  .avue-tags{
    padding: 3px 5px 5px 0;
    background: #f0f0f0;
    box-shadow: inset 0 0 3px 2px hsla(0deg,0%,39.2%,10%);

    .is-active{
      &::before{
        background: #409EFF !important;
      }
    }

    .el-tabs__item{
      position: relative;
      height: 32px !important;
      padding: 0 15px !important;
      color: #515a6e!important;
      line-height: 32px !important;
      background: #fff!important;
      border: 1px solid #e8eaec!important;
      border-radius: 3px;

      &::before{
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 10px;
        background: #e8eaec;
        border-radius: 50%;
        content: '';
      }
    }
  }

  .avue-sidebar{
    background: #001529;

    .el-menu-item{
      &.is-active {
          background-color: #000c17;

          &::before {
            display: none;
          }

          i,span{
            color: #409EFF;
        }
      }
    }

     .el-submenu{
        .el-menu-item{
          &.is-active {
            background-color: #409EFF;

            &::before {
              display: none;
            }

            i,span{
              color: #fff;
          }
        }
      }
    }
  }
}