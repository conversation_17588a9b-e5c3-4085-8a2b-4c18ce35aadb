<template>
  <div>
    <el-dialog
      width="600px"
      append-to-body
      :visible.sync="open"
      title="消息详情"
      @close="open = false"
    >
      <div class="msg_detail_wrapper" v-loading="loading">
        <div class="title">{{ detailData.title }}</div>
        <div class="desc">
          <div class="sender">发布人: {{ detailData.sender }}</div>
          <div class="time">发布时间: {{ detailData.scheduleTime }}</div>
        </div>
        <div class="content">{{ detailData.content }}</div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { getDetail, postMsgRead } from '@/api/message/message';

  export default {
    name: 'MessageDetail',
    props: {
      formVisible: {
        type: Boolean,
        default: false
      },
      msgId: {
        type: String,
        require: true
      },
      hasRead: {
        type: Boolean,
        default: false
      },
      hasReadId: {
        type: String,
        require: true
      }
    },
    watch: {
      msgId: {
        handler(val) {
          val && this.getDetail();
        },
        immediate: true
      }
    },
    data() {
      return {
        // 表单加载
        loading: false,
        detailData: {}
      };
    },
    methods: {
      // 获取订单详情
      async getDetail() {
        try {
          this.loading = true;
          const { data = {} } = await getDetail(this.msgId);
          if (data.success) {
            this.detailData = data.data;
            // 如果消息未读,把消息状态更改为已读
            if (!this.hasRead) {
              await postMsgRead({ id: this.hasReadId });
            }
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      }
    },
    computed: {
      open: {
        get() {
          return this.formVisible;
        },
        set() {
          this.$emit('close');
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .msg_detail_wrapper {
    color: #333;

    .title {
      font-weight: bold;
      font-size: 18px;
      text-align: center;
    }

    .desc {
      display: flex;
      justify-content: flex-end;
      margin: 18px 0;
      font-size: 12px;

      .sender {
        margin-right: 15px;
      }
    }

    .content {
      font-size: 16px;
      line-height: 2em;
      text-indent: 32px;
    }
  }
</style>
