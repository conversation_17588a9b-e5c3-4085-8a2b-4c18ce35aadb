<template>
  <el-dialog
    :title="title"
    width="400px"
    :visible.sync="visited"
    append-to-body
    :close-on-click-modal="false"
  >
    <div style="height: 460px; overflow: auto" v-if="visited">
      <el-tree
        v-loading="treeLoading"
        :data="list"
        :check-strictly="true"
        show-checkbox
        node-key="id"
        ref="tree"
        :default-expanded-keys="defaultExpandedKeys"
        highlight-current
        :props="defaultProps"
        @check-change="handleCheckChange"
      ></el-tree>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visited = false" size="small">返 回</el-button>
      <el-button type="primary" size="small" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getDeptTree } from '@/api/system/dept';
  import website from '@/config/website';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'deptCom',
    data() {
      return {
        treeLoading: false,
        list: [],
        defaultProps: {
          children: 'children',
          label: 'title'
        },
        defaultExpandedKeys: [],
        deptIds: [],
        deptIdData: []
      };
    },
    props: {
      title: String,
      open: {
        type: Boolean,
        default: false
      },
      rowData: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    watch: {
      rowData: {
        handler(data) {
          if (this.visited && data.assessedDeptList) {
            this.deptIds = data.assessedDeptList.map((item) => item.id);
            this.deptIdData = data.assessedDeptList.map(
              ({ id, name: title }) => ({ id, title })
            );
          }
        },
        deep: true
      },
      open(val) {
        val && this.getDeptListData();
      }
    },
    methods: {
      // 递归查找下属机构
      getSubDept(deptData, id) {
        const queue = [...deptData];
        while (queue.length) {
          const o = queue.shift();
          if (o.id == id) return [o];
          queue.push(...(o.children || []));
        }
      },
      // 获取机构数据
      async getDeptListData() {
        try {
          this.treeLoading = true;
          const res = await getDeptTree(website.tenantId);
          const data = res.data.data;
          this.list = data;
          this.defaultExpandedKeys = cloneDeep(this.deptIds);
          this.$refs.tree && this.$refs.tree.setCheckedKeys(this.deptIds || []);
        } catch (e) {
          console.error(e);
        } finally {
          this.treeLoading = false;
        }
      },
      // 选中
      handleCheckChange() {
        let res = this.$refs.tree.getCheckedNodes(false, true);
        let arr = [];
        res.forEach((item) => {
          arr.push(item);
        });
        this.deptIdData = arr;
      },
      onSubmit() {
        // 判断是否选择被评价人
        if (!this.deptIdData.length) {
          return this.$message.warning('请选择至少一个机构');
        }
        // 根据选择被评价人筛选机构及岗位
        this.$emit('deptSubmit', this.deptIdData);
        this.visited = false;
      }
    },
    computed: {
      visited: {
        set(bool) {
          this.deptIds = [];
          this.deptIdData = [];
          this.$emit('close', bool);
        },
        get() {
          return this.open;
        }
      }
    }
  };
</script>
