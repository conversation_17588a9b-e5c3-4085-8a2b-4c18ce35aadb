import request from '@/router/axios';

// 模板列表树查询
export const listTree = (type, allStatus) => {
  return request({
    url: '/api/attila-resource/contractModel/list-tree',
    method: 'get',
    params: { type, allStatus }
  });
};
// 列表更多查询
export const listMore = (type, category) => {
  return request({
    url: '/api/attila-resource/contractModel/list-more',
    method: 'get',
    params: { type, category }
  });
};
// 保存合同模板
export const saveModel = (data) => {
  return request({
    url: '/api/attila-resource/contractModel/save-model',
    method: 'post',
    data
  });
};
// 删除合同模板
export const removeModel = (id) => {
  return request({
    url: '/api/attila-resource/contractModel/remove-model',
    method: 'post',
    data: { id }
  });
};
// 保存合同附件
export const saveContractFile = (fileType, fileName, fileUrl, contractId) => {
  return request({
    url: '/api/attila-resource/contractModel/save-contract-file',
    method: 'post',
    data: { fileType, fileName, fileUrl, contractId }
  });
};
