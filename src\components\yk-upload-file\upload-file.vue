<template>
  <div>
    <el-upload
      ref="upload"
      accept="multipart/form-data"
      :action="env + action"
      :multiple="multiple"
      :list-type="listType"
      :file-list="fileList"
      :disabled="disabled"
      :limit="limit"
      :auto-upload="autoUpload"
      :before-upload="beforeUpload"
      :before-remove="beforeRemove"
      :on-preview="onPreview"
      :on-remove="onRemove"
      :on-success="onSuccess"
      :on-error="onError"
      :on-exceed="onExceed"
      :headers="headers"
      :http-request="httpRequestFn"
    >
      <el-button :type="btnType" icon="el-icon-upload" size="mini">{{
        btnTitle
      }}</el-button>
      <div slot="tip" v-if="!onlyButton" class="text">
        上传文件大小不超过{{ fileSize }}M
      </div>
    </el-upload>

    <div class="img_fullScreen" v-show="false">
      <el-image
        style="height: 100%"
        ref="image"
        :src="imageUrl"
        :preview-src-list="srcList"
      >
        <span slot="placeholder" class="loading">加载中...</span>
        <span slot="error">图片加载失败!</span>
      </el-image>
    </div>
  </div>
</template>

<script>
  import { Base64 } from 'js-base64';
  import { mapGetters } from 'vuex';
  import {
    strFilter,
    pdfFilter,
    fileFilter,
    compressFilter,
    videoFilter
  } from '@/constant/common';
  import { downloadFile, downloadUrl } from '@/util/download';
  import website from '@/config/website';

  export default {
    name: 'UploadFile',
    props: {
      // 按钮类型
      btnType: {
        type: String,
        default: 'primary'
      },
      // 按钮文案
      btnTitle: {
        type: String,
        default: '点击上传'
      },
      // 上传描述
      describe: {
        type: String,
        default: ''
      },
      // 编辑回填
      files: {
        type: Array,
        default() {
          return [];
        }
      },
      // 是否隐藏描述文案
      onlyButton: {
        type: Boolean,
        default: false
      },
      // 上传地址
      action: {
        type: String,
        default: '/szyk-resource/oss/endpoint/put-file-attach'
      },
      // 文件列表类型
      listType: {
        type: String,
        default: 'text'
      },
      // 是否多选
      multiple: {
        type: Boolean,
        default: true
      },
      // 最大允许上传个数（默认10）
      limit: {
        type: Number,
        default: 10
      },
      // 是否禁用
      disabled: {
        type: Boolean,
        default: false
      },
      // 文件大小
      fileSize: {
        type: Number,
        default: 20
      },
      // 选取文件后立即进行上传
      autoUpload: {
        type: Boolean,
        default: false
      },
      // 上传之前的处理回调
      beforeUploadFn: {
        type: Function,
        default: () => {
          return true;
        }
      },
      beforeRemoveFn: {
        type: Function,
        default: () => {
          return true;
        }
      }
    },
    data() {
      return {
        env: '/api',
        fileList: [],
        visited: false,
        imageUrl: '',
        imgName: '',
        srcList: [],
        msgList: [],
        sizeList: []
      };
    },
    watch: {
      files: {
        handler(arr) {
          if (arr.length !== 0) {
            setTimeout(() => {
              arr.forEach((item) => {
                if (item.originalName) item.name = item.originalName;
              });
              this.fileList = arr;
            }, 500);
          } else {
            this.fileList = [];
          }
        },
        deep: true,
        immediate: true
      },
      msgList: {
        handler(newVal) {
          if (newVal.length) {
            this.msgGo();
          }
        },
        deep: true
      },
      sizeList: {
        handler(newVal) {
          if (newVal.length) {
            this.sizeGo();
          }
        },
        deep: true
      }
    },
    computed: {
      ...mapGetters(['token']),
      headers() {
        return {
          'Szyk-Auth': 'Bearer ' + this.token,
          Authorization: `Basic ${Base64.encode(
            `${website.clientId}:${website.clientSecret}`
          )}`
        };
      }
    },
    methods: {
      httpRequestFn(args) {
        console.log(args);
        this.$refs.upload.submit();
      },
      msgGo() {
        const self = this;
        this.$message({
          message: `${self.msgList[0].name}上传文件格式不支持 !`,
          type: 'error',
          onClose() {
            self.msgList.shift();
          }
        });
      },
      sizeGo() {
        const self = this;
        this.$message({
          message: `上传文件大小不能超过 ${this.fileSize} 'MB!`,
          type: 'error',
          onClose() {
            self.sizeList.shift();
          }
        });
      },
      // 上传文件之前的钩子
      beforeUpload(file) {
        let fileExt = file.name.replace(/.+\./, '');
        const allFileType = [
          ...strFilter,
          ...pdfFilter,
          ...fileFilter,
          ...compressFilter,
          ...videoFilter
        ];
        let formatLimit = allFileType.toString().split(',');
        let isTrueFile = formatLimit.indexOf(fileExt.toLowerCase()) !== -1;

        if (!isTrueFile) {
          this.msgList.push(file);
          return false;
        }

        const isSize = file.size / 1024 / 1024 < this.fileSize;
        if (!isSize) {
          this.sizeList.push(file);
          return false;
        }
        let propUp = this.beforeUploadFn(file);
        return isSize && propUp;
      },
      // 删除文件之前的钩子
      beforeRemove(file) {
        this.beforeRemoveFn(file);
      },
      // 点击文件列表中已上传的文件时的钩子
      onPreview(file) {
        const fileId = file.id;
        const name = file.name;
        const suffix = name.split('.').pop().toLowerCase();
        const isImg = strFilter.some((item) => item === suffix);
        // const isPdf = pdfFilter.some((item) => item === suffix);
        if (isImg) {
          // img预览
          this.openImg(file);
        } else {
          // 文件下载
          const url = downloadUrl('download', fileId);
          downloadFile(url);
        }
        // else if (isPdf) {
        //   // pdf预览
        //   // const url = downloadUrl('preview', fileId);
        //   // const combinUrl = url + `/${name}`;
        //   // window.open(combinUrl);
        // }
      },
      // 图片预览
      openImg(file) {
        // const fileId = file.id;
        const fileLink = file.link;
        this.visited = true;
        this.imgName = file.name;
        // this.imageUrl = downloadUrl('download', fileId);
        this.imageUrl = fileLink;
        this.srcList = [this.imageUrl];
        // 调用预览方法
        this.$nextTick(() => {
          this.$refs.image.clickHandler();
        });
      },
      // 文件列表移除文件时的钩子
      onRemove(file, fileList) {
        this.fileChange(fileList);
      },
      // 文件上传成功时的钩子
      onSuccess(res, file, fileList) {
        if (res.code === 200) {
          this.$message.success('文件上传成功！');
          this.fileChange(fileList);
        } else {
          this.onError(res);
        }
      },
      // 文件数量改变
      fileChange(fileList) {
        const fileArray = [];
        fileList.forEach((item) => {
          fileArray.push({
            name: item.name || item.response.data.originalName,
            id: item.id || item.response.data.attachId,
            link: item.link || item.response.data.link
          });
        });
        this.fileList = fileArray;
        const _filesArr = fileArray.map((item) => item.id);
        this.$emit('onInput', _filesArr, fileArray);
      },
      // 文件上传失败时的钩子
      onError(err, file) {
        this.$message.error(err.msg || `${file.name}上传失败`);
        this.$emit('onError', err);
      },
      // 文件状态改变时的钩子
      onExceed() {
        const title = `当前只能上传 ${this.limit} 个文件`;
        this.$message.warning(title);
      }
    }
  };
</script>

<style scoped>
  .text {
    color: #929292;
    font-size: 12px;
    letter-spacing: 0.5px;
  }

  .loading {
    font-size: 12px;
    line-height: 75px;
    text-align: center;
  }

  .img_fullScreen {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 240px);
    padding: 0 10px;
    text-align: center;
  }
</style>
