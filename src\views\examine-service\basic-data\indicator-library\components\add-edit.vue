<template>
  <el-dialog
    :title="title"
    :visible="true"
    width="520px"
    append-to-body
    :close-on-click-modal="false"
    @close="close"
  >
    <el-form
      :model="form"
      v-loading="loading"
      ref="form"
      :rules="rules"
      size="mini"
      label-width="80px"
    >
      <el-form-item label="指标名称" prop="indexName">
        <el-input
          v-model="form.indexName"
          type="text"
          placeholder="请输入指标名称"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="考核标准" prop="indexStandard">
        <el-input
          v-model="form.indexStandard"
          type="textarea"
          placeholder="请输入考核标准"
          style="width: 100%"
          maxlength="500"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit" size="small"> 确认 </el-button>
      <el-button @click="close" size="small"> 返回 </el-button>
    </div>
  </el-dialog>
</template>
<script>
  import {
    postEdit,
    getDetail,
    postAdd
  } from '@/api/examine/indicator-library';

  const rules = {
    indexName: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
    indexStandard: [
      { required: true, message: '请输入考核标准', trigger: 'blur' }
    ]
  };

  export default {
    props: {
      id: {
        type: String,
        default: ''
      },
      title: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        loading: false,
        form: {
          indexName: undefined, // 指标名称
          indexStandard: undefined // 考核标准
        },
        rules
      };
    },

    mounted() {
      if (this.id) {
        this.getDetail();
      }
    },
    methods: {
      // 获取详情
      async getDetail() {
        this.loading = true;
        try {
          const { data } = await getDetail(this.id);
          if (data.success) {
            this.form.indexName = data.data.indexName;
            this.form.indexStandard = data.data.indexStandard;
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 提交
      async submit() {
        try {
          await this.$refs.form.validate(async (valid) => {
            if (valid) {
              if (this.id) {
                this.form.id = this.id;
                const { data } = await postEdit(this.form);
                if (data.success) {
                  this.$message.success('修改指标成功');
                  this.$emit('close', true);
                }
              } else {
                const { data } = await postAdd(this.form);
                if (data.success) {
                  this.$message.success('新增指标成功');
                  this.$emit('close', true);
                }
              }
            }
          });
        } catch (e) {
          console.error(e);
        }
      },
      // 弹窗关闭
      close() {
        this.$emit('close', false);
      }
    }
  };
</script>
<style lang="scss" scoped>
  ::v-deep .el-form {
    width: 100%;
    margin: 0 auto;
  }
</style>
