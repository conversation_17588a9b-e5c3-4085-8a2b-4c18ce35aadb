import Vue from 'vue';
import Vuex from 'vuex';
import user from './modules/user';
import common from './modules/common';
import tags from './modules/tags';
import logs from './modules/logs';
import oaSetUp from './modules/oa-set-up';
import dict from './modules/dict';
import getters from './getters';
import oabadge from './modules/oabadge';
import scoringMethod from './modules/scoringMethod';
import commonComp from './modules/commonComp';

Vue.use(Vuex);
const store = new Vuex.Store({
  modules: {
    user,
    common,
    logs,
    oabadge,
    oaSetUp,
    tags,
    dict,
    scoringMethod,
    commonComp
  },
  getters
});

export default store;
