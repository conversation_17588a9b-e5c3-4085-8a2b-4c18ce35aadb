/**
 * v-hasPermi 操作权限处理
 *
 */

import store from '@/store';

export default {
  inserted(el, binding, vnode) {
    console.log('&&&&&&&&', vnode);
    const { value } = binding;
    const permission = store.getters && store.getters.permission;

    if (value && value instanceof Array && value.length > 0) {
      console.log(value);
      console.log(permission);
      const permissionFlag = value;

      const hasPermissions = permissionFlag.every((item) => {
        return Object.keys(permission).includes(item);
      });

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error(`请设置操作权限标签值`);
    }
  }
};
