<template>
  <div class="info_form_mb5">
    <el-form
      :model="form"
      size="small"
      ref="form"
      :inline="true"
      label-suffix=":"
      label-width="100px"
    >
      <el-row :gutter="12">
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="评价单编号" prop="code">
            <el-input
              placeholder="请输入评价单编号"
              v-model.trim="form.code"
              clearable
            />
          </el-form-item>
        </el-col>
        <!-- 2023/04/10  要求隐藏接收人列 -->
        <!-- <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="接收人" prop="assessorName">
            <el-input
              placeholder="请输入接收人"
              v-model.trim="form.assessorName"
              clearable
            />
          </el-form-item>
        </el-col> -->
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="考核周期" prop="periodItemId">
            <el-input
              class="disable_cursor"
              placeholder="请选择考核周期"
              :value="periodItemName"
              @click.native="periodDialogShow = true"
              style="width: 200px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="单据状态" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择单据状态"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type.evaluation_form_status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="截止日期" prop="deadline">
            <el-date-picker
              v-model="form.deadline"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择截止日期"
              style="width: 100%"
              :editable="false"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >清空</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <period-select
      v-if="periodDialogShow"
      :selectPeriodId="form.periodItemId"
      @dialogSave="handleDialogSave"
      @dialogClose="periodDialogShow = false"
    />
  </div>
</template>

<script>
  import { PeriodSelect } from '@/views/examine-service/components/period-select';
  import { scheme_type } from '@/constant/service';

  export default {
    name: 'search',
    components: { PeriodSelect },
    serviceDicts: ['evaluation_form_status'],
    data() {
      return {
        // 考核周期属性
        periodDialogShow: false,
        periodItemName: undefined,
        // 表单
        form: {
          code: undefined,
          assessorName: undefined,
          periodItemId: undefined,
          status: undefined,
          deadline: undefined,
          schemeType: scheme_type.kpi
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      // 接收选中的考核周期
      handleDialogSave(data) {
        this.periodDialogShow = false;
        this.form.periodItemId = data.id;
        this.periodItemName = data.itemName;
      },
      // 重置
      resetQuery() {
        this.periodItemName = undefined;
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        this.$emit('search', this.form);
      }
    }
  };
</script>
