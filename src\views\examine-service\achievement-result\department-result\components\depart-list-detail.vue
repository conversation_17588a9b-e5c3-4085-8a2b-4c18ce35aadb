<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="list"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column
        fixed
        type="index"
        label="序号"
        width="50"
        align="center"
      >
      </el-table-column>
      <el-table-column
        fixed
        label="部门名称"
        prop="deptName"
        width="200"
        align="center"
      >
      </el-table-column>
      <el-table-column label="经济效益(30%)" align="center">
        <el-table-column label="利润总额(50%)" align="center">
          <el-table-column
            label="原始分"
            prop="profitOriginalScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.profitOriginalScore || '---'
            }}</template>
          </el-table-column>
          <el-table-column
            label="折算分"
            prop="profitConvertScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.profitConvertScore || '---'
            }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="商品煤单位完全成本(50%)" align="center">
          <el-table-column
            label="原始分"
            prop="costOriginalScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.costOriginalScore || '---'
            }}</template>
          </el-table-column>
          <el-table-column
            label="折算分"
            prop="costConvertScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.costConvertScore || '---'
            }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="折算分小计"
          prop="economyConvertScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.economyConvertScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="KPI指标(40%)" align="center">
        <el-table-column
          label="原始分"
          prop="kpiOriginalScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.kpiOriginalScore || '---'
          }}</template>
        </el-table-column>
        <el-table-column
          label="折算分"
          prop="kpiConvertScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.kpiConvertScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="工作效能(30%)" align="center">
        <el-table-column
          label="原始分"
          prop="workOriginalScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.workOriginalScore || '---'
          }}</template>
        </el-table-column>
        <el-table-column
          label="折算分"
          prop="workConvertScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.workConvertScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="部门建设(扣分项)" align="center">
        <el-table-column
          label="原始分"
          prop="deptOriginalScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.deptOriginalScore || '---'
          }}</template>
        </el-table-column>
        <el-table-column
          label="扣分"
          prop="deptDeductScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.deptDeductScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="年度重点工作" align="center" width="150px">
        <template slot-scope="{ row }">{{
          row.annualTaskScore || '---'
        }}</template>
      </el-table-column>
      <el-table-column prop="systemCalculateScore" align="center" width="120px">
        <div slot="header">
          系统计算得分
          <el-tooltip
            class="item"
            effect="dark"
            content="系统计算得分=【经济效益】折算得分+【KPI指标】折算得分+【工作效能】折算得分-【部门建设】扣分+年度重点工作"
            placement="top"
            ><i class="el-icon-question"></i
          ></el-tooltip>
        </div>
        <template slot-scope="{ row }">{{
          row.systemCalculateScore || '---'
        }}</template>
      </el-table-column>
      <el-table-column
        label="本周期考核得分"
        width="160"
        prop="assessScore"
        align="center"
      >
        <template slot-scope="{ row }">{{
          row.assessScore | scoreFilter
        }}</template>
      </el-table-column>
      <el-table-column label="名次" width="50" align="center" prop="rank">
      </el-table-column>
      <el-table-column label="备注(点击查看)" align="center" prop="comment">
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleInput(scope.row)">
            {{ scope.row.comment || '---' }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      width="600px"
      title="备注"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input v-model.trim="tempInput" type="textarea" disabled :rows="12" />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  export default {
    name: 'depart-list-detail',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        inputVisited: false,
        tempInput: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      handleInput(row) {
        this.inputVisited = true;
        this.tempInput = row.comment;
      },
      // 完成情况弹窗关闭
      inputClose() {
        this.inputVisited = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
    cursor: pointer;
  }
</style>
