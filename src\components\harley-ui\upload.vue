<template>
  <div class="h-upload" :class="{ 'is-deleted': isDeleted }">
    <el-upload
      v-if="uploadable || againUpload"
      :action="action"
      :headers="hHeaders"
      :multiple="multiple"
      :show-file-list="false"
      :accept="accept"
      :list-type="listType"
      :before-upload="handleBeforeUpload"
      :on-success="handleOnSuccess"
      :on-error="handleOnError"
      :disabled="hDisabled"
      :drag="drag"
      :auto-upload="autoUpload"
      :on-change="onChange"
      class="h-upload-content"
    >
      <slot></slot>
      <slot name="trigger"></slot>
      <span v-if="showTips" slot="tip" class="h-upload-tips">{{ tip }}</span>
    </el-upload>
    <template v-else-if="!fileList.length && noTip">无</template>
    <template v-if="showFileList">
      <div
        v-for="({ url, icon, name, type }, index) in fileList"
        :key="url + index"
        class="file-item"
        :class="{ 'is-disabled': hDisabled }"
      >
        <el-image
          :src="encodeOSSUrl(url)"
          :preview-src-list="getPreviewList(url)"
          :class="`js_imgPreviewModel${key}${index} img-preview-model`"
        >
        </el-image>
        <img v-oss :src="icon" class="file-item-icon" />
        <span class="file-name">
          {{ name }}
        </span>
        <!-- <el-button @click="seeFile(index)" type="link">{{ name }}</el-button> -->
        <i class="el-icon-circle-check" />
        <i
          @click="handleOnRemove(index)"
          class="el-icon-circle-close file-list-close-btn"
        />

        <template v-if="!isDeleted">
          <!-- <span
            v-if="docViewJudgeFun(type) || imageType(type)"
            @click="seeFile(type, index, url, name)"
            class="el-button el-button--text"
          >
            查看
          </span> -->
          <span
            @click="resetDownLoadFunction(encodeOSSUrl(url), name)"
            class="el-button el-button--text"
          >
            下载
          </span>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
  import website from '@/config/website';
  import { Base64 } from 'js-base64';
  import {
    deepClone,
    getDocIcon,
    docViewJudge,
    encodeOSSUrl
  } from '@/util/util';
  import { resetRouter } from '@/router/router';
  import { getWpsViewUrl } from '@/api/resource/wps';

  export default {
    name: 'HUpload',
    inject: {
      elForm: {
        default: ''
      }
    },
    props: {
      value: [String, Array],
      drag: {
        type: Boolean,
        default: false
      },
      // default: '/api/attila-resource/oss/endpoint/put-file-by-original-name'
      action: {
        type: String,
        default: '/api/szyk-resource/oss/endpoint/put-file-attach'
      },
      headers: {
        type: Object,
        default() {
          return {};
        }
      },
      multiple: Boolean,
      limit: {
        type: Number,
        default: 30
      },
      showFileList: {
        type: Boolean,
        default: false
      },
      accept: {
        type: String,
        default:
          '.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .xls, .xlsx, .pdf, .ppt'
      },
      beforeUpload: Function,
      onSuccess: Function,
      onError: Function,
      onRemove: Function,
      listType: {
        type: String,
        validator(value) {
          return ['text', 'picture', 'picture-card'].indexOf(value) !== -1;
        }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      showLoading: {
        type: Boolean,
        default: false
      },
      againUpload: {
        type: Boolean,
        default: false
      },
      // 上传文件最大限制
      maxFileSize: {
        type: Number,
        default: 20
      },
      tip: String,
      // 判断文件列表没数据时是否显示无
      noTip: {
        type: Boolean,
        default: true
      },
      autoUpload: {
        type: Boolean,
        default: true
      },
      onChange: Function,
      resetDownLoadFunction: {
        type: Function,
        default: (url, fileName) => {
          // console.log('filename', fileName);
          window.open(`${url}?attname=${fileName}`);
        }
      },
      isDeleted: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: null,
        key: '',
        encodeOSSUrl
      };
    },
    computed: {
      hHeaders() {
        let headers = {
          Authorization: `Basic ${Base64.encode(
            `${website.clientId}:${website.clientSecret}`
          )}`,
          'Tenant-Id': this.$store.getters.tenantId,
          'User-Type': 'web',
          ...this.headers
        };
        headers[website.tokenHeader] = 'bearer ' + this.$store.getters.token;
        return headers;
      },
      hDisabled() {
        return this.disabled || (this.elForm || {}).disabled;
      },
      uploadable() {
        return (
          !this.hDisabled &&
          ((!this.multiple && !this.value) ||
            (this.multiple && this.value.length < this.limit))
        );
      },
      showTips() {
        return this.tip && this.uploadable;
      },
      fileList() {
        return this.getFile(this.value);
        // let list = [];
        // if (this.multiple) {
        //   list = [...this.value];
        // } else if (this.value) {
        //   list = [this.value];
        // }
        // const u = list.filter((l) => l);
        // return Array.from(u, (url) => {
        //   return this.getFile(url);
        // });
      },
      srcList() {
        return this.fileList
          .filter(({ type }) => this.imageType(type))
          .map(({ url }) => url);
      }
    },
    watch: {
      value(val) {
        if (this.$parent.$options.componentName === 'ElFormItem') {
          this.$parent.$emit('el.form.change', val);
        }
      }
    },
    created() {
      this.key = new Date().getTime();
    },
    methods: {
      // 添加文件预览按钮是否显示判定
      docViewJudgeFun(type) {
        return docViewJudge(type);
      },
      imageType(type) {
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(type);
      },
      // 查看文档
      seeFile(type, index, url, name) {
        if (this.imageType(type)) {
          let imgBox = document.getElementsByClassName(
            `js_imgPreviewModel${this.key}${index}`
          )[0];
          imgBox.getElementsByTagName('img')[0].click();
        } else {
          // let urlFarst = 'https://wps.atila.cn/onlinePreview?url=';
          // window.open(`${urlFarst}${url}`, '_blank');
          getWpsViewUrl(url).then((res) => {
            if (res.data && res.data.success) {
              if (res.data.data) {
                let routeUrl = this.$router.resolve({
                  name: 'documentDetailPreview',
                  query: {
                    url: res.data.data,
                    docName: name,
                    fileType: type,
                    fileUrl: url
                  }
                });
                window.open(routeUrl.href);
              } else {
                this.$message.warning('预览地址获取失败');
              }
            }
          });
        }
      },
      handleBeforeUpload(file) {
        let { name, size } = file;
        let typeList = this.accept
          .split(',')
          .map((item) => item.trim().toLowerCase().substr(1));
        // 文件类型校验
        let dotIndex = name.lastIndexOf('.');
        if (dotIndex === -1) {
          this.$message.error(
            `请上传正确格式的文件。支持格式：${typeList.join('/')}`
          );
          return false;
        } else {
          let suffix = name.substring(dotIndex + 1);
          if (typeList.indexOf(suffix.toLowerCase()) === -1) {
            this.$message.error(
              `请上传正确格式的文件。支持格式：${typeList.join('/')}`
            );
            return false;
          }
        }
        let result = true;
        if (this.multiple && this.value.length >= this.maxFileSize) {
          this.$message.error('文件数量超过上限');
          return false;
        }
        if (size > 1048576 * this.maxFileSize) {
          this.$message.error(`文件大小不能超过${this.maxFileSize}M！`);
          return false;
        }

        if (this.beforeUpload) {
          result = this.beforeUpload(file);
          if (result === false) {
            return false;
          }
        }
        if (this.showLoading) {
          this.loading = this.$loading({
            lock: true,
            text: '正在上传',
            spinner: 'el-icon-loading'
          });
        }
        if (result !== true) {
          return Promise.resolve(result);
        }
      },
      handleOnSuccess(response, file) {
        // console.log('response', response);
        // console.log('file', file);
        if (this.showLoading && this.loading) {
          this.loading.close();
          this.loading = null;
        }
        let value = response.data;
        if (this.multiple && this.value) {
          value = [...this.value, value];
        }
        this.$emit('input', value);
        if (this.onSuccess) {
          this.$nextTick(() => {
            this.onSuccess(response, file, this.fileList);
          });
        }
      },
      handleOnError(err, file) {
        if (this.showLoading && this.loading) {
          this.loading.close();
          this.loading = null;
        }
        if (err.status === 401) {
          this.$store.commit('SET_REFRESH_LOCK', true);
          // 如果是401则跳转到登录页面
          if (!this.$store.getters.reloginFlag) {
            this.$store.commit('SET_RELOGIN_FLAG', true);
            this.$confirm(
              '您尚未登录或者登录信息已失效，请重新登录',
              '请登录',
              {
                confirmButtonText: '前往登录'
              }
            )
              .then(() => {
                this.$store.dispatch('FedLogOut').then(() => {
                  resetRouter();
                  this.$router.push({ name: 'login' });
                });
              })
              .catch(() => {
                this.$store.commit('SET_RELOGIN_FLAG', false);
              });
          }
          return;
        }
        this.$message.error('系统错误，请稍后重试。');
        if (this.onError) {
          this.onError(err, file, this.fileList);
        }
      },
      handleOnRemove(index) {
        let file = deepClone(this.fileList[index]);
        let value;
        if (this.multiple) {
          value = [...this.value];
          value.splice(index, 1);
        } else {
          value = '';
        }
        this.$emit('input', value);
        if (this.onRemove) {
          this.$nextTick(() => {
            this.onRemove(file, this.fileList);
          });
        }
      },
      getFile(list) {
        list.forEach((item) => {
          item.name = item.originalName;
          item.type = item.type;
          item.url = item.fileUrl;
          item.icon = getDocIcon(item.type);
        });
        return list;
        // let name = '';
        // let type = '';
        // let icon = '';
        // let index = url.lastIndexOf('/');
        // if (index > -1) {
        //   name = url.substring(index + 1, url.length);
        // }
        // if (name.split('.').length > 2) {
        //   index = name.indexOf('.');
        //   if (index === 32) {
        //     name = name.substring(index + 1, name.length);
        //   }
        // }
        // index = name.lastIndexOf('.');
        // if (index > -1) {
        //   type = name.substring(index + 1, name.length);
        // }
        // icon = getDocIcon(type);
        // return { url, name, type, icon };
      },
      getPreviewList(url) {
        let list = [...this.srcList];
        let index = list.indexOf(url);
        if (index > -1) {
          let preList = list.splice(0, index) || [];
          list = list.concat(preList);
        }
        return list;
      }
    }
  };
</script>

<style lang="scss">
  @import '@/styles/element-ui';

  .h-upload {
    &.is-deleted {
      .file-name {
        text-decoration: line-through;
      }
    }

    .img-preview-model {
      width: 0;
      height: 0;
    }

    .h-upload-content {
      width: 100%;
      height: 100%;

      & + .file-item {
        margin-top: 4px;
      }

      .h-upload-tips {
        margin-left: 10px;
        color: #909399;
        font-size: 12px;
      }
    }

    .file-item {
      display: flex;
      align-items: center;
      padding: 12px 18px;
      line-height: 22px;
      background: #f7f8fa;

      .el-button--text {
        cursor: pointer;
      }

      .file-item-icon {
        height: 14px;
        margin-right: 8px;
      }

      span {
        flex: 1;
      }

      .el-icon-circle-check {
        color: #70cd44;
        font-size: 16px;
      }

      .file-list-close-btn {
        display: none;
        color: #a3a3a3;
        font-size: 16px;
        cursor: pointer;

        &:hover {
          color: #757575;
        }
      }

      .el-button {
        display: none;
        margin-left: 20px;
        padding: 0;
      }

      &:hover {
        .el-icon-circle-check {
          display: none;
        }

        .file-list-close-btn {
          display: inline-block;
        }
      }

      &.is-disabled {
        span {
          flex: unset;
        }

        .el-icon-circle-check,
        .file-list-close-btn {
          display: none;
        }

        &:hover {
          .el-icon-circle-check,
          .file-list-close-btn {
            display: none;
          }

          .el-button {
            display: inline-block;
          }
        }
      }

      & + .file-item {
        margin-top: 4px;
      }
    }
  }
</style>
