<template>
  <div class="wrapper" v-loading="loading">
    <el-collapse style="border: 0" v-model="collapse">
      <el-collapse-item name="one">
        <template slot="title">
          <h2 class="title">基础信息</h2>
        </template>
        <basic-info
          :form="basic"
          v-model="basic.evaluationDate"
          :is-detail="isDetail"
        />
      </el-collapse-item>
    </el-collapse>
    <section>
      <div class="title_wrapper">
        <h2 class="title">评价信息</h2>
        <!--        <el-tooltip effect="dark" content="全屏展示" placement="top-start">-->
        <!--          <i class="el-icon-full-screen icon-full" @click="visited = true"></i>-->
        <!--        </el-tooltip>-->
      </div>
      <el-alert
        v-if="!isDetail"
        style="margin-bottom: 15px"
        title="注意：请点击拖拽进行排名！"
        type="info"
        show-icon
        :closable="false"
      />
      <!--   详情   -->
      <evaluate-list-detail v-if="isDetail" :list="list" />
      <!--   评价   -->
      <evaluate-list
        v-else
        :key="1"
        :max-height="300"
        :list.sync="list"
        :maxScore="maxScore"
      />
      <div class="end-btn">
        <el-button
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => handleSave('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => handleSave('submit')"
          >提交</el-button
        >
      </div>
    </section>
  </div>
</template>

<script>
  import {
    getDetail,
    postSubmit,
    getMaxScore
  } from '@/api/examine/work-efficiency';
  import { BasicInfo, EvaluateList, EvaluateListDetail } from './components';
  import { formatTime } from '@/util/util';

  export default {
    name: 'evaluate',
    components: {
      BasicInfo,
      EvaluateList,
      EvaluateListDetail
    },
    props: {
      isDetail: {
        type: Boolean,
        required: true
      },
      isApproval: {
        type: Boolean,
        required: true
      },
      code: {
        type: String,
        default: ''
      }
    },
    computed: {
      title() {
        return this.isDetail ? '查看详情' : '评价详情';
      }
    },
    data() {
      return {
        // visited: false,
        loadingSave: false,
        loadingSubmit: false,
        disabled: false,
        loading: false,
        collapse: 'one',
        basic: {},
        list: [],
        maxScore: 0
      };
    },
    // 先获取最高分数, 再填充列表排名分数
    async created() {
      await this.getMaxScoreData();
      await this.init();
    },
    methods: {
      // 获取工作效能最高分
      async getMaxScoreData() {
        try {
          const params = { paramKey: 'business.work.maxScore' };
          const { data } = await getMaxScore(params);
          if (data.code === 200 && data.data) {
            this.maxScore = +data.data;
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 初始化
      async init() {
        try {
          this.loading = true;
          const res = await getDetail({
            evaluationCode: this.code
          });
          const data = res.data.data;
          this.basic = data;
          // 如果详情里没有填报日期, 置为当前时间
          if (!this.basic.evaluationDate) {
            this.basic.evaluationDate = new Date()
              .toLocaleString()
              .replaceAll('/', '-');
          }
          const list = data.resultList || [];
          // 如果当前评价单是已完成状态, 分值直接取后台返回分值, 否则按配置的最高分值递减排列
          this.list = list.map((item, i) => {
            item.rank = i + 1;
            item.score =
              data.status === '3'
                ? (+item.score).toFixed(0)
                : this.maxScore - i; // 每个人间隔多少分
            return item;
          });
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      handleSave(type) {
        const data = Object.assign({}, this.basic, {
          action: type,
          evaluationDate: formatTime(this.basic.evaluationDate),
          resultList: this.list
        });

        if (type === 'save') {
          this.save(data, '保存');
        }

        if (type === 'submit') {
          if (!this.basic.evaluationDate) {
            return this.$message.warning('评价日期未选择，请选择！');
          }
          this.save(data, '提交');
        }
      },
      // 保存-提交
      async save(data, msg) {
        try {
          this.disabled = true;
          this.loadingSave = true;
          await postSubmit(data);
          this.$message.success('数据' + msg + '成功！');
          this.$emit('refresh');
        } catch (e) {
          console.error(e);
        } finally {
          this.disabled = false;
          this.loadingSave = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;
    width: 100%;

    .icon-full {
      position: absolute;
      right: 10px;
      bottom: 7px;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
