<template>
  <div>
    <basic-container>
      <el-select v-model="form.sex" placeholder="请选择性别">
        <el-option
          v-for="dict in systemDicts.type.sex"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
      <el-select v-model="form.warehouse" placeholder="请选择仓库">
        <el-option
          v-for="dict in serviceDicts.type['001']"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
      {{ form.sex }}
      {{ form.warehouse }}
      <el-button
        type="primary"
        size="small"
        @click="handleForm()"
        icon="el-icon-plus"
        >add</el-button
      >
      <el-descriptions title="用户信息">
        <el-descriptions-item label="用户名">kooriookami</el-descriptions-item>
        <el-descriptions-item label="手机号">18100000000</el-descriptions-item>
        <el-descriptions-item label="居住地">苏州市</el-descriptions-item>
        <el-descriptions-item label="备注">
          <el-tag size="small">学校</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系地址"
          >江苏省苏州市吴中区吴中大道 1188 号</el-descriptions-item
        >
      </el-descriptions>
    </basic-container>
  </div>
</template>

<script>
  export default {
    name: 'wel',
    serviceDicts: ['001'],
    systemDicts: ['sex'],
    data() {
      return {
        form: {}
      };
    },
    computed: {},
    created() {},
    methods: {
      handleForm(id) {
        this.$router.push({
          path: '/dict-horizontal/index',
          query: {
            id: id
          }
        });
      }
    },
    mounted() {
      console.log('this', this);
    }
  };
</script>

<style scoped="scoped" lang="scss"></style>
