import hasRole from './permission/hasRole';
import hasPermi from './permission/hasPermi';
import oss from './oss';
import auth from './auth';

const install = function (Vue) {
  Vue.directive('hasRole', hasRole);
  Vue.directive('hasPermi', hasPermi);
  Vue.directive('oss', oss);
  Vue.directive('auth', auth);
};

if (window.Vue) {
  window['hasRole'] = hasRole;
  window['hasPermi'] = hasPermi;
  window['oss'] = oss;
  window['auth'] = auth;
  Vue.use(install); // eslint-disable-line
}

export default install;
