<template>
  <div>
    <basic-container>
      <el-select v-model="form.sex" placeholder="请选择性别">
        <el-option
          v-for="dict in dict.type.sex"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
      {{ form.sex }}
    </basic-container>
  </div>
</template>

<script>
  export default {
    name: 'wel',
    dicts: ['sex', 'notice'],
    data() {
      return {
        form: {}
      };
    },
    computed: {},
    created() {},
    methods: {},
    mounted() {
      console.log(this.dict);
    }
  };
</script>

<style scoped="scoped" lang="scss"></style>
