<template>
  <el-form ref="form" :model="queryParams" size="small" inline label-suffix=":">
    <el-form-item style="margin-bottom: 0" label="搜索关键词" prop="keyword">
      <el-input
        v-model="queryParams.keyword"
        placeholder="请输入关键词"
        clearable
      />
    </el-form-item>
    <el-form-item style="margin-bottom: 0" label="状态" prop="hasRead">
      <el-select
        v-model="queryParams.hasRead"
        placeholder="请选择状态"
        style="width: 100%"
        clearable
      >
        <el-option
          v-for="dict in statusDict"
          :key="dict.value"
          :label="dict.label"
          :value="+dict.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item style="margin-bottom: 0">
      <el-button
        type="primary"
        icon="el-icon-search"
        size="mini"
        @click="handleQuery"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >重置</el-button
      >
    </el-form-item>
  </el-form>
</template>
<script>
  export default {
    name: 'MessageManageSearch',
    data() {
      return {
        statusDict: [
          {
            value: 0,
            label: '未读'
          },
          {
            value: 1,
            label: '已读'
          }
        ],
        queryParams: {
          keyword: '',
          hasRead: undefined
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        this.$emit('search', this.queryParams);
      }
    }
  };
</script>
<style lang=""></style>
