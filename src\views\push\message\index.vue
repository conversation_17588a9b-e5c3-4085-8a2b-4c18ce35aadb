<template>
  <basic-container>
    <search @search="query" />
    <el-row>
      <el-col :span="24">
        <div class="tool-box">
          <el-button
            v-if="permission.message_add"
            type="primary"
            icon="el-icon-plus"
            size="small"
            @click="handleAdd"
            >新增</el-button
          >
        </div>
      </el-col>
    </el-row>
    <table-info :source="tableData" :loading="loading" @dispatch="dispatch" />
    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!-- 新增/编辑/详情 -->
    <add-edit
      :dialogTitle="dialogTitle"
      :formVisible="formVisible"
      :viewMode="viewMode"
      :msgId="msgId"
      @refresh="request"
      @close="handleDialogClose"
    />
  </basic-container>
</template>

<script>
  import { Search, TableInfo, AddEdit } from './components';
  import { getPage } from '@/api/message/message';
  import { mapGetters } from 'vuex';

  export default {
    name: 'Message_push',
    components: { Search, TableInfo, AddEdit },
    data() {
      return {
        // 查询条件
        queryParams: {
          size: 10,
          current: 1
        },
        // 表格加载中
        loading: false,
        // 列表条目总数量
        total: 0,
        // 列表数据
        tableData: [],
        // 是否显示
        formVisible: false,
        msgId: '',
        // 是否查看
        viewMode: false,
        // dialog标题
        dialogTitle: ''
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    methods: {
      // 查询
      query(params) {
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          params
        );
        this.request();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 请求列表数据
      async request() {
        try {
          this.loading = true;
          const res = await getPage(this.queryParams);
          const { total = 0, records = [] } = res.data.data;
          this.total = total;
          this.tableData = records;
          this.multiples = [];
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 列表操作
      dispatch(type, data) {
        switch (type) {
          case 'edit':
            return this.handleEdit(data);
          case 'view':
            return this.handleView(data);
          case 'refresh':
            return this.request();
          default:
            return false;
        }
      },
      handleAdd() {
        this.dialogTitle = '新增';
        this.formVisible = true;
        this.viewMode = false;
      },
      handleView(row) {
        this.dialogTitle = '查看';
        this.formVisible = true;
        this.viewMode = true;
        this.msgId = row.id;
      },
      handleEdit(row) {
        this.dialogTitle = '编辑';
        this.formVisible = true;
        this.viewMode = false;
        this.msgId = row.id;
      },
      handleDialogClose() {
        this.formVisible = false;
        this.msgId = '';
        this.viewMode = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }
</style>
