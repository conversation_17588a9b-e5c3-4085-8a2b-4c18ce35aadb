<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    :component-list="componentList"
    :accept="accept"
  />
</template>

<script>
  import { mapGetters } from 'vuex';

  export default {
    name: 'TurnFormalComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      accept: String
    },
    computed: {
      ...mapGetters(['userInfo']),
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    methods: {
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
