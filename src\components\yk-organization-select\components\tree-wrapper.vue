<template>
  <div class="box" ref="box">
    <div class="left" :class="leftVal" ref="left">
      <slot name="left"></slot>
    </div>
    <div class="resize" ref="resize" title="收缩侧边栏">⋮</div>
    <div class="mid" :class="midVal" ref="mid">
      <slot name="mid"></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TreeWrapper',
    props: {
      leftVal: {
        type: String,
        default: 'left_depart'
      },
      midVal: {
        type: String,
        default: 'mid_depart'
      }
    },
    mounted() {
      this.dragControllerDiv();
      window.addEventListener('resize', this.removeStyle);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.removeStyle);
    },
    methods: {
      removeStyle() {
        this.$refs.left.removeAttribute('style');
        this.$refs.mid.removeAttribute('style');
      },
      dragControllerDiv() {
        const resize = this.$refs.resize;
        const left = this.$refs.left;
        const mid = this.$refs.mid;
        const box = this.$refs.box;
        // 鼠标按下事件
        resize.onmousedown = function (e) {
          //颜色改变提醒
          resize.style.background = '#818181';
          const startX = e.clientX;
          resize.left = resize.offsetLeft;
          // 鼠标拖动事件
          document.onmousemove = function (e) {
            let endX = e.clientX;
            let moveLen = resize.left + (endX - startX); // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
            let maxT = box.clientWidth - resize.offsetWidth; // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

            if (moveLen < 32) moveLen = 32; // 左边区域的最小宽度为32px
            if (moveLen > maxT - 150) moveLen = maxT - 150; //右边区域最小宽度为150px

            resize.style.left = moveLen; // 设置左侧区域的宽度

            left.style.width = moveLen + 'px';
            mid.style.width = box.clientWidth - moveLen - 20 + 'px';
          };
          // 鼠标松开事件
          document.onmouseup = function () {
            //颜色恢复
            resize.style.background = '#d6d6d6';
            document.onmousemove = null;
            document.onmouseup = null;
            resize.releaseCapture && resize.releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
          };
          resize.setCapture && resize.setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
          return false;
        };
      }
    }
  };
</script>

<style lang="scss" scoped>
  .box {
    width: 100%;
    height: 100%;
    margin: 1% 0;
    overflow: hidden;
  }

  .left {
    float: left;
    box-sizing: border-box;
    height: 100%;
    padding-right: 10px;
    background: #fff;
  }

  .left_depart {
    width: calc(32% - 10px); /* 左侧初始化宽度 */
  }

  .left_person {
    width: calc(22% - 10px); /* 左侧初始化宽度 */
  }

  /* 拖拽区div样式 */
  .resize {
    position: relative;
    top: 45%;
    float: left;
    width: 10px;
    height: 50px;
    margin-top: -10px;
    color: white;

    /* z-index: 99999; */
    font-size: 32px;
    background-color: #d6d6d6;
    background-position: center;
    background-size: cover;
    border-radius: 5px;
    cursor: col-resize;
  }

  /* 拖拽区鼠标悬停样式 */
  .resize:hover {
    color: #444;
  }

  /* 右侧div样式 */
  .mid {
    float: left;
    box-sizing: border-box;
    height: 100%;
    padding: 0 7.5px;
    background: #fff;
    border-left: 1px solid #d7d7d7;
  }

  .mid_depart {
    width: 68%; /* 右侧初始化宽度 */
  }

  .mid_person {
    width: 78%; /* 右侧初始化宽度 */
  }
</style>
