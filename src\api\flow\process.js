import request from '@/router/axios';

export const completeTask = (data, params) => {
  return request({
    url: '/api/pc/process-task-operation/approve',
    method: 'post',
    data,
    params
  });
};

// 撤销
export const revocation = (id) => {
  return request({
    url: '/api/pc/process-instance/revocation',
    method: 'get',
    params: { id }
  });
};

// 审批流角标
export const cornerMark = () => {
  return request({
    url: '/api/pc/process-instance-inform/corner-mark',
    method: 'get',
    hideProgress: true
  });
};

export const processList = (params) => {
  return request({
    url: '/api/pc/process-instance/approve-page',
    method: 'get',
    params
  });
};

// 固定业务表单列表
export const fixedFormList = (params) => {
  return request({
    url: '/api/test/page',
    method: 'get',
    params
  });
};

export const processStartList = (isForm) => {
  return request({
    url: '/api/pc/process-definition/start/list',
    method: 'get',
    params: { isForm }
  });
};

export const oldCommonDetail = (processInstanceId) => {
  return request({
    url: '/api/process/common-detail',
    method: 'get',
    params: { processInstanceId }
  });
};

export const oldProcessDetail = (processInstanceId) => {
  return request({
    url: '/api/process/detail',
    method: 'get',
    params: { processInstanceId }
  });
};

export const processDetail = (id) => {
  return request({
    url: '/api/pc/process-instance/detail/flow',
    method: 'get',
    params: { id }
  });
};

export const commonDetail = (id) => {
  return request({
    url: '/api/pc/process-instance/detail-nodes',
    method: 'get',
    params: { id }
  });
};

// pc撤销
export const checkButton = (processId) => {
  return request({
    url: '/api/pc/process-instance/check/button',
    method: 'get',
    params: { processId }
  });
};

// 流程新建/修改
export const create = (data) => {
  return request({
    url: '/api/pc/process-definition/create',
    method: 'post',
    data
  });
};
// 流程预览
export const qrCreate = (data) => {
  return request({
    url: '/api/pc/process-definition/qr',
    method: 'post',
    data
  });
};
// 流程列表
export const listCommon = () => {
  return request({
    url: '/api/pc/process-definition/list-common',
    method: 'get'
  });
};
// 流程详情
export const processDefinitionDetail = (id) => {
  return request({
    url: '/api/pc/process-definition/detail',
    method: 'get',
    params: { id }
  });
};
// 流程详情
export const groupList = () => {
  return request({
    url: '/api/pc/process-group/list',
    method: 'get'
  });
};
// 关联审批单列表
export const definitionList = (params) => {
  return request({
    url: '/api/pc/process-definition/list',
    method: 'get',
    params
  });
};
// 打印审批单
export const printApprovalForm = (params) => {
  return request({
    url: '/api/pc/process-instance/print',
    method: 'get',
    params
  });
};
// 获取流程信息
export const getFlowInfo = (id) => {
  return request({
    url: '/api/pc/process-definition/start/flow',
    method: 'get',
    params: { id }
  });
};
// 获取流程审批人
export const getFlowCondition = (id, dataJson) => {
  return request({
    url: '/api/pc/process-definition/start-condition',
    method: 'post',
    data: { id, dataJson }
  });
};
// 发起审批
export const launchApproval = (data) => {
  return request({
    url: '/api/pc/process-instance/launch',
    method: 'post',
    data
  });
};
// 转交
export const transmit = (params) => {
  return request({
    url: '/api/pc/process-task-operation/transmit',
    method: 'get',
    params
  });
};
// 抄送
export const maillAdd = (params) => {
  return request({
    url: '/api/pc/process-task-operation/maillAdd',
    method: 'get',
    params
  });
};
// 加签
export const signAdd = (data) => {
  return request({
    url: '/api/pc/process-task-operation/signAdd',
    method: 'post',
    data
  });
};
// 抄送全部已读
export const readAllCC = () => {
  return request({
    url: '/api/pc/process-task-operation/read',
    method: 'post'
  });
};
// 评论
export const comment = (data) => {
  return request({
    url: '/api/pc/process-task-operation/comment',
    method: 'post',
    data
  });
};
// 评论
export const deleteComment = (id) => {
  return request({
    url: '/api/pc/process-task-operation/comment/delete',
    method: 'delete',
    params: { id }
  });
};

// 根据字典key获取对应最新发布流程定义id
export const getProcessId = (params) => {
  return request({
    url: `/api/pc/process-definition/getIdByKey`,
    method: 'get',
    params
  });
};
