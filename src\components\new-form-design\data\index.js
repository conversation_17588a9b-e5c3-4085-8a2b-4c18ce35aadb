export default {
  component: [
    {
      name: '文本',
      children: [
        {
          icon: 'icon-text1',
          name: '单行输入框',
          type: 'input',
          valueJson: {
            name: '单行文本',
            placeholder: '请输入',
            maxLength: 30,
            required: true,
            scanCode: false
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            {
              name: '字数上限',
              type: 'maxLength',
              require: true,
              max: 100
            },
            { name: '必填', type: 'required', require: false },
            {
              name: '支持扫码',
              type: 'scanCode',
              require: false
            }
          ]
        },
        {
          icon: 'icon-text2',
          name: '多行输入框',
          type: 'textarea',
          valueJson: {
            name: '多行文本',
            placeholder: '请输入',
            maxLength: 200,
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            {
              name: '字数上限',
              type: 'maxLength',
              require: true,
              max: 5000
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-i',
          name: '说明文字',
          type: 'tips',
          valueJson: {
            content: '说明文字',
            fontColor: false,
            link: '',
            noApply: false
          },
          formJson: [
            { name: '内容', type: 'content', require: true, maxLength: 100 },
            {
              name: '红色字体',
              type: 'fontColor',
              require: false
            },
            {
              name: '链接跳转地址',
              type: 'link',
              require: false,
              maxLength: 100
            },
            {
              name: '只在发起页显示',
              type: 'noApply',
              require: false
            }
          ]
        }
      ]
    },
    {
      name: '数值',
      children: [
        {
          icon: 'icon-number',
          name: '数字输入框',
          type: 'inputNumber',
          valueJson: {
            name: '数字输入框',
            placeholder: '请输入',
            unit: '',
            required: true,
            decimal: true,
            decimalLength: 2
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            { name: '单位', type: 'unit', require: false },
            { name: '必填', type: 'required', require: false },
            { name: '允许小数', type: 'decimal', require: false },
            {
              name: '小数位数',
              type: 'decimalLength',
              require: false,
              max: 5
            }
          ]
        },
        {
          icon: 'icon-money',
          name: '金额',
          type: 'inputMoney',
          valueJson: {
            name: '金额',
            placeholder: '请输入',
            capital: false,
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            { name: '显示大写', type: 'capital', require: false },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-calculate',
          name: '计算公式',
          type: 'computed',
          valueJson: {
            name: '计算公式',
            formula: [],
            // 判断当前formula有没有用到其他计算公式，有的话将其他的加进去
            formulaFlat: [],
            formulaInfo: '',
            required: true
          },
          formJson: [
            {
              name: '控件名称',
              type: 'name',
              require: true
            },
            {
              name: '公式',
              type: 'formula',
              require: true
            }
          ]
        }
      ]
    },
    {
      name: '选项',
      children: [
        {
          icon: 'icon-singlebox',
          name: '单选框',
          type: 'radio',
          valueJson: {
            name: '单选框',
            placeholder: '请选择',
            options: [{ value: '' }, { value: '' }],
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            { name: '选项', type: 'options', require: true },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-CheckBox',
          name: '多选框',
          type: 'checkbox',
          valueJson: {
            name: '多选框',
            placeholder: '请选择',
            options: [{ value: '' }, { value: '' }],
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            { name: '选项', type: 'options', require: true },
            { name: '必填', type: 'required', require: false }
          ]
        }
      ]
    },
    {
      name: '日期',
      children: [
        {
          icon: 'icon-date',
          name: '日期',
          type: 'date',
          valueJson: {
            name: '日期',
            placeholder: '请选择',
            dateType: 1,
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            // 1 年月日 2 年月日上午下午 3 年月日时分
            {
              name: '日期类型',
              type: 'dateType',
              require: true,
              children: [
                { label: '年-月-日', value: 1 },
                { label: '年-月-日 上午／下午', value: 2 },
                { label: '年-月-日 时:分', value: 3 }
              ]
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-date2',
          name: '日期区间',
          type: 'daterange',
          valueJson: {
            nameOne: '开始时间',
            nameTwo: '结束时间',
            nameThree: '时长',
            placeholder: '请选择',
            dateType: 1,
            required: true
          },
          formJson: [
            { name: '控件1名称', type: 'nameOne', require: true },
            { name: '控件2名称', type: 'nameTwo', require: true },
            {
              name: '控件3名称',
              type: 'nameThree',
              require: true
            },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            // 1 年月日 2 年月日上午下午 3 年月日时分
            {
              name: '日期类型',
              type: 'dateType',
              require: true,
              children: [
                { label: '年-月-日', value: 1 },
                { label: '年-月-日 上午／下午', value: 2 },
                { label: '年-月-日 时:分', value: 3 }
              ]
            },
            { name: '必填', type: 'required', require: false }
          ]
        }
      ]
    },
    {
      name: '附件',
      children: [
        {
          icon: 'icon-pic',
          name: '图片',
          type: 'image',
          valueJson: {
            name: '图片',
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-flies',
          name: '附件',
          type: 'file',
          valueJson: {
            name: '附件',
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            { name: '必填', type: 'required', require: false }
          ]
        }
      ]
    },
    {
      name: '其他',
      children: [
        {
          icon: 'icon-passcard',
          name: '身份证',
          type: 'idcard',
          headerTooltip: '身份证信息自动校验',
          valueJson: {
            name: '身份证',
            placeholder: '请输入',
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-phone1',
          name: '电话',
          type: 'phone',
          valueJson: {
            name: '电话',
            placeholder: '请输入',
            phoneType: 1,
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            // 1座机或手机 2仅座机 3仅手机
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            {
              name: '类型',
              type: 'phoneType',
              require: false,
              children: [
                { label: '座机或手机', value: 1 },
                { label: '仅座机', value: 2 },
                { label: '仅手机', value: 3 }
              ]
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-contact',
          name: '联系人',
          type: 'people',
          valueJson: {
            name: '联系人',
            placeholder: '请选择',
            peopleSelect: 1,
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            // 1只能选择一人 2可以选择多人
            {
              name: '选择',
              type: 'peopleSelect',
              children: [
                { label: '只能选择一人', value: 1 },
                { label: '可以选择多人', value: 2 }
              ],
              require: false
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-group',
          name: '部门',
          type: 'dept',
          valueJson: {
            name: '部门',
            placeholder: '请选择',
            deptSelect: 1,
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            // 1只能选择一个部门 2可以选择多个部门
            {
              name: '选择',
              type: 'deptSelect',
              children: [
                { label: '只能选择一个部门', value: 1 },
                { label: '可以选择多个部门', value: 2 }
              ],
              require: false
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-map',
          name: '地点',
          type: 'area',
          headerTooltip: '自动获取发起人所在地点',
          valueJson: {
            name: '地点',
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-area',
          name: '省市区',
          type: 'region',
          valueJson: {
            name: '省市区',
            provinceType: 1,
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            // 1省市区 2省市区街道 3省市
            {
              name: '类型',
              type: 'provinceType',
              children: [
                { label: '省市区', value: 1 },
                { label: '省市区街道', value: 2 }
              ],
              require: false
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-form',
          name: '关联审批单',
          type: 'relationApply',
          valueJson: {
            name: '关联审批单',
            placeholder: '请选择',
            relevanceApply: '',
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            {
              name: '提示文字',
              type: 'placeholder',
              require: true
            },
            // 1只能选择一个部门 2可以选择多个部门
            {
              name: '关联审批',
              type: 'relevanceApply',
              require: true
            },
            { name: '必填', type: 'required', require: false }
          ]
        },
        {
          icon: 'icon-xls',
          name: '明细/表格',
          type: 'form',
          children: [],
          valueJson: {
            name: '明细/表格',
            action: '动作',
            required: true
          },
          formJson: [
            { name: '控件名称', type: 'name', require: true },
            { name: '动作', type: 'action', require: true, maxLength: 10 },
            { name: '必填', type: 'required', require: false }
          ]
        }
      ]
    }
  ],
  componentGroup: [
    {
      name: '人事管理',
      children: [
        {
          icon: 'icon-holiday',
          name: '请假/调休套件',
          type: 'rest',
          header: '请假类型',
          children: [
            {
              type: 'desc',
              id: 'rest5'
            },
            {
              type: 'radio',
              id: 'rest1',
              valueJson: {
                name: '请假类型',
                placeholder: '请选择',
                required: true
              }
            },
            {
              type: 'date',
              id: 'rest2',
              valueJson: {
                name: '开始时间',
                placeholder: '请选择',
                required: true,
                dateType: 3
              }
            },
            {
              type: 'date',
              id: 'rest3',
              valueJson: {
                name: '结束时间',
                placeholder: '请选择',
                required: true,
                dateType: 3
              }
            },
            {
              type: 'text',
              id: 'rest4',
              valueJson: {
                name: '时长',
                placeholder: '自动计算'
              }
            }
          ],
          formJson: [
            {
              type: 'holiday'
            }
          ]
        },
        {
          icon: 'icon-replacecard',
          name: '补卡套件',
          type: 'replacecard',
          children: [
            {
              type: 'desc',
              id: 'replacecard2'
            },
            {
              type: 'date',
              id: 'replacecard1',
              valueJson: {
                name: '补卡时间',
                placeholder: '请选择',
                required: true,
                dateType: 3
              }
            }
          ],
          valueJson: {
            name: '补卡时间',
            placeholder: '请选择'
          }
        },
        {
          icon: 'icon-work',
          name: '加班套件',
          type: 'work',
          header: '加班人',
          children: [
            {
              type: 'desc',
              id: 'work5'
            },
            {
              type: 'people',
              id: 'work1',
              valueJson: {
                name: '加班人',
                placeholder: '请选择',
                peopleSelect: 2,
                required: true
              }
            },
            {
              type: 'date',
              id: 'work2',
              valueJson: {
                name: '开始时间',
                placeholder: '请选择',
                dateType: 3,
                required: true
              }
            },
            {
              type: 'date',
              id: 'work3',
              valueJson: {
                name: '结束时间',
                placeholder: '请选择',
                dateType: 3,
                required: true
              }
            },
            {
              type: 'text',
              id: 'work4',
              valueJson: {
                name: '时长',
                unit: '小时',
                placeholder: '自动计算'
              }
            }
          ],
          valueJson: {
            instead: false
          },
          formJson: [
            {
              name: '允许代他人提交',
              type: 'instead',
              require: false
            }
          ]
        },
        {
          icon: 'icon-out',
          name: '外出套件',
          type: 'out',
          children: [
            {
              type: 'date',
              id: 'out1',
              valueJson: {
                name: '开始时间',
                placeholder: '请选择',
                required: true
              }
            },
            {
              type: 'date',
              id: 'out2',
              valueJson: {
                name: '结束时间',
                placeholder: '请选择',
                required: true
              }
            },
            {
              type: 'text',
              id: 'out3',
              valueJson: {
                name: '时长',
                placeholder: '自动计算'
              }
            }
          ],
          valueJson: {
            dateType: 1
          },
          formJson: [
            // 1 年月日 2 年月日上午下午 3 年月日时分
            {
              name: '日期类型',
              type: 'dateType',
              require: true,
              children: [
                { label: '年-月-日', value: 1 },
                { label: '年-月-日 上午／下午', value: 2 },
                { label: '年-月-日 时:分', value: 3 }
              ]
            }
          ]
        },
        {
          icon: 'icon-trip',
          name: '出差套件',
          type: 'trip',
          children: [
            {
              type: 'textarea',
              id: 'trip1',
              valueJson: {
                name: '出差事由',
                placeholder: '请输入',
                maxLength: 200,
                required: false
              }
            },
            {
              type: 'form',
              id: 'trip2',
              children: [
                {
                  type: 'radio',
                  id: 'trip3',
                  valueJson: {
                    name: '交通工具',
                    placeholder: '请选择',
                    options: [
                      { value: '火车' },
                      { value: '汽车' },
                      { value: '飞机' }
                    ],
                    required: true
                  }
                },
                {
                  type: 'radio',
                  id: 'trip4',
                  valueJson: {
                    name: '单程/往返',
                    placeholder: '请选择',
                    options: [{ value: '单程' }, { value: '往返' }],
                    required: true
                  }
                },
                {
                  type: 'region',
                  id: 'trip5',
                  valueJson: {
                    name: '出发城市',
                    provinceType: 3,
                    required: true
                  }
                },
                {
                  type: 'region',
                  id: 'trip6',
                  valueJson: {
                    name: '目的城市',
                    provinceType: 3,
                    required: true
                  }
                },
                {
                  type: 'daterange',
                  id: 'trip7',
                  valueJson: {
                    nameOne: '开始时间',
                    nameTwo: '结束时间',
                    nameThree: '时长',
                    placeholder: '请选择',
                    required: true
                  }
                }
              ],
              valueJson: {
                name: '行程',
                action: '添加',
                required: true
              },
              formJson: [
                { name: '控件名称', type: 'name', require: true },
                { name: '添加', type: 'action', require: true },
                { name: '必填', type: 'required', require: false }
              ]
            },
            {
              type: 'text',
              id: 'trip8',
              valueJson: {
                name: '出差天数',
                unit: '自然日',
                placeholder: '自动计算'
              }
            },
            {
              type: 'textarea',
              id: 'trip9',
              valueJson: {
                name: '出差备注',
                placeholder: '请输入',
                maxLength: 200,
                required: false
              }
            },
            {
              type: 'people',
              id: 'trip10',
              valueJson: {
                name: '同行人',
                placeholder: '请选择',
                peopleSelect: 2,
                required: false
              }
            }
          ],
          valueJson: {
            dateType: 1,
            peerPeople: true
          },
          formJson: [
            // 1 年月日 2 年月日上午下午 3 年月日时分
            {
              name: '日期类型',
              type: 'dateType',
              require: true,
              children: [
                { label: '年-月-日', value: 1 },
                { label: '年-月-日 上午／下午', value: 2 },
                { label: '年-月-日 时:分', value: 3 }
              ]
            },
            {
              name: '可添加同行人',
              type: 'peerPeople',
              require: false
            }
          ]
        },
        {
          icon: 'icon-turn',
          name: '转正套件',
          type: 'turnFormal',
          children: [
            {
              type: 'people',
              id: 'turnFormal1',
              valueJson: {
                name: '申请人',
                placeholder: '请选择',
                peopleSelect: 1,
                required: true
              }
            },
            {
              type: 'date',
              id: 'turnFormal2',
              valueJson: {
                name: '入职时间',
                placeholder: '请选择',
                required: true,
                dateType: 1
              }
            },
            {
              type: 'inputNumber',
              id: 'turnFormal3',
              valueJson: {
                name: '试用期',
                placeholder: '请输入',
                unit: '月',
                required: false,
                decimal: false,
                decimalLength: 2
              }
            },
            {
              type: 'date',
              id: 'turnFormal4',
              valueJson: {
                name: '转正时间',
                placeholder: '请选择',
                required: true,
                dateType: 1
              }
            }
          ],
          valueJson: {
            instead: false
          },
          formJson: [
            {
              name: '允许代他人提交',
              type: 'instead',
              require: false
            }
          ]
        },
        {
          icon: 'icon-leave',
          name: '离职套件',
          type: 'leave',
          children: [
            {
              type: 'date',
              id: 'leave1',
              valueJson: {
                name: '入职日期',
                placeholder: '请选择',
                required: true,
                dateType: 1
              }
            },
            {
              type: 'date',
              id: 'leave2',
              valueJson: {
                name: '预计离职日期',
                placeholder: '请选择',
                required: true,
                dateType: 1
              }
            },
            {
              type: 'input',
              id: 'leave3',
              valueJson: {
                name: '离职原因',
                placeholder: '请输入',
                maxLength: 30,
                required: true,
                scanCode: false
              }
            },
            {
              type: 'textarea',
              id: 'leave4',
              valueJson: {
                name: '离职原因备注',
                placeholder: '请输入',
                maxLength: 200,
                required: false
              }
            },
            {
              type: 'people',
              id: 'leave5',
              valueJson: {
                name: '工作交接人',
                placeholder: '请选择',
                peopleSelect: 2,
                required: true
              }
            },
            {
              type: 'textarea',
              id: 'leave6',
              valueJson: {
                name: '工作交接事项',
                placeholder: '请输入',
                maxLength: 500,
                required: true
              }
            }
          ]
        }
      ]
    }
  ]
};
