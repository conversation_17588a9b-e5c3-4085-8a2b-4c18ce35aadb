<template>
  <h-input-number
    v-model="hValue"
    :min="0"
    :max="max"
    :step="1"
    :precision="precision"
    :placeholder="data.placeholder"
    clearable
  />
</template>

<script>
  export default {
    name: 'InputNumberComp',
    props: {
      value: {
        type: String
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    computed: {
      hValue: {
        get() {
          if (!this.value) {
            return undefined;
          }
          if (typeof this.value === 'string') {
            return parseFloat(this.value);
          }
          return this.value;
        },
        set(val) {
          if (val === undefined) {
            this.$emit('input', '');
            return;
          }
          let str = val + '';
          if (this.precision && !str.includes('.')) {
            str += '.' + '0'.repeat(this.precision);
          }
          this.$emit('input', str);
        }
      },
      precision() {
        let { decimal, decimalLength } = this.data;
        if (!decimal) {
          return 0;
        }
        if (decimalLength === undefined) {
          decimalLength = 2;
        }
        return decimalLength;
      },
      max() {
        return parseFloat(9999999999 + '.' + '9'.repeat(this.precision));
      }
    }
  };
</script>
