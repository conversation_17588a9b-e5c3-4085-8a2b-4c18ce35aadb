<template>
  <el-drawer
    size="60%"
    class="drawer_modal"
    append-to-body
    :title="drawerTitle"
    :visible.sync="open"
    :wrapperClosable="isView"
    :before-close="handleDrawerClose"
  >
    <add-edit-form
      v-if="!isView"
      ref="form"
      :formData="formData"
      :drawerType="drawerType"
    />
    <detail v-else :formData="formData" />
    <div
      class="avue-dialog__footer avue-dialog__footer--right"
      style="z-index: 999"
    >
      <el-button
        v-if="!isView"
        type="primary"
        size="small"
        icon="el-icon-circle-plus-outline"
        @click="handleSubmit"
        >保 存</el-button
      >
      <el-button
        v-if="!isView"
        size="small"
        icon="el-icon-circle-close"
        @click="handleDrawerClose"
        >返 回</el-button
      >
    </div>
  </el-drawer>
</template>
<script>
  import AddEditForm from './components/form.vue';
  import Detail from './components/detail.vue';
  import { getUser, add, update } from '@/api/system/user';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'user-drawer',
    components: { AddEditForm, Detail },
    props: {
      drawerType: String,
      userId: String,
      drawerVisible: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      drawerVisible(val) {
        if (val && ['view', 'edit'].includes(this.drawerType)) {
          this.getUserDetail();
        }
      }
    },
    data() {
      return {
        formData: {
          deptPostList: []
        }
      };
    },
    methods: {
      // 获取用户详情
      async getUserDetail() {
        try {
          const res = await getUser(this.userId);
          const data = res.data.data;
          this.formData = {
            ...data,
            userType: `${data.userType}`,
            roleId: data.roleId.split(',')
          };
        } catch (e) {
          console.error(e);
        }
      },
      // 保存
      async handleSubmit() {
        const { valid, form } = this.$refs.form.validateForm();
        if (!valid) return;
        const apiObj = {
          add: {
            api: add,
            msg: '新增成功!'
          },
          edit: {
            api: update,
            msg: '编辑成功!'
          }
        };
        let params = cloneDeep(form);
        params.roleId = params.roleId.toString();
        const res = await apiObj[this.drawerType].api(params);
        if (res.data.code === 200) {
          this.$message.success(apiObj[this.drawerType].msg);
          this.handleDrawerClose();
          this.$emit('refresh');
        }
      },
      // 抽屉关闭
      handleDrawerClose() {
        this.form = {};
        this.$refs.form && this.$refs.form.resetFields();
        this.open = false;
      }
    },
    computed: {
      open: {
        get() {
          return this.drawerVisible;
        },
        set() {
          this.$emit('close');
        }
      },
      isView() {
        return this.drawerType === 'view';
      },
      drawerTitle() {
        const titleObj = { view: '查看', add: '新增', edit: '编辑' };
        return titleObj[this.drawerType];
      }
    }
  };
</script>
<style lang="scss" scoped>
  .drawer_modal {
    ::v-deep(.el-drawer__header) {
      padding-bottom: 20px;
      color: #000000d9;
      font-size: 18px;
      letter-spacing: 6px;
      border-bottom: 1px solid #f0f0f0;
    }

    ::v-deep(.el-drawer__body) {
      padding: 0 10px 60px 30px;

      .el-form {
        padding: 0 20px;
      }
    }
  }
</style>
