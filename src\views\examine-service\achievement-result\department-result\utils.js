// 根据本周期考核得分计算部门名次
export function getDeptsRank(data) {
  if (!data || !data.length) return [];
  const scoreMap = new Map();
  [...new Set(data.map((item) => item.assessScore))]
    .sort((a, b) => b - a)
    .forEach((score, index) => scoreMap.set(score, index + 1));
  const res = data.map((item) => ({
    ...item,
    rank:
      item.assessScore !== undefined && item.assessScore !== ''
        ? scoreMap.get(item.assessScore)
        : '---'
  }));
  return res;
}
