<template>
  <basic-container>
    <transition-group name="fade">
      <div v-show="!open" :key="1">
        <search @query="query" />
        <table-list v-loading="loading" :list="list" @dispatch="handleEvent" />
        <yk-pagination
          small
          v-show="total > 0"
          :total="total"
          :page.sync="params.current"
          :limit.sync="params.size"
          @pagination="getList"
        />
      </div>
      <yk-local-model :key="2" :title="title" :open="open" @close="close">
        <evaluate
          :is-detail="isDetail"
          :code="code"
          :is-approval="isApproval"
          @close="close"
          @refresh="refresh"
        />
      </yk-local-model>
    </transition-group>
  </basic-container>
</template>

<script>
  import { getPageList } from '@/api/examine/post-examine';
  import { Search, TableList } from './components';
  import Evaluate from './evaluate';
  // 岗位业绩考核
  export default {
    name: 'postExamine',
    components: {
      Search,
      TableList,
      Evaluate
    },
    data() {
      return {
        loading: false,
        params: {
          current: 1,
          size: 10
        },
        list: [],
        total: 0,
        title: '评价',
        open: false,
        isDetail: false,
        isApproval: false,
        code: ''
      };
    },
    methods: {
      // 查询方法
      async request() {
        try {
          this.loading = true;
          const res = await getPageList(this.params);
          const { records = [], total = 0 } = res.data.data;
          this.list = records;
          this.total = total;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 搜索查询
      query(form) {
        Object.assign(this.params, form, {
          current: 1,
          size: 10
        });
        this.request();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.params, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 操作分发
      handleEvent(type, { code }) {
        this.code = code;
        switch (type) {
          case 'detail':
            return this.handleDetail();
          case 'edit':
            return this.handleEdit();
          case 'evaluate':
            return this.handleEvaluate();
          case 'examine':
            return this.handleExamine();
          default:
            return false;
        }
      },
      // 查看
      handleDetail() {
        this.title = '查看';
        this.open = true;
        this.isDetail = true;
      },
      // 编辑
      handleEdit() {
        this.title = '编辑';
        this.open = true;
        this.isDetail = false;
      },
      // 评价
      handleEvaluate() {
        this.title = '评价';
        this.open = true;
        this.isDetail = false;
      },
      // 审核
      handleExamine() {
        this.title = '审核';
        this.open = true;
        this.isApproval = true;
        this.isDetail = true;
      },
      // 关闭
      close() {
        this.open = false;
        this.isApproval = false;
        this.isDetail = false;
      },
      // 刷新
      refresh() {
        this.close();
        this.request();
      }
    }
  };
</script>
