<template>
  <h-block :title="$route.meta.title" class="approval-apply">
    <div class="apply-info">
      <img
        v-if="isSign && dataObj.imgUrl"
        v-oss
        :src="dataObj.imgUrl"
        class="apply-status"
      />
      <!-- <div v-if="dataObj.isRelated" @click="share" class="share-button">
        <i class="iconfont icon-company-0" />分享
      </div> -->
      <header>审批内容</header>
      <span v-if="isWarn">
        <i class="iconfont icon-error warningStr"></i>
        {{ dataObj.warningStr }}</span
      >
      <apply-component
        :style="isForm ? 'display:none' : ''"
        v-model="form"
        :component-list="dataObj.formStyleJson"
        :accept="'.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .xls, .xlsx, .pdf, .ppt, .rar, .zip'"
        :field-status="dataObj.fieldStatus"
      ></apply-component>
      <!-- 固定表单页面 -->
      <FormDetail v-if="isForm" :form="testPluginForm" />
    </div>
    <div v-if="isDetail" class="detailLink">
      <span @click="goYDDetail">查看业务详情</span>
    </div>
    <apply-log
      v-if="dataObj.oldProcessId"
      :process-instance-id="dataObj.oldProcessId"
      :is-pick-up="true"
      title="原审批流程"
    ></apply-log>
    <apply-log
      v-if="updateData"
      @refresh="resetDetails"
      :process-instance-id="dataObj.processInstanceId"
      :title="dataObj.oldProcessId ? '当前审批流程' : '审批日志'"
    ></apply-log>
    <div class="footer-btn">
      <el-button @click="cancel" class="cancle">返回</el-button>
      <el-popover
        v-if="moreBtn.length"
        placement="top"
        width="140"
        trigger="hover"
        popper-class="more-btn"
      >
        <div>
          <div
            v-for="({ callback, type, text, icon = '' }, i) of moreBtn"
            :key="i"
            @click="callback(type)"
            class="btn-cell"
          >
            <i :class="icon.includes('el-') ? icon : `iconfont ${icon}`"></i>
            {{ text }}
          </div>
        </div>
        <el-button slot="reference">更多</el-button>
      </el-popover>
      <el-button
        v-for="({ callback, type, text }, i) of footerBtn"
        :key="i"
        @click="callback(type)"
        :class="type"
        >{{ text }}</el-button
      >
    </div>
    <!-- 同意拒绝 -->
    <apply-dialog
      @submit="submit"
      :dialog-visible.sync="dialogVisible"
    ></apply-dialog>
    <!-- 销假 -->
    <repeal-dialog
      v-loading="repealLoading"
      @submit="repealSubmit"
      :repeal-visible.sync="repealVisible"
    ></repeal-dialog>
    <!-- 打印预览文件弹窗 -->
    <print-approval-form
      v-if="printFromShow"
      @close="closePrintApprovalForm"
      :url="printFromUrl"
      :show="printFromShow"
    />
    <change-apply-log
      @status-success="resetDetails"
      :change-apply-log-visible.sync="changeApplyLogVisible"
      :apply-log-status="applyLogStatus"
      :data-obj="dataObj"
    ></change-apply-log>
    <!-- 评论 -->
    <comment-dialog
      @submit="commentSubmit"
      :visible.sync="commentVisible"
    ></comment-dialog>
    <select-people
      @rangeSave="shareSubmit"
      :range-visible.sync="shareVisible"
      :is-radio="false"
      :min-select-length="1"
    ></select-people>
  </h-block>
</template>
<script>
  import './approval/index.scss';
  import { mapGetters } from 'vuex';
  import {
    processDetail,
    completeTask,
    revocation,
    printApprovalForm,
    checkButton,
    launchApproval,
    comment
  } from '@/api/flow/process';
  import { urge, share, createDiscussion } from '@/api/desk/im';
  import { setUserCompany } from '@/api/system/dept';
  import { resetRouter } from '@/router/router';

  import applyLog from '@/components/apply-log';
  import applyComponent from '@/components/apply-component';
  // import PrintApprovalForm from './approval/print-approval-form';
  import applyDialog from './approval/apply-dialog';
  import repealDialog from './approval/repeal-dialog';
  import changeApplyLog from './approval/change-apply-log';
  import CommentDialog from './approval/comment-dialog';
  import SelectPeople from '@/components/select-tree/select-people';
  import FormDetail from './form-detail.vue';

  const ID_TO_LINK = new Map([
    [13, 'contractDetail'],
    [19, 'contractDetail'],
    [15, 'authorizationView'],
    [14, 'decisionDetail'],
    [11, 'viewWill'],
    [18, 'invoiceDetail'],
    [16, 'receiptDetail'],
    [17, 'payDetail'],
    [12, 'inventoryCheckDetail']
  ]);

  export default {
    components: {
      applyLog,
      applyComponent,
      // PrintApprovalForm,
      applyDialog,
      repealDialog,
      changeApplyLog,
      CommentDialog,
      SelectPeople,
      FormDetail
    },
    // props: { id: String },
    data() {
      return {
        id: undefined,
        isForm: undefined,
        testPluginForm: {},
        dataObj: {
          imgUrl: ''
        },
        footerBtn: [],
        moreBtn: [],
        dialogVisible: false,
        repealLoading: false,
        applyOpinion: '',
        flag: '',
        printFromUrl: '', // 打印的url
        printFromShow: false,
        updateData: true,
        isCheck: false,
        repealVisible: false,
        applyLogStatus: '',
        changeApplyLogVisible: false,
        commentVisible: false,
        shareVisible: false,
        form: {}
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      isSign() {
        return this.dataObj.status !== 0 && !this.dataObj.oldProcessId;
      },
      isWarn() {
        return this.dataObj.warningStr && this.dataObj.flowableTaskId;
      },
      isDetail() {
        return ID_TO_LINK.has(this.dataObj.formBusinessType);
      }
    },
    watch: {
      dataObj: {
        handler() {
          this.createdBtn();
        },
        deep: true
      },
      isCheck() {
        this.createdBtn();
      }
    },
    created() {
      this.resetDetails();
    },
    methods: {
      resetDetails() {
        this.id = this.$route.query.id;
        // const loading = this.$loading();
        processDetail(this.id)
          .then((res) => {
            if (res.data.data.oldProcessId) {
              res.data.data.processName = `${res.data.data.processName}-销假申请`;
              if (res.data.data.status === 1) {
                res.data.data.processName = `${res.data.data.processName}-已销假`;
              } else if (res.data.data.status === 2) {
                res.data.data.processName = `${res.data.data.processName}--销假申请被拒绝`;
              }
            }
            this.$route.meta.title = res.data.data.processName;
            this.$router.$avueRouter.setTitle(res.data.data.processName, false);
            this.getDetail(res.data.data);
            this.updateData = false;
            this.$nextTick(() => {
              this.updateData = true;
            });
          })
          .finally(() => {
            // loading.close();
          });
      },
      getDetail(data) {
        /**
         * status 0, "审批中" 1, "通过" 2, "拒绝" 3, "撤销" 4, "禁用" 6, "删除"
         * categoryId 区分淄矿项目
         * businessId 淄矿跳转id
         * isCommon 判断是否为当前人发起
         * oldProcessId 判断当前是否为销假
         * isDefinitionActive 流程定义是否可用
         * warningStr 加班预警
         */
        this.dataObj = data;
        // 右上角审批状态图片
        this.dataObj.imgUrl = `/apply/apply-${data.status || 0}.png`;
        // json样式
        this.dataObj.formStyleJson = JSON.parse(data.formStyleJson);
        // formkv
        this.form = JSON.parse(data.dataJson);

        this.isForm = data.isForm !== null ? data.isForm : 0;
        this.testPluginForm = data.testPluginForm;

        this.checkButton(data.processInstanceId);
      },
      checkButton(processId) {
        checkButton(processId).then((res) => {
          if (res && res.data && res.data.success) {
            this.isCheck = res.data.data;
          }
        });
      },
      goYDDetail() {
        let routeUrl = this.$router.resolve({
          name: 'application',
          query: {
            code: 'ydzk',
            target: ID_TO_LINK.get(this.dataObj.formBusinessType),
            query: JSON.stringify({ id: this.dataObj.businessId })
          }
        });
        window.open(routeUrl.href, '_blank');
      },
      createdBtn() {
        let {
          isRelated,
          flowableTaskId,
          isCommon,
          status,
          isDefinitionActive
        } = this.dataObj;
        if (!isRelated) {
          this.moreBtn = [];
          this.footerBtn = [];
          return;
        }

        const obj = [];

        if (flowableTaskId) {
          obj.push({ callback: this.confirm, type: 'refuse', text: '拒绝' });
          obj.push({ callback: this.confirm, type: 'agree', text: '同意' });
        }

        if (isCommon && status === 0) {
          obj.push({ callback: this.urge, text: '催办', icon: 'icon-cui-0' });
          obj.push({
            callback: this.repeal,
            text: '撤销',
            icon: 'icon-rollback-0'
          });
        }

        obj.push({
          callback: this.comment,
          text: '评论',
          icon: 'icon-pinglun-0'
        });

        // 调整更多按钮顺序
        if (flowableTaskId) {
          obj.push({
            callback: this.changeApplyLog,
            type: 'transfer',
            text: '转交',
            icon: 'icon-forward-0'
          });
          obj.push({
            callback: this.changeApplyLog,
            type: 'addSign',
            text: '加签',
            icon: 'icon-addtip-0'
          });
        }

        // obj.push({
        //   callback: this.createDiscussion,
        //   text: '发起讨论',
        //   icon: 'icon-discuss-0'
        // });

        if (status === 0 && (flowableTaskId || isCommon)) {
          obj.push({
            callback: this.changeApplyLog,
            type: 'send',
            text: '抄送',
            icon: 'icon-send-0'
          });
        }

        // if (status === 1 || status === 2) {
        //   obj.push({
        //     callback: this.printFrom,
        //     text: '打印',
        //     icon: 'el-icon-printer'
        //   });
        // }

        // if (this.isCheck) {
        //   obj.push({
        //     callback: this.repealHoliday,
        //     text: '销假',
        //     icon: 'icon-rollback-0'
        //   });
        // }
        if (
          isDefinitionActive &&
          this.dataObj.orgId === this.userInfo.dept_id
        ) {
          obj.push({
            callback: this.againLaunch,
            text: '再次发起',
            icon: 'icon-retweet-0'
          });
        }

        if (obj.length > 3) {
          this.moreBtn = obj.slice(2, obj.length);
          this.footerBtn = obj.slice(0, 2);
        } else {
          this.moreBtn = [];
          this.footerBtn = obj;
        }
      },
      changeCompany(id) {
        const loading = this.$loading({
          lock: true,
          spinner: 'el-icon-loading'
        });
        setUserCompany(id)
          .then(() => {
            this.$store.commit('SET_REFRESH_LOCK', true);
            return this.$store.dispatch('refreshToken');
          })
          .then((data) => {
            return this.$store.dispatch('saveLoginData', data);
          })
          .then(() => {
            return this.$store.dispatch('GetCompanyList');
          })
          .then(() => {
            this.$router.go(0);
          })
          .catch(() => {
            loading.close();
            resetRouter();
            this.$router.push({ name: 'login' });
          });
      },
      confirm(type) {
        this.flag = type === 'agree' ? 1 : 2;
        this.dialogVisible = true;
      },
      submit({ applyOpinion, file }) {
        const data = {
          status: this.flag,
          flowableTaskId: this.dataObj.flowableTaskId,
          opinion: applyOpinion,
          flowProcessInstanceId: this.dataObj.flowProcessInstanceId,
          currentNodeId: this.dataObj.currentNodeId,
          file: JSON.stringify(file),
          dataJson: JSON.stringify(this.form),
          testPluginForm: this.testPluginForm
        };
        const loading = this.$loading();
        completeTask(data, this.$route.query)
          .then((res) => {
            this.$message.success(
              res.data.data.flag ? '审批成功，自动进入下一审批' : '审批成功'
            );
            this.dialogVisible = false;
            this.$store.dispatch('cornerMark');
            loading.close();
            setTimeout(() => {
              // const { processInstanceId, flowableInstanceId, isOld, flag } =
              //   res.data.data;

              // if (!flag) {
              //   this.resetDetails();
              //   return;
              // }

              // let jump;
              // if (isOld) {
              //   jump = this.$router.resolve({
              //     name: 'applyOldDetail',
              //     params: { id: flowableInstanceId }
              //   });
              // } else {
              //   jump = this.$router.resolve({
              //     name: 'apply',
              //     params: { id: processInstanceId },
              //     query: this.$route.query
              //   });
              // }
              // this.$router.push(jump.location);
              this.resetDetails();
            }, 500);
          })
          .catch((err) => {
            console.log('审核结束--报错', err);
          })
          .finally(() => {
            loading.close();
          });
      },
      urge() {
        this.$confirm('是否向当前审批人发送催办消息？', '提示').then(() => {
          const loading = this.$loading();
          urge(this.id)
            .then(() => {
              loading.close();
              this.$message.success('操作成功');
            })
            .catch(() => {
              loading.close();
            });
        });
      },
      async repeal() {
        this.flag = 3;
        const confirm = await this.$confirm(`确定要撤销吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return true;
          })
          .catch(() => {});

        if (confirm) {
          const res = await revocation(this.id);
          if (res && res.data && res.data.success) {
            this.$message.success('撤回成功');
          }
          await this.resetDetails();
        }
      },
      comment() {
        this.commentVisible = true;
      },
      commentSubmit({ description, file }) {
        const loading = this.$loading();
        comment({
          description,
          processInstanceId: this.dataObj.flowProcessInstanceId,
          nodeId: this.dataObj.currentNodeId,
          file: JSON.stringify(file)
        })
          .then(() => {
            loading.close();
            this.$message.success('操作成功');
            this.commentVisible = false;
            this.resetDetails();
          })
          .catch(() => {
            loading.close();
          });
      },
      changeApplyLog(applyLogStatus) {
        this.applyLogStatus = applyLogStatus;
        this.changeApplyLogVisible = true;
      },
      createDiscussion() {
        this.$confirm('是否确定要发起讨论？', '提示').then(() => {
          const loading = this.$loading();
          createDiscussion(this.id)
            .then((res) => {
              if (res.data && res.data.success) {
                // 发起讨论成功后保存聊天数据 用于跳转后自动选中该聊天
                res.data.data.conversationType = 3;
                this.$store.dispatch('chat/setChatUserInfo', res.data.data);
              }
              loading.close();
              this.$message.success('操作成功');
              this.$router.push({ name: 'im' });
            })
            .catch(() => {
              loading.close();
            });
        });
      },
      // 打印表单
      printFrom() {
        let params = {
          id: this.id
        };
        const loading = this.$loading();
        printApprovalForm(params)
          .then((res) => {
            if (res.data) {
              this.printFromShow = true;
              this.printFromUrl = res.data.data;
            }
          })
          .catch(() => {})
          .finally(() => {
            loading.close();
          });
      },
      // 关闭打印表单
      closePrintApprovalForm() {
        this.printFromShow = false;
        this.printFromUrl = '';
      },
      repealHoliday() {
        this.repealVisible = true;
      },
      repealSubmit(remark) {
        const data = {
          remark,
          processDefinitionId: this.dataObj.processDefinitionId,
          oldProcessId: this.dataObj.processInstanceId
        };
        this.repealLoading = true;
        launchApproval(data)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.$message.success('发起成功');
              this.resetDetails();
              this.repealVisible = false;
            }
          })
          .finally(() => {
            this.repealLoading = false;
          });
      },
      againLaunch() {
        this.$router.push({
          name: 'launchApproval',
          // params: { id: this.dataObj.processDefinitionId },
          query: {
            id: this.dataObj.processDefinitionId,
            instanceId:
              this.dataObj.oldProcessId || this.dataObj.processInstanceId
          }
        });
      },
      share() {
        this.shareVisible = true;
      },
      shareSubmit(list = []) {
        const loading = this.$loading();
        share({
          flowId: this.id,
          userIds: Array.from(list, (item) => item.userId)
        })
          .then(() => {
            loading.close();
            this.$message.success('操作成功');
            this.shareVisible = false;
          })
          .catch(() => {
            loading.close();
          });
      },
      findTag(value) {
        let tag, key;
        let tagList = this.$store.state.tags.tagList;
        tagList.map((item, index) => {
          if (item.value === value) {
            tag = item;
            key = index;
          }
        });
        return { tag: tag, key: key };
      },
      cancel() {
        let { tag } = this.findTag(this.$route.fullPath);
        this.$store.commit('DEL_TAG', tag);
        setTimeout(() => {
          this.$router.push('/flow-manage/approval');
        }, 500);
      }
    }
  };
</script>
