<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    :component-list="componentList"
    :disabled="disabled"
    :rules="rules"
    :loading="loading"
  />
</template>

<script>
  import { mapGetters } from 'vuex';
  import { getEntryTime, getRegularTime } from '@/api/user/staff';
  import { deepClone } from '@/util/util';
  import { str2Date } from '@/util/date';

  export default {
    name: 'TurnFormalComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: {
          turnFormal2: false,
          turnFormal4: false
        }
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      rules() {
        let rules = {
          turnFormal4: [
            { required: true, message: '请选择' },
            { validator: this.validateTurnFormal4 }
          ]
        };
        return rules;
      }
    },
    watch: {
      'hValue.turnFormal2'() {
        this.setTurnFormal4();
      },
      'hValue.turnFormal3'() {
        this.setTurnFormal4();
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        let { id, employeeName } = this.userInfo;
        this.$set(this.hValue, 'turnFormal1', [{ id: id, name: employeeName }]);
        if (!this.data.instead) {
          let componentList = deepClone(this.componentList);
          componentList[0].valueJson.readonly = true;
          this.$emit('update:component-list', componentList);
        }
        this.$set(this.loading, 'turnFormal2', true);
        getEntryTime()
          .then((res) => {
            this.$set(this.hValue, 'turnFormal2', res.data.data || '');
            this.$nextTick(() => {
              this.$nextTick(() => {
                this.$refs.formBuild.clearValidate('turnFormal2');
              });
            });
            this.$set(this.loading, 'turnFormal2', false);
          })
          .catch(() => {
            this.$set(this.loading, 'turnFormal2', false);
          });
      },
      setTurnFormal4() {
        this.$set(this.hValue, 'turnFormal4', '');
        let { turnFormal2, turnFormal3 } = this.hValue;
        if (turnFormal2 && turnFormal3) {
          this.$set(this.loading, 'turnFormal4', true);
          getRegularTime(turnFormal2, turnFormal3)
            .then((res) => {
              this.$set(this.hValue, 'turnFormal4', res.data.data || '');
              this.$set(this.loading, 'turnFormal4', false);
            })
            .catch(() => {
              this.$set(this.loading, 'turnFormal4', false);
            });
        }
      },
      validateTurnFormal4(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('转正时间不能早于入职时间'));
          return;
        }
        callback();
      },
      validateTime() {
        let { turnFormal2, turnFormal4 } = this.hValue;
        if (!turnFormal2 || !turnFormal4) {
          return true;
        }
        let startDate = str2Date(turnFormal2);
        let endDate = str2Date(turnFormal4);
        return startDate.getTime() <= endDate.getTime();
      },
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
