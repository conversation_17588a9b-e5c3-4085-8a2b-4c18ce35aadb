<template>
  <div class="formula">
    <el-input
      v-model="formula"
      @focus="showDialog"
      size="small"
      readonly
      placeholder="计算公式="
    ></el-input>
    <el-dialog
      :visible.sync="dialogVisible"
      append-to-body
      :before-close="closeDialog"
      width="484px"
      class="computedFormal"
      title="编辑计算公式"
    >
      <el-form>
        <el-form-item label="计算公式：">
          <div class="content" :class="{ contentError: errorTip }">
            <div>
              <span class="info">计算公式=</span>
              <div
                v-for="(key, i) of formulaValue"
                :key="i"
                class="cell cellContent"
              >
                {{ key.label }}
              </div>
            </div>
            <span class="clear">
              <img v-oss @click="deleteBox" src="/oa/delete.png" alt="" />
              <span @click="clearValue">清空</span>
            </span>
          </div>
          <span v-if="errorTip" class="errorTip">
            <i class="el-icon-warning"></i>
            编辑的计算公式为空或不符合计算法则，无法计算</span
          >
        </el-form-item>
        <el-form-item label="控件：">
          <div
            v-for="(li, i) of selectComponent"
            :key="i"
            @click="setValue(li.id, li.valueJson.name)"
            class="cell"
          >
            {{ li.valueJson.name }}
          </div>
        </el-form-item>
        <el-form-item label="符号：">
          <div @click="setValue('+')" class="cell">+</div>
          <div @click="setValue('-')" class="cell">-</div>
          <div @click="setValue('*')" class="cell">*</div>
          <div @click="setValue('/')" class="cell">/</div>
          <div @click="setValue('(')" class="cell">(</div>
          <div @click="setValue(')')" class="cell">)</div>
        </el-form-item>
        <el-form-item label="数字：">
          <div
            v-for="key in 9"
            :key="key"
            @click="setValue(`${key}`)"
            class="cell"
          >
            {{ key }}
          </div>
          <div @click="setValue('0')" class="cell">0</div>
          <div @click="setValue('.')" class="cell">.</div>
        </el-form-item>
      </el-form>
      <div class="actions">
        <el-button @click="closeDialog">返回</el-button>
        <el-button @click="confirm" type="primary">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { deepClone } from '@/util/util.js';

  export default {
    props: {
      value: {
        type: Array,
        default: () => []
      },
      dataList: {
        type: Array,
        default: () => []
      },
      id: {
        type: String,
        default: ''
      },
      formulaName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        dialogVisible: false,
        formulaValue: [],
        formula: '',
        selectComponent: [],
        errorTip: false
      };
    },
    watch: {
      dialogVisible() {
        if (this.dialogVisible) {
          this.formulaValue = deepClone(this.value);
          let list = [];
          this.dataList.forEach((comp) => {
            if (this.isSelectable(comp)) {
              list.push(comp);
            } else if (
              'form' === comp.type &&
              comp.children.find((item) => item.id === this.id)
            ) {
              comp.children.forEach((item) => {
                if (this.isSelectable(item)) {
                  list.push(item);
                }
              });
            }
          });
          this.selectComponent = list;
        }
      }
    },
    mounted() {
      this.formula = this.formulaName;
    },
    methods: {
      showDialog() {
        this.dialogVisible = true;
      },
      deleteBox() {
        this.formulaValue.pop();
      },
      clearValue() {
        this.formulaValue = [];
      },
      setValue(value, label) {
        this.formulaValue.push({ value, label: label || value });
      },
      confirm() {
        this.errorTip = false;
        const valueData = deepClone(this.formulaValue);
        let num = 0;
        valueData.forEach((l, i) => {
          // 控件前后必须有字符
          if (
            l.value.indexOf('inputMoney') > -1 ||
            l.value.indexOf('inputNumber') > -1 ||
            l.value.indexOf('computed') > -1
          ) {
            if (
              i + 1 < valueData.length &&
              !['+', '-', '*', '/', ')'].includes(valueData[i + 1].value)
            ) {
              this.errorTip = true;
            } else if (
              i - 1 > -1 &&
              !['+', '-', '*', '/', '('].includes(valueData[i - 1].value)
            ) {
              this.errorTip = true;
            } else {
              l.value = '1';
            }
          }

          // 括号数必须一致
          if (l.value === '(') {
            num++;
          } else if (l.value === ')') {
            num--;
          }
        });
        num && (this.errorTip = true);

        if (this.errorTip) {
          return;
        }

        let reg = /^(\(*\d+(.\d+)*\)*(\+|-|\/|\*))+\d+(.\d+)*\)*$/;
        const value = valueData.map((f) => f.value).join('');
        if (reg.test(value)) {
          this.formula = valueData.map((l) => l.label).join('');
          this.errorTip = false;
          this.$emit('input', deepClone(this.formulaValue));
          this.$emit('formula', this.formula);
          this.closeDialog();
        } else {
          this.errorTip = true;
        }
      },
      closeDialog() {
        this.errorTip = false;
        this.formulaValue = [];
        this.dialogVisible = false;
      },
      isSelectable(comp) {
        return (
          ['inputMoney', 'computed', 'inputNumber'].includes(comp.type) &&
          comp.id !== this.id
        );
      }
    }
  };
</script>
<style lang="scss">
  .formula {
    input {
      cursor: pointer;
    }
  }

  .computedFormal {
    .el-dialog__body {
      padding: 24px 0 24px 24px !important;
    }

    .el-form-item {
      display: flex;
      margin-bottom: 0;
    }

    .el-form-item__label {
      width: 86px !important;
    }

    .el-form-item__content {
      position: relative;
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      line-height: inherit;

      .cell {
        box-sizing: border-box;
        min-width: 48px;
        max-width: 106px;
        height: 36px;
        margin-right: 12px;
        margin-bottom: 12px;
        padding: 0 8px;
        overflow: hidden;
        line-height: 36px;
        white-space: nowrap;
        text-align: center;
        text-overflow: ellipsis;
        background: #f7f8fa;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background: #f0f2f5;
        }
      }

      .cellContent {
        min-width: 28px;
        margin-right: 8px;
      }

      .errorTip {
        position: absolute;
        bottom: 0;

        .el-icon-warning {
          margin-right: 4px;
          color: #ff5151;
        }
      }
    }

    .content {
      position: relative;
      box-sizing: border-box;
      width: 346px;
      height: 120px !important;
      margin-bottom: 24px;
      padding: 12px;
      padding-bottom: 40px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;

      & > div {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 100%;
        overflow-y: auto;
        line-height: 28px;
      }

      .info {
        margin-right: 8px;
      }

      .cell {
        height: 28px;
        line-height: 28px;
      }

      .clear {
        position: absolute;
        right: 16px;
        bottom: 7px;
        display: flex;
        align-items: center;
        line-height: inherit !important;
        cursor: pointer;

        img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }
    }

    .contentError {
      border-color: #ff5151;
    }

    .actions {
      margin-top: 12px;
      text-align: center;

      button {
        width: 140px;
      }
    }
  }
</style>
