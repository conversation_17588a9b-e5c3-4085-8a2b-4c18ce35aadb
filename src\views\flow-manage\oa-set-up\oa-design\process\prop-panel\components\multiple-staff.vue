<template>
  <el-dialog
    :title="title"
    :visible.sync="rangeVisible"
    width="544px"
    :append-to-body="true"
    custom-class="range"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeName" @tab-click="changeTabs" class="tab-type">
      <el-tab-pane label="成员" name="people"></el-tab-pane>
      <el-tab-pane label="部门" name="dept"></el-tab-pane>
      <el-tab-pane v-if="isHide" label="岗位" name="position"></el-tab-pane>
      <!-- <el-tab-pane v-if="isHide" label="职务" name="job"></el-tab-pane> -->
    </el-tabs>
    <div v-loading="loading" class="content">
      <select-tree
        :checked-list.sync="searchList"
        :tree-data="treeData"
        :radio="activeName"
        :check-strictly="checkStrictly"
        :max-select-length="20"
      ></select-tree>
    </div>
    <div class="footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button @click="save" type="primary">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { deepClone } from '@/util/util.js';
  import selectTree from './select-tree';
  import { getList } from '@/api/system/post';
  import { jobList } from '@/api/system/company';
  import { getTreeList, getStaffTreeList } from '@/api/system/dept';
  export default {
    components: { selectTree },
    props: {
      rangeVisible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '选择'
      },
      ids: {
        type: Array,
        default: () => []
      },
      radio: {
        type: String,
        default: 'people'
      },
      isHide: {
        type: Boolean,
        default: true
      },
      orgId: String
    },
    data() {
      return {
        activeName: 'people',
        searchList: [],
        treeData: [],
        loading: false,
        isDept: true,
        checkStrictly: false
      };
    },
    watch: {
      rangeVisible: {
        handler(val) {
          if (val) {
            this.searchList = this.ids || [];
          } else {
            this.activeName = 'people';
            this.getData();
          }
        },
        immediate: true
      }
    },
    created() {
      this.getData();
    },
    methods: {
      hasPeople(iarr) {
        function treeFilter(tree, func) {
          // 使用map复制一下节点，避免修改到原树
          return tree
            .map((node) => ({ ...node }))
            .filter((node) => {
              node.children = node.children && treeFilter(node.children, func);
              return func(node) || (node.children && node.children.length);
            });
        }
        return treeFilter(iarr, (item) => item.dataType === 3);
      },
      async getData() {
        let data = {};
        this.loading = true;
        try {
          switch (this.activeName) {
            case 'people':
              data = await getStaffTreeList(null, null, this.orgId);
              break;
            case 'dept':
              data = await getTreeList(this.orgId);
              break;
            case 'position':
              data = await getList(this.orgId);
              break;
            case 'job':
              data = await jobList(this.orgId);
              break;
          }
          if (data && data.data && data.data.success) {
            let arr = data.data.data;
            // let result = this.hasPeople(arr);
            this.treeData = arr;
          } else if (data && data.data && !data.data.success) {
            this.$message.error(data.data.message);
          }
          this.loading = false;
        } catch {
          this.treeData = [];
          this.loading = false;
        }
      },
      changeTabs(tab) {
        this.activeName = tab.name;
        this.getData();
      },
      save() {
        const list = deepClone(this.searchList);
        this.$emit('rangeSave', list);
        this.handleClose();
      },
      handleClose() {
        this.searchList = [];
        this.$emit('update:rangeVisible', false);
      }
    }
  };
</script>
<style lang="scss">
  .el-dialog__wrapper .range .el-dialog__header {
    padding-bottom: 17px !important;
  }

  .range {
    .tab-type {
      .el-tabs__header {
        margin-top: 0 !important;
        margin-bottom: 0 !important;

        .el-tabs__nav-wrap {
          padding: 0;

          .el-tabs__nav {
            .el-tabs__item {
              height: 45px;
              padding: 0 13px;
              line-height: 45px;
            }
          }

          &::after {
            background: none;
            border: none;
          }
        }
      }
    }

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      padding: 0 !important;

      .header-tip {
        margin-bottom: 4px;
        padding-bottom: 16px;
        padding-left: 24px;
        border-bottom: 1px solid #d9d9d9;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88px;

      button {
        width: 140px;
        height: 40px;
      }
    }
  }
</style>
<style lang="scss" scoped>
  .range {
    .tab-type {
      position: absolute;
      box-sizing: border-box;
      width: 240px;
      margin-top: 12px;
      margin-left: 24px;
      padding: 0 12px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
</style>
