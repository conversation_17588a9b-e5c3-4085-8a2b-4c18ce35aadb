<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    :component-list="componentList"
    :disabled="disabled"
    :rules="rules"
    :loading="loading"
  />
</template>

<script>
  import { getTimeInterval } from '@/api/desk/flow';
  import { deepClone } from '@/util/util';
  import { DATE_AND_HALF_DAY } from '@/const/validator';
  import { str2Date } from '@/util/date';

  export default {
    name: 'OutComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        hour: undefined,
        loading: {
          out3: false
        }
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      rules() {
        let rules = {
          out1: [{ required: true, message: '请选择' }],
          out2: [{ required: true, message: '请选择' }],
          out3: [{ validator: this.validateOut3 }]
        };
        if (this.data.dateType === 2) {
          rules.out1.push(DATE_AND_HALF_DAY);
          rules.out2.push(DATE_AND_HALF_DAY);
        }
        rules.out1.push({ validator: this.validateOut1 });
        rules.out2.push({ validator: this.validateOut2 });
        return rules;
      }
    },
    watch: {
      'hValue.out1'() {
        this.setOut3();
      },
      'hValue.out2'() {
        this.setOut3();
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        let unit = this.data.dateType;
        let componentList = deepClone(this.componentList);
        componentList[0].valueJson.dateType = unit;
        componentList[1].valueJson.dateType = unit;
        componentList[2].valueJson.unit = unit === 3 ? '小时' : '天';
        this.$emit('update:component-list', componentList);
        this.setOut3();
      },
      setOut3() {
        this.$set(this.hValue, 'out3', '');
        this.hour = undefined;
        let { out1, out2 } = this.hValue;
        if (out1 && out2) {
          if (out1 === out2) {
            this.hour = 0;
          } else if (this.validateTime()) {
            this.$set(this.loading, 'out3', true);
            this.hour = -1;
            getTimeInterval({
              flowType: 4,
              beginTime: out1,
              endTime: out2,
              dateUnit: this.data.dateType
            })
              .then((res) => {
                this.$set(this.loading, 'out3', false);
                let { hour } = res.data.data || {};
                this.hour = hour;
                if (hour || hour === 0) {
                  this.$set(this.hValue, 'out3', hour + '');
                }
              })
              .catch(() => {
                this.$set(this.loading, 'out3', false);
                this.hour = -2;
              });
          }
        }
      },
      validateOut1(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('开始时间不得晚于结束时间'));
          return;
        }
        callback();
      },
      validateOut2(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('结束时间不得早于开始时间'));
          return;
        }
        callback();
      },
      validateOut3(rule, value, callback) {
        let { out1, out2 } = this.hValue;
        if (out1 && out2) {
          if (this.hour === -1) {
            callback(new Error('正在计算时长，请等待'));
            return;
          }
          if (this.hour === -2) {
            callback(new Error('时长计算错误，请稍后重试'));
            return;
          }
          if (this.hour <= 0) {
            callback(new Error('时长必须大于0'));
            return;
          }
        }
        callback();
      },
      validateTime() {
        let { out1, out2 } = this.hValue;
        if (!out1 || !out2) {
          return true;
        }
        if (this.data.dateType === 2) {
          out1 = out1.replace('上午', '00:00:00').replace('下午', '12:00:00');
          out2 = out2.replace('上午', '00:00:00').replace('下午', '12:00:00');
        }
        let startDate = str2Date(out1);
        let endDate = str2Date(out2);
        return startDate.getTime() <= endDate.getTime();
      },
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
