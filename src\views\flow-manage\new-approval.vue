<template>
  <h-block title="发起审批" class="newApproval">
    <ul>
      <li v-for="({ groupName, definitionList }, i) of oaList" :key="i">
        <div class="header">{{ groupName }}</div>
        <div class="content-container">
          <div
            v-for="({ processName, icon, id }, ci) of definitionList"
            :key="ci"
            @click="approvalLink(id)"
            class="content"
          >
            <img v-oss :src="icon || '/oa/icon_diy_3.png'" />
            <div>
              <h4>{{ processName }}</h4>
              <span style="font-size: 14px">发起{{ processName }}申请</span>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </h-block>
</template>
<script>
  import './approval/index.scss';
  import { processStartList } from '@/api/flow/process';

  export default {
    data() {
      return {
        oaList: []
      };
    },
    created() {
      let isForm = 0;
      processStartList(isForm).then((res) => {
        if (res && res.data && res.data.success) {
          this.oaList = res.data.data;
        }
      });
    },
    methods: {
      approvalLink(id) {
        this.$router.push({ name: 'launchApproval', query: { id } });
      }
    }
  };
</script>
