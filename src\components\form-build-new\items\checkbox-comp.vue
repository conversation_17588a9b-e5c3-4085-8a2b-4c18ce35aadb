<template>
  <h-select
    v-model="hValue"
    :data-source="options"
    :props="props"
    multiple
    :placeholder="data.placeholder"
    clearable
  />
</template>

<script>
  export default {
    name: 'CheckboxComp',
    props: {
      value: {
        type: Array
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        props: { label: 'value' }
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      options() {
        return this.data.options || [];
      }
    }
  };
</script>
