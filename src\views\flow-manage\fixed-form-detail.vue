<template>
  <h-block :title="$route.meta.title" class="approval-apply">
    <div class="apply-info">
      <header>审批内容</header>
      <!-- 固定表单页面 -->
      <FormDetail :form="testPluginForm" />
    </div>
    <apply-log
      @refresh="resetDetails"
      :process-instance-id="dataObj.processInstanceId"
      title="审批日志"
    ></apply-log>
    <div class="footer-btn">
      <el-button @click="cancel" class="cancle">返 回</el-button>
    </div>
  </h-block>
</template>
<script>
  import './approval/index.scss';
  import { processDetail } from '@/api/flow/process';
  import applyLog from '@/components/apply-log';
  import FormDetail from './form-detail.vue';
  export default {
    components: {
      applyLog,
      FormDetail
    },
    data() {
      return {
        id: undefined,
        dataObj: {},
        testPluginForm: {}
      };
    },
    created() {
      this.id = this.$route.query.id;
      this.resetDetails();
    },
    methods: {
      resetDetails() {
        const loading = this.$loading();
        processDetail(this.id)
          .then((res) => {
            this.$route.meta.title = res.data.data.processName;
            this.$router.$avueRouter.setTitle(res.data.data.processName, false);
            this.getDetail(res.data.data);
          })
          .finally(() => {
            loading.close();
          });
      },
      getDetail(data) {
        this.dataObj = data;
        this.testPluginForm = data.testPluginForm;
      },
      findTag(value) {
        let tag, key;
        let tagList = this.$store.state.tags.tagList;
        tagList.map((item, index) => {
          if (item.value === value) {
            tag = item;
            key = index;
          }
        });
        return { tag: tag, key: key };
      },
      cancel() {
        let { tag } = this.findTag(this.$route.fullPath);
        this.$store.commit('DEL_TAG', tag);
        setTimeout(() => {
          this.$router.push('/flow-manage/fixed-flow-demo');
        }, 500);
      }
    }
  };
</script>
