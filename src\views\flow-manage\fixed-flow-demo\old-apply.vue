<template>
  <h-block :title="$route.meta.title" class="approval-apply">
    <div class="apply-info">
      <img v-oss :src="imgUrl" class="apply-status" />
      <header>审批内容</header>
      <div
        v-for="({ label, value, type }, i) of list"
        :key="i"
        class="content"
        :class="{ attach: type === 'file' }"
      >
        <template v-if="type === 'file'">
          <div>附件：</div>
          <h-upload
            class="fileUrl"
            :value="Array.from(value || [], (item) => item.fileUrl)"
            show-file-list
            multiple
            disabled
          />
        </template>
        <template v-else>
          <div>{{ label }}：</div>
          <div>{{ value | noStatus }}</div>
        </template>
      </div>
    </div>
    <div v-if="isDetail" class="detailLink">
      <span @click="goDetail">查看业务详情</span>
    </div>
    <apply-log :process-instance-id="id"></apply-log>
  </h-block>
</template>
<script>
  import './index.scss';
  import { oldProcessDetail } from '@/api/flow/process';
  import applyLog from '@/components/apply-log/old-apply-log';

  const ID_TO_LINK = new Map([
    [55, 'contractDetail'],
    [58, 'contractDetail'],
    [53, 'authorizationView'],
    [54, 'decisionDetail'],
    [57, 'viewWill'],
    [52, 'invoiceDetail'],
    [50, 'receiptDetail'],
    [51, 'payDetail'],
    [56, 'inventoryCheckDetail']
  ]);

  export default {
    components: { applyLog },
    filters: {
      noStatus(value) {
        return value || '--';
      }
    },
    props: { id: String },
    data() {
      return {
        list: [],
        file: [],
        imgUrl: '',
        status: '',
        activities: [],
        categoryId: '',
        businessId: ''
      };
    },
    computed: {
      isDetail() {
        return ID_TO_LINK.has(this.categoryId);
      }
    },
    beforeRouteEnter: (to, from, next) => {
      next((vm) => {
        const loading = vm.$loading();
        oldProcessDetail(vm.$route.params.id)
          .then((res) => {
            if (res && res.data && res.data.success) {
              vm.$route.meta.title = res.data.data.processDefinitionName;
              vm.$router.$avueRouter.setTitle(
                res.data.data.processDefinitionName,
                false
              );
              vm.getDetail(res);
            }
          })
          .finally(() => {
            loading.close();
          });
      });
    },
    methods: {
      download(url) {
        window.open(url, '_blank');
      },
      goDetail() {
        this.$router.push({
          name: ID_TO_LINK.get(this.categoryId),
          query: { id: this.businessId }
        });
      },
      getDetail(res) {
        /**
         * sysKey系统默认json
         * customKey用户自定义key
         * formKv用户输入的对应字段的值
         * historyList流程节点
         */
        const data = res.data.data;
        this.status = data.status;
        this.categoryId = data.categoryId;
        this.businessId = data.businessId;
        this.imgUrl = `/apply/apply-${data.status || 0}.png`;
        this.list = [
          ...JSON.parse(data.sysKey || '[]'),
          ...JSON.parse(data.customKey || '[]')
        ];
        const formKvValue = JSON.parse(data.formKv || '{}');
        this.list = this.list.map((l) => {
          return {
            ...l,
            value: formKvValue[l.prop]
          };
        });
        this.activities = (data.historyList || []).map((h) => {
          const flag = ['yes', 'no', 'recall'];
          const name = ['通过', '拒绝', '待审批'];
          const icon = `iconfont icon-process_${flag[h.status]}`;
          return {
            status: h.status,
            content: h.historyActivityName,
            timestamp: h.endTime,
            createUserPhoto: h.createUserPhoto,
            icon,
            processName: name[h.status],
            name: h.assigneeName,
            comment: h.comment
          };
        });
        if (data.status === 1 && data.informUsers.length) {
          const avatars = data.informUsers.map((r) => {
            return { name: r.nickName, avatar: r.avatar };
          });
          this.activities.push({ content: '抄送人', avatars });
        }
      }
    }
  };
</script>
