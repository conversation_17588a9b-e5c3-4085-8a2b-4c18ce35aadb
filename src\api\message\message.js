import request from '@/router/axios';

export const getCountNotRead = (current, size, params) => {
  return request({
    url: '/api/message/countNotReadMessage',
    method: 'get',
    params: {
      ...params,
      current,
      size
    },
    hideProgress: true
  });
};

export const readMessage = (row) => {
  return request({
    url: '/api/message/readMessage',
    method: 'post',
    data: row
  });
};

export const pushMessage = (row) => {
  return request({
    url: '/api/message/pushMessage',
    method: 'post',
    data: row
  });
};

// 消息推送 - 分页列表
export const getPage = ({ current, size, ...params }) => {
  return request({
    url: '/api/message/pageSend',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};
// 消息推送 - 新增/编辑
export const postMessage = (row) => {
  return request({
    url: '/api/message/commitMessage',
    method: 'post',
    data: row
  });
};

// 消息推送 - 详情
export const getDetail = (messageId) => {
  return request({
    url: `/api/message/detail/${messageId}`,
    method: 'get'
  });
};

// 消息推送 - 删除
export const postDel = (params) => {
  return request({
    url: '/api/message/deleteMessage',
    method: 'post',
    params
  });
};

// 消息管理 - 分页列表
export const getReceivePage = ({ current, size, ...params }) => {
  return request({
    url: '/api/message/pageReceive',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};
// 消息管理 - 消息已读
export const postMsgRead = (data) => {
  return request({
    url: '/api/message/readMessage',
    method: 'post',
    data
  });
};
// 消息管理 - 全部已读
export const getMsgAllRead = (type) => {
  return request({
    url: `/api/message/allReadMessage?type=${type}`,
    method: 'get'
  });
};
