export default {
  start: {
    type: 'start',
    content: '发起者',
    properties: { title: '发起', initiator: 'ALL' }
  },
  approverPerson: {
    isSelf: true,
    type: 'approver',
    content: '发起人自选',
    properties: { title: '审批' }
  },
  // copyPeople: {
  //   type: "approver",
  //   isCC: true,
  //   content: "请设置抄送人",
  //   properties: { title: "抄送" },
  // },
  approver: {
    type: 'approver',
    content: '请设置审批人',
    properties: { title: '审批' }
  },
  copy: {
    type: 'copy',
    content: '请设置抄送人',
    isCC: true,
    properties: {
      title: '抄送',
      menbers: [],
      userOptional: true
    }
  },
  condition: {
    type: 'condition',
    content: '条件: 请设置',
    properties: {
      title: '条件',
      conditions: [],
      initiator: null,
      isDefault: false
    }
  },
  branch: { type: 'branch', content: '', properties: {} },
  empty: { type: 'empty', content: '', properties: {} }
};
