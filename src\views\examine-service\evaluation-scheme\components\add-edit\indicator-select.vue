<template>
  <el-dialog
    width="65%"
    :visible="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :title="isDetail ? '查看指标' : '选择指标'"
    @close="handleCancel"
  >
    <!-- 主体 -->
    <el-row :gutter="16">
      <el-tooltip
        placement="top"
        :disabled="!deptText.length"
        :content="deptText"
      >
        <el-col :span="17" class="text_ellipsis"
          >被评价机构: {{ deptText }}</el-col
        >
      </el-tooltip>
      <el-col
        :span="6"
        :offset="1"
        class="text_ellipsis"
        style="min-width: 150px; text-align: right"
        >总分: {{ totalScore }}</el-col
      >
    </el-row>
    <template v-if="!isDetail">
      <el-button
        plain
        type="primary"
        icon="el-icon-plus"
        size="mini"
        style="margin-top: 12px; margin-right: 10px"
        @click="handleAdd"
        >新增
      </el-button>
      <yk-select-user
        append-to-body
        btnTitle="批量设置评价人"
        modelTitle="批量设置评价人"
        :disabled="!multipleSelection.length"
        @result="handleBatchUserSelect"
      />
    </template>
    <el-table
      size="small"
      style="width: 100%; margin-top: 12px"
      v-loading="loading"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="!isDetail"
        align="center"
        type="selection"
        width="60"
      />
      <el-table-column align="center" type="index" label="序号" width="60" />
      <el-table-column
        align="center"
        prop="indexId"
        label="指标名称"
        width="160px"
      >
        <template slot-scope="{ row, $index }">
          <el-select
            :disabled="isDetail"
            filterable
            v-model="row.indexId"
            placeholder="请选择指标"
            size="small"
            style="width: 100%"
            @change="(val) => handleNameChange(val, $index)"
          >
            <el-option
              v-for="dict in indicatorOptions"
              :key="dict.id"
              :label="dict.indexName"
              :value="dict.id"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="standard"
        label="考核目标及计分标准"
        min-width="100"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="scoreTypeId"
        label="打分方式"
        width="160px"
      >
        <template slot-scope="{ row, $index }">
          <el-select
            :disabled="isDetail"
            v-model="row.scoreTypeId"
            placeholder="请选择打分方式"
            size="small"
            style="width: 100%"
            @change="(val) => handleScoreWayChange(val, $index)"
          >
            <el-option
              v-for="dict in scoreTypeOptions"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="basicScore"
        label="基本分"
        width="100px"
      >
        <template slot-scope="{ row }">
          <el-input
            size="small"
            type="number"
            :min="0"
            v-model="row.basicScore"
            :disabled="isDetail || isHundred(row.scoreTypeId)"
          />
        </template>
      </el-table-column>
      <el-table-column
        v-if="deptHaveNormalFlag"
        align="center"
        prop="assessorUserList"
        label="评价人"
        min-width="300px"
        show-overflow-tooltip
      >
        <template slot-scope="{ row, $index }">
          <!-- 实现单击输入框可以更改领导: 将领导选择按钮隐藏,  单击输入框时触发领导选择按钮单击事件 -->
          <yk-select-user
            :ref="`assessorUserList${$index}`"
            v-show="false"
            modelTitle="选择评价人"
            :userArr="row.assessorUserList"
            @result="(arr) => handleUserSelect(arr, row)"
            append-to-body
          />
          <el-tooltip
            effect="dark"
            placement="top"
            :disabled="!row.assessorUserList.length"
            :content="transferAryToString(row.assessorUserList)"
          >
            <div
              class="dialog-div-wrapper"
              placeholder="请选择评价人"
              :class="isDetail && 'disabled'"
              @click="!isDetail && handleAssessorUserClick($index)"
            >
              {{ transferAryToString(row.assessorUserList) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isDetail"
        fixed="right"
        align="center"
        label="操作"
        width="80"
      >
        <template slot-scope="{ $index }">
          <el-popconfirm
            style="margin-left: 8px"
            title="确定 删除 此数据吗？"
            @confirm="() => handleDelete($index)"
          >
            <el-button
              type="text"
              size="mini"
              icon="el-icon-delete"
              class="button-del"
              slot="reference"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗按钮 -->
    <div slot="footer" style="text-align: center">
      <el-button class="cancle" size="small" @click="handleCancel"
        >返回</el-button
      >
      <el-button
        v-if="!isDetail"
        type="primary"
        size="small"
        @click="handleSubmit"
        class="confirm"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { getDeptHaveUser } from '@/api/common';
  import { getList as getIndicatorList } from '@/api/examine/indicator-library';
  import { getList as getScoreTypeList } from '@/api/examine/scoring-method';
  import { checkTableField, transferAryToString } from '../util';
  // 用户选择
  import { YkSelectUser } from '@/components/yk-select-org';

  export default {
    name: 'indicator-select',
    components: { YkSelectUser },
    props: {
      isDetail: {
        type: Boolean,
        default: false
      },
      rowData: {
        type: Object,
        default() {
          return {};
        }
      },
      dimension: {
        type: String,
        default: ''
      }
    },
    watch: {
      rowData: {
        handler(newVal) {
          if (newVal.schemeItemIndexList && newVal.schemeItemIndexList.length) {
            this.tableData = cloneDeep(newVal.schemeItemIndexList);
          }
        },
        immediate: true,
        deep: true
      }
    },
    data() {
      return {
        loading: false,
        // 批量选择评价人
        multipleSelection: [],
        // 指标库
        indicatorOptions: [],
        // 打分方式
        scoreTypeOptions: [],
        tableData: [],
        // 部门下是否有普通用户
        deptHaveNormalFlag: true
      };
    },
    async created() {
      this.loading = true;
      await this.getIndicatorListData();
      await this.getScoreTypeListData();
      await this.checkDept();
      this.loading = false;
    },
    methods: {
      transferAryToString,
      // 校验部门下是否有普通员工
      async checkDept() {
        if (this.dimension !== '2') return false;
        const deptId = this.rowData.assessedDeptList[0].id;
        const { data } = await getDeptHaveUser({ deptId });
        this.deptHaveNormalFlag = data.data;
      },
      // 批量选择评价人回调
      handleBatchUserSelect(userArr) {
        this.multipleSelection.forEach((item) => {
          item.assessorUserList = userArr;
        });
      },
      // 表格多选变化回调
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      // 获取指标库
      async getIndicatorListData() {
        try {
          const res = await getIndicatorList({ current: 1, size: 100 });
          const data = res.data.data;
          this.indicatorOptions = data.records;

          if (this.tableData.length) {
            this.tableData.forEach((item) => {
              const _index = this.indicatorOptions.find(
                (index) => index.id === item.indexId
              );
              item.standard = _index.indexStandard;
            });
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 获取打分方式
      async getScoreTypeListData() {
        try {
          const res = await getScoreTypeList({ current: 1, size: 100 });
          const data = res.data.data;
          this.scoreTypeOptions = data.records;
        } catch (e) {
          console.error(e);
        }
      },
      // 新增
      handleAdd() {
        const rowschemeItemIndexList = {
          indexId: undefined,
          indexName: undefined,
          standard: undefined,
          scoreTypeId: this.scoreTypeOptions.length
            ? this.scoreTypeOptions[0].id
            : undefined,
          basicScore: 100,
          assessorUserList: []
        };
        this.tableData.push(rowschemeItemIndexList);
      },
      // 指标名称更改
      handleNameChange(val, index) {
        this.tableData[index].indexId = val;
        const selectedOption = this.indicatorOptions.find(
          (item) => item.id === val
        );
        this.tableData[index].indexName = selectedOption.indexName;
        this.tableData[index].standard = selectedOption.indexStandard;
      },
      // 打分方式更改
      handleScoreWayChange(val, index) {
        this.tableData[index].scoreWay = val;
        const selectedOption = this.scoreTypeOptions.find(
          (item) => item.id === val
        );
        this.tableData[index].basicScore = selectedOption.maxScore;
        this.tableData[index].scoreSystem = selectedOption.scoreSystem;
        // 如果是百分制, 将基本分置为100, 如果是任意分, 将基本分置为0, 如果是基本分, 将基本分置空
        if (selectedOption.scoreSystem === '1') {
          this.tableData[index].basicScore = 100;
        } else if (selectedOption.scoreSystem === '5') {
          this.tableData[index].basicScore = 0;
        }
      },
      // 如果打分方式是百分制, 基本分展示100, 不可编辑
      isHundred(id) {
        const selectedOption = this.scoreTypeOptions.find(
          (item) => item.id === id
        );
        return selectedOption && selectedOption.scoreSystem === '1';
      },
      // 被评价人单击
      handleAssessorUserClick(index) {
        let btn = this.$refs[`assessorUserList${index}`];
        if (btn) {
          btn.visible = true;
        }
      },
      // 人员选择
      handleUserSelect(userArr, row) {
        row.assessorUserList = userArr;
      },
      // 删除行
      handleDelete(index) {
        this.tableData.splice(index, 1);
      },
      // 返回
      handleCancel() {
        this.$emit('dialogClose');
      },
      // 保存
      handleSubmit() {
        // 至少一条数据
        if (!this.tableData.length)
          return this.$message.warning('请配置至少一条指标');

        // 基本分不能为空
        if (
          this.tableData.some(
            (item) => !item.basicScore && item.basicScore !== 0
          )
        )
          return this.$message.warning('基本分不能为空');

        // 校验数据必填项
        const fieldObj = {
          indexId: '请选择指标',
          scoreTypeId: '请选择打分方式'
        };
        if (this.deptHaveNormalFlag) {
          fieldObj.assessorUserList = '请选择至少一个评价人';
        }
        const { flag, index, msg } = checkTableField(this.tableData, fieldObj);
        if (!flag)
          return this.$message.warning(`第 ${index + 1} 行数据有误: ${msg}`);
        // 向父级传递数据
        this.$emit('dialogSave', {
          data: this.tableData,
          totalScore: this.totalScore
        });
      }
    },
    computed: {
      totalScore() {
        return this.tableData.reduce(
          (total, cur) => total + +cur.basicScore,
          0
        );
      },
      deptText() {
        return transferAryToString(this.rowData.assessedDeptList);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .dialog-div-wrapper {
    height: 28px;
    line-height: 26px;
    font-size: 13px;
    padding: 0 5px 0 15px;
    text-align: left;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    user-select: none;

    &:empty:before {
      content: attr(placeholder);
      color: #c0c4cc;
    }

    &.disabled {
      background-color: #f5f7fa;
      border-color: #dfe4ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
</style>
