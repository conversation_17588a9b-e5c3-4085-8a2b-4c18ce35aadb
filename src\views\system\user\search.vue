<template>
  <div>
    <el-form
      ref="form"
      :model="queryParams"
      label-suffix=": "
      :inline="true"
      :size="size"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="登录账号" prop="account">
            <el-input
              v-model="queryParams.account"
              placeholder="请输入登录账号"
              clearable
              :style="inputStyle"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="用户姓名" prop="realName">
            <el-input
              v-model="queryParams.realName"
              placeholder="请输入用户姓名"
              clearable
              :style="inputStyle"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="用户平台" prop="userType">
            <el-select
              v-model="queryParams.userType"
              placeholder="请选择用户平台"
              :style="inputStyle"
            >
              <el-option
                v-for="dict in systemDicts.type.user_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetQuery"
              >清空</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
  export default {
    name: 'UserSearch',
    systemDicts: ['user_type'],
    props: {
      size: {
        type: String,
        default: 'small'
      }
    },
    data() {
      return {
        queryParams: {
          account: '',
          realName: '',
          userType: undefined
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        this.$emit('search', this.queryParams);
      }
    },
    computed: {
      inputStyle() {
        return this.size === 'mini' ? { width: '130px' } : null;
      }
    }
  };
</script>
<style lang=""></style>
