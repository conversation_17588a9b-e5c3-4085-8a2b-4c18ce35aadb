<template>
  <component
    :is="componentId"
    v-if="visible === true"
    @add-list="addList"
    @update-field-status="updateFieldStatus"
    :visible.sync="visible"
    :value.sync="value"
    :field-status="fieldStatus"
  ></component>
</template>
<script>
  import condition from './condition/condition';
  import approver from './approver/approver';
  export default {
    components: { condition, approver },
    props: {
      value: {
        // 当前节点数据
        type: Object,
        default: () => {}
      },
      processData: {
        // 当前节点数据
        type: Object,
        default: () => {}
      },
      isModel: {
        type: Boolean,
        default: false
      },
      fieldStatus: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        componentId: '',
        visible: false
      };
    },
    watch: {
      visible(v) {
        !v && this.$emit('cancel');
      },
      value() {
        // 根据当前节点数据判断弹框内容
        // 空节点
        if (!this.value) {
          return;
        }
        // 开始节点
        if (this.value.type === 'start') {
          return;
        }
        // 条件节点而且为其他条件
        if (
          this.value.type === 'condition' &&
          this.value.properties.isDefault
        ) {
          return;
        }
        this.componentId =
          this.value.type === 'copy' ? 'approver' : this.value.type;
        this.visible = true;
      }
    },
    methods: {
      addList(l) {
        this.$emit('confirm', l);
      },
      updateFieldStatus(status) {
        this.$emit('status', status);
      }
    }
  };
</script>
