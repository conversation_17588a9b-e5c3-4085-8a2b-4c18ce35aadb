<template>
  <div class="bc-form-build-input-number">
    <el-col :span="12">
      <el-form-item :prop="`${prop}.value`" :label="`${data.name}(元)：`">
        <h-input-number
          v-model="hValue"
          :min="0"
          :max="9999999999.99"
          :step="1"
          :precision="2"
          :placeholder="data.placeholder"
          clearable
        />
      </el-form-item>
    </el-col>
    <el-col v-if="data.capital" :span="12">
      <el-form-item label="大写：">
        <div class="interval">{{ value.text || '--' }}</div>
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
  import { numToCny } from '@/util/util';

  export default {
    name: 'InputMoneyComp',
    props: {
      value: {
        type: Object,
        default() {
          return { value: '', text: '' };
        }
      },
      prop: {
        type: String,
        default: ''
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    computed: {
      hValue: {
        get() {
          if (!this.value || !this.value.value) {
            return undefined;
          }
          let value = this.value.value;
          if (typeof value === 'string') {
            return parseFloat(value);
          }
          return value;
        },
        set(val) {
          if (val === undefined) {
            this.$emit('input', { value: '', text: '' });
            return;
          }
          let value = val + '';
          if (value) {
            if (!value.includes('.')) {
              value += '.00';
            }
            this.$emit('input', { value, text: val ? numToCny(val) : '' });
          }
        }
      }
    },
    created() {
      if (!this.value.value) {
        this.$emit('input', { value: undefined, text: '' });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .bc-form-build-input-number {
    .interval {
      line-height: 32.4px;
    }
  }
</style>
