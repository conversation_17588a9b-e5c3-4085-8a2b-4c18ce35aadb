<template>
  <div>
    <apply-log
      v-if="updateData"
      @refresh="resetDetails"
      :process-instance-id="dataObj.processInstanceId"
      :title="dataObj.oldProcessId ? '当前审批流程' : '审批日志'"
    ></apply-log>
    <div class="footer-btn" style="text-align: center">
      <el-button @click="cancel" class="cancle">返回</el-button>
      <el-popover
        v-if="moreBtn.length"
        placement="top"
        width="140"
        trigger="hover"
        popper-class="more-btn"
      >
        <div>
          <div
            v-for="({ callback, type, text, icon = '' }, i) of moreBtn"
            :key="i"
            @click="callback(type)"
            class="btn-cell"
          >
            <i :class="icon.includes('el-') ? icon : `iconfont ${icon}`"></i>
            {{ text }}
          </div>
        </div>
        <el-button slot="reference" style="margin: 0 10px">更多</el-button>
      </el-popover>
      <el-button
        v-for="({ callback, type, text, theme }, i) of footerBtn"
        :key="i"
        :type="theme"
        @click="callback(type)"
        :class="type"
        >{{ text }}</el-button
      >
    </div>
    <!-- 同意拒绝 -->
    <apply-dialog
      @submit="submit"
      :dialog-visible.sync="dialogVisible"
    ></apply-dialog>
  </div>
</template>

<script>
  import './css/index.scss';
  import { processDetail } from '@/api/flow/process';
  // import { urge } from '@/api/desk/im';
  import ApplyLog from '@/components/apply-log';
  import ApplyDialog from './components/apply-dialog';
  import CommentDialog from './components/comment-dialog';
  export default {
    name: 'approval-flow',
    components: {
      ApplyLog,
      ApplyDialog,
      CommentDialog
    },
    props: {
      id: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        dataObj: {},
        updateData: true,
        footerBtn: [],
        moreBtn: [],
        dialogVisible: false
      };
    },
    watch: {
      dataObj: {
        handler() {
          this.createdBtn();
        },
        deep: true
      }
    },
    mounted() {
      this.resetDetails();
    },
    methods: {
      resetDetails() {
        processDetail(this.id).then((res) => {
          if (res.data.data.oldProcessId) {
            res.data.data.processName = `${res.data.data.processName}-销假申请`;
            if (res.data.data.status === 1) {
              res.data.data.processName = `${res.data.data.processName}-已销假`;
            } else if (res.data.data.status === 2) {
              res.data.data.processName = `${res.data.data.processName}--销假申请被拒绝`;
            }
          }
          this.getDetail(res.data.data);
          this.updateData = false;
          this.$nextTick(() => {
            this.updateData = true;
          });
        });
      },
      getDetail(data) {
        /**
         * status 0, "审批中" 1, "通过" 2, "拒绝" 3, "撤销" 4, "禁用" 6, "删除"
         * categoryId 区分淄矿项目
         * businessId 淄矿跳转id
         * isCommon 判断是否为当前人发起
         * oldProcessId 判断当前是否为销假
         * isDefinitionActive 流程定义是否可用
         * warningStr 加班预警
         */
        this.dataObj = data;
        // 右上角审批状态图片
        this.dataObj.imgUrl = `/apply/apply-${data.status || 0}.png`;
      },
      createdBtn() {
        let { isRelated, flowableTaskId, isCommon, status } = this.dataObj;
        if (!isRelated) {
          this.moreBtn = [];
          this.footerBtn = [];
          return;
        }

        const obj = [];

        if (flowableTaskId) {
          obj.push({
            callback: this.confirm,
            type: 'refuse',
            text: '拒绝',
            theme: 'danger'
          });
          obj.push({
            callback: this.confirm,
            type: 'agree',
            text: '同意',
            theme: 'success'
          });
        }

        if (isCommon && status === 0) {
          // obj.push({
          //   callback: this.urge,
          //   text: '催办',
          //   icon: 'icon-cui-0',
          //   theme: 'info'
          // });
          obj.push({
            callback: this.repeal,
            text: '撤销',
            icon: 'icon-rollback-0',
            theme: 'warning'
          });
        }

        if (obj.length > 3) {
          this.moreBtn = obj.slice(2, obj.length);
          this.footerBtn = obj.slice(0, 2);
        } else {
          this.moreBtn = [];
          this.footerBtn = obj;
        }
        console.log('ggg', this.footerBtn);
      },
      confirm(type) {
        this.flag = type === 'agree' ? 1 : 2;
        this.dialogVisible = true;
      },
      submit({ applyOpinion, file }) {
        const data = {
          status: this.flag,
          flowableTaskId: this.dataObj.flowableTaskId,
          opinion: applyOpinion,
          flowProcessInstanceId: this.dataObj.flowProcessInstanceId,
          currentNodeId: this.dataObj.currentNodeId,
          file: JSON.stringify(file),
          dataJson: JSON.stringify(this.form)
        };
        console.log('data', data);
        this.$emit('submit', data);
      },
      // 催办
      // urge() {
      //   this.$confirm('是否向当前审批人发送催办消息？', '提示').then(() => {
      //     const loading = this.$loading();
      //     urge(this.id)
      //       .then(() => {
      //         this.$message.success('操作成功');
      //       })
      //       .finally(() => {
      //         loading.close();
      //       });
      //   });
      // },
      // 撤销
      async repeal() {
        this.flag = 3;
        const confirm = await this.$confirm(`确定要撤销吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return true;
          })
          .catch(() => {});

        if (confirm) {
          this.$emit('repeal');
        }
      },
      // 关闭返回
      cancel() {
        this.$emit('close');
      }
    }
  };
</script>
<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;
    width: 100%;

    .icon-full {
      position: absolute;
      right: 10px;
      bottom: 7px;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
