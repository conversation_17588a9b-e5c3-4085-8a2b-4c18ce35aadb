## 西北矿业绩效考核系统

1. 运行步骤：

```
// 安装依赖
cnpm install
// 开发环境
npm run serve
// 生产环境
npm run build
// 测试环境
npm run build:test
// 演示环境
npm run build:show
```

## v1.0.1版本操作说明

### 修改基础容器组件 basic-container

1、添加 autoHeight 是否在前端框架内容显示区显示自动高度
2、添加 fullHeight 是否显示全屏自动高度
3、添加 zoomHeight 在前端框架内容显示区显示自动高度减少高度

### 添加图片预览组件 ImagePreview

1、src String 多个地址用逗号分割
2、with、height 设置宽高

### 添加指令权限 directive/permission

1、v-hasPermi 根据菜单权限判断 实例：v-hasPermi="['notice_add']"
2、v-hasRole 根据角色权限判断 实例: v-hasRole="['administrator']"

### 数据字典使用步骤

1、在字典页面查找字典对应 code 值；
2、在使用页面添加对应字典 code 值 实例：
   系统字典：systemDicts: ["sex", "notice"]
   业务字典：serviceDicts: ["001"]
3、使用对应字典 实例：
   systemDicts.type.sex
   serviceDicts.type['001']

```
<el-select v-model="form.sex" placeholder="请选择性别">
        <el-option
          v-for="dict in systemDicts.type.sex"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
      <el-select v-model="form.warehouse" placeholder="请选择仓库">
        <el-option
          v-for="dict in serviceDicts.type['001']"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
```
### 优化本地代码格式化
1、项目根目录新增.prettierrc.json
singleQuote：是否格式化单引号； semi：结尾显示分号； trailingComma: 结尾逗号
```
{
  "singleQuote": true,
  "semi": false,
  "trailingComma": "none"
}
```
### 【新增】 版本管理

修改版本号，除了修改package.json 同时还要修改 public/version.js 缺一不可

### 【新增】 动态路由配置

#### 目录
必填：菜单名称、路由地址、菜单图标、菜单类型（菜单）、
菜单编号、菜单别名、菜单状态（显示）、是否缓存（否）、菜单排序
#### 菜单
必填：菜单名称、路由地址、菜单图标、菜单编号、菜单类型（菜单）、菜单别名、
新窗口（是）、组件名称（全局唯一）、菜单状态、激活地址（配置动态路由时填项：高亮作用）、
组件地址（viws文件夹下的地址，且不需要.vue后缀），是否缓存，菜单排序
#### 按钮
必填：菜单名称, 菜单图标，菜单编号、菜单类型（按钮）、菜单别名、菜单显示（隐藏）、
是否缓存（不缓存）、菜单排序
