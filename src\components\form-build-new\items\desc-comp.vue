<template>
  <div v-if="value" class="bc-form-build-desc">
    <i class="el-icon-warning" />
    <span>{{ value }}</span>
  </div>
</template>

<script>
  export default {
    name: '<PERSON><PERSON><PERSON>om<PERSON>',
    props: {
      value: String
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-ui';

  .bc-form-build-desc {
    .el-icon-warning {
      margin-right: 5px;
      font-size: 16px;
      line-height: 32px;

      // color: $--color-warning;
    }
  }
</style>
