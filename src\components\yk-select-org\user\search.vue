<template>
  <div>
    <el-form
      :model="form"
      size="small"
      ref="form"
      :inline="true"
      label-width="80px"
      label-suffix=":"
    >
      <el-form-item label="人员姓名" prop="realName">
        <el-input
          placeholder="请输入人员姓名"
          v-model="form.realName"
          @keyup.enter.native.stop="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >清空</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'search',
    data() {
      return {
        // 查询参数
        form: {
          realName: undefined
        }
      };
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        this.$emit('search', this.form.realName);
      }
    }
  };
</script>
