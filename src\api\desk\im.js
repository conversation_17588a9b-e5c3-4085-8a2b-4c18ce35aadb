/*
 * @description:
 * @param:
 * @author: Fei
 * @return:
 * @Date: 2020-11-19 09:33:58
 */
import request from '@/router/axios';
import qs from 'qs';
/**
 * 获取融云token
 * @param {userId:"用户id"}
 */
export const getImToken = (params) => {
  return request({
    url: '/api/attila-desk/im-user/get-token',
    method: 'get',
    params
  });
};
/**
 * 搜索联系人
 * @param {keywords:"搜索的关键字"}
 */
export const searchUser = (params) => {
  return request({
    url: '/api/attila-desk/im-user/search',
    method: 'get',
    params
  });
};

/**
 * 好友详情
 * @param {idOrPhone:"手机号/userId"}
 */
export const userDetail = (params) => {
  return request({
    url: '/api/system-attila/detail',
    method: 'get',
    params
  });
};

/**
 * 群组详情
 * @param {id:"id"}
 */
export const groupDetail = (params) => {
  return request({
    url: '/api/attila-desk/im/group/detail',
    method: 'get',
    params
  });
};
/**
 * 好友列表
 * @param {idOrPhone:"手机号/userId"}
 */
export const getFriendsList = (params) => {
  return request({
    url: '/api/attila-desk/im-friend/list-my',
    method: 'get',
    params
  });
};

/**
 * 好友列表
 * @param {name:"群组名称"}
 */
export const getGroupList = (params) => {
  return request({
    url: '/api/attila-desk/im/group/list',
    method: 'get',
    params
  });
};

/**
 * 同意加为好友
 * @param {targetId:"好友用户id"}
 */
export const addAgreement = (params) => {
  return request({
    url: '/api/attila-desk/im-friend/add-agreement',
    method: 'post',
    data: params
  });
};

/**
 * 忽略加为好友
 * @param {targetId:"好友用户id"}
 */
export const addRefuse = (params) => {
  return request({
    url: '/api/attila-desk/im-friend/add-refuse',
    method: 'post',
    data: params
  });
};

/**
 * 忽略加为好友
 * @param {displayName:"好友备注",targetId:"好友用户id"}
 */
export const updateDisplayName = (params) => {
  return request({
    url: '/api/attila-desk/im-friend/update-display-name',
    method: 'post',
    data: params
  });
};

/**
 * 搜索群组
 * @param {keywords:"好友用户id"}
 */
export const searchGroup = (params) => {
  return request({
    url: '/api/attila-desk/im/group/search',
    method: 'post',
    data: params
  });
};

/**
 * 获取群组成员列表
 * @param {keywords:"好友用户id"}
 */
export const groupMemberList = (params) => {
  return request({
    url: '/api/attila-desk/im/group-member/list',
    method: 'get',
    params
  });
};

/**
 * 获取集团列表
 * @param
 */
export const getAuthAllList = (params) => {
  return request({
    url: '/api/attila-system/common/auth-all-list',
    method: 'get',
    params
  });
};

/**
 * 获取企业内部组织列表
 * @param
 */
export const imCompanyTree = (params) => {
  return request({
    url: '/api/attila-system/dept/im-company/tree',
    method: 'get',
    params
  });
};

/**
 * 搜索集团列表
 * @param
 */
export const deptSearch = (params) => {
  return request({
    url: `/api/attila-system/dept/dept-search?keywords=${params.keywords}`,
    method: 'post',
    data: params
  });
};

export const fileUpload = (params) => {
  return request({
    url: `/api/szyk-resource/oss/endpoint/put-file-attach`,
    method: 'post',
    data: params
  });
};
/**
 * 申请加为好友
 * @param
 */
export const addApply = (params) => {
  return request({
    url: `/api/attila-desk/im-friend/add-apply`,
    method: 'post',
    data: params
  });
};
/**
 * IM-单聊-用户信息
 * @param {ids:[]} params
 */
export const listImInfo = (params) => {
  // console.log(" IM-单聊-用户信息", params);
  return request({
    url: `/api/attila-desk/im-user/list-im-info`,
    method: 'post',
    data: qs.stringify(params)
  });
};
/**
 * IM-群聊-用户信息
 * @param {ids:[],groupId:""} params
 */
export const listGroupImInfo = (params) => {
  return request({
    url: `/api/attila-desk/im-user/list-group-im-info`,
    method: 'post',
    data: qs.stringify(params)
  });
};
/**
 * IM-群聊-详情（批量获取-简单版）
 * @param {ids:[]} params
 */

export const groupInfoSimple = (params) => {
  return request({
    url: `/api/attila-desk/im/group/v1/simple-detail`,
    method: 'post',
    data: qs.stringify(params)
  });
};
/**
 * IM-群聊-详情（批量获取-简单版）
 * @param {id:""} params
 */
export const getGroupDetail = (params) => {
  return request({
    url: `/api/attila-desk/im/group/v1/detail`,
    method: 'get',
    params
  });
};
/**
 * IM-查询组织以及组织关联组织的根父节点列表
 * @param
 */
export const getOrganizeRootNode = (params) => {
  return request({
    url: `/api/attila-system/dept/im-org/list`,
    method: 'get',
    params
  });
};
/**
 * IM-查询组织下的部门、人员、关联子组织列表
 * @param
 */
export const getOrganizeRootNodeDetail = (params) => {
  return request({
    url: '/api/attila-system/dept/im-org/list-detail',
    method: 'get',
    params
  });
};

/**
 * IM-查询组织下的部门、人员、关联子组织列表
 * @param
 */
export const imModelsearch = (params) => {
  return request({
    url: '/api/attila-system/dept/im-org/search',
    method: 'get',
    params
  });
};

/**
 * IM-根据群ID查询群文件夹ID
 * @param {String} groupId 群ID
 */
export const selectGroupFolderId = (params) => {
  return request({
    url: '/api/attila-resource/attiladoc/selectGroupFolderId',
    method: 'get',
    params
  });
};

// 催办
export const urge = (flowId) => {
  return request({
    url: '/api/pc/process-task-operation/press-flow',
    method: 'post',
    data: { flowId }
  });
};
// 分享
export const share = (data) => {
  return request({
    url: '/api/attila-desk/im-friend/send-flow-share',
    method: 'post',
    data
  });
};
// 发起讨论
export const createDiscussion = (flowId) => {
  return request({
    url: '/api/attila-desk/im/group/flow/create',
    method: 'post',
    data: { flowId }
  });
};
