<template>
  <el-table
    :data="source"
    size="small"
    border
    style="width: 100%; margin-top: 10px"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" />
    <el-table-column type="index" label="序号" align="center" width="60">
    </el-table-column>
    <el-table-column prop="indexName" label="指标名称" align="center">
    </el-table-column>
    <el-table-column
      prop="indexStandard"
      label="考核标准"
      align="center"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column prop="action" align="center" label="操作" width="180px">
      <template slot-scope="scope">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-edit"
          @click="() => dispatch('edit', scope.row)"
          >编辑</el-button
        >
        <el-popconfirm
          style="margin-left: 8px"
          title="确定 删除 此数据吗？"
          @confirm="() => dispatch('delete', scope.row)"
        >
          <el-button
            type="text"
            icon="el-icon-delete"
            size="mini"
            slot="reference"
            style="color: red"
            >删除
          </el-button>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-list',
    props: {
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    methods: {
      dispatch(type, row) {
        this.$emit('dispatch', type, row);
      },
      handleSelectionChange(val) {
        this.$emit('choice', val);
      }
    }
  };
</script>
