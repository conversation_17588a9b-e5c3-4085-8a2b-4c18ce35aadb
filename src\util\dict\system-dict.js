import Dict from './dict';
import { mergeOptions } from './dict-options';

export default function (Vue, options) {
  mergeOptions(options);
  Vue.mixin({
    data() {
      if (
        this.$options === undefined ||
        this.$options.systemDicts === undefined ||
        this.$options.systemDicts === null
      ) {
        return {};
      }
      const systemDicts = new Dict();
      systemDicts.owner = this;
      return {
        systemDicts
      };
    },
    created() {
      if (!(this.systemDicts instanceof Dict)) {
        return;
      }
      options.onCreated && options.onCreated(this.systemDicts);
      console.log('this.systemDicts', options);
      this.systemDicts.init(this.$options.systemDicts).then(() => {
        options.onReady && options.onReady(this.systemDicts);
        this.$nextTick(() => {
          this.$emit('dictReady', this.systemDicts);
          if (
            this.$options.methods &&
            this.$options.methods.onDictReady instanceof Function
          ) {
            this.$options.methods.onDictReady.call(this, this.systemDicts);
          }
        });
      });
    }
  });
}
