<template>
  <el-form
    ref="form"
    :model="form"
    :rules="rules"
    label-suffix=": "
    label-width="100px"
    size="small"
  >
    <el-row>
      <el-col :span="24" v-if="website.tenantMode">
        <el-form-item label="所属租户" prop="tenantId">
          <avue-input-tree
            v-model="form.tenantId"
            type="tree"
            placeholder="请选择所属租户"
            style="width: 100%"
            :dic="tenantData"
          ></avue-input-tree>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="机构名称" prop="deptName">
          <el-input
            v-model="form.deptName"
            placeholder="请输入机构名称"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="机构全称" prop="fullName">
          <el-input
            v-model="form.fullName"
            placeholder="请输入机构全称"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="上级机构" prop="parentId">
          <avue-input-tree
            v-model="form.parentId"
            type="tree"
            check-strictly
            :disabled="modelType === 'addChild'"
            placeholder="请选择上级机构"
            style="width: 100%"
            :dic="deptData"
          ></avue-input-tree>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="机构类型" prop="deptCategory">
          <el-select
            v-model="form.deptCategory"
            placeholder="请选择机构类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in systemDicts.type.org_category"
              :key="dict.value"
              :label="dict.label"
              :value="+dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number
            placeholder="请输入排序"
            style="width: 100%"
            v-model="form.sort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="备注" prop="remark">
          <el-input placeholder="请输入备注" v-model="form.remark" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import YkIconSelect from '@/components/yk-icon-select';
  import YkMenuSelect from '@/components/yk-menu-select';
  import { getDeptTree } from '@/api/system/dept';
  import { formatTreeData } from '@/util/util';
  import website from '@/config/website';

  const rules = {
    deptName: {
      required: true,
      message: '请输入机构名称',
      trigger: 'blur'
    },
    fullName: {
      required: true,
      message: '请输入机构全称',
      trigger: 'blur'
    },
    deptCategory: {
      required: true,
      message: '请选择机构类型',
      trigger: 'change'
    },
    sort: {
      required: true,
      message: '请输入菜单排序',
      trigger: 'blur'
    }
  };

  export default {
    inheritAttrs: false,
    name: 'dept-form',
    components: {
      YkIconSelect,
      YkMenuSelect
    },
    systemDicts: ['org_category'],
    props: {
      modelType: {
        type: String,
        default: ''
      },
      visited: {
        type: Boolean,
        default: false
      },
      childId: {
        type: String,
        default: ''
      },
      tenantData: {
        type: Array,
        default() {
          return [];
        }
      },
      formData: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    watch: {
      formData: {
        handler(val) {
          Object.assign(this.form, val);
        },
        deep: true
      },
      visited: {
        handler(val) {
          if (val) {
            this.initDeptData();
            if (this.childId.length) {
              this.form.parentId = this.childId;
            }
          }
        },
        immediate: true
      }
    },
    data() {
      return {
        rules,
        website,
        deptData: [],
        form: {
          deptName: '',
          fullName: '',
          parentId: '',
          deptCategory: undefined,
          sort: undefined,
          remark: ''
        }
      };
    },
    methods: {
      // 获取上级机构信息
      async initDeptData() {
        try {
          const res = await getDeptTree();
          this.deptData = formatTreeData(res.data.data, {
            label: 'title'
          });
        } catch (e) {
          console.error(e);
        }
      },
      // 表单校验
      validateFn() {
        return new Promise((resolve, reject) => {
          this.$refs.form.validate((valid) => {
            if (valid) {
              resolve(this.form);
            } else {
              reject(new Error('please complete the options'));
            }
          });
        });
      },
      clearFn() {
        this.$refs.form.resetFields();
        this.form = {
          deptName: '',
          fullName: '',
          parentId: '',
          deptCategory: undefined,
          sort: undefined,
          remark: ''
        };
      }
    }
  };
</script>
