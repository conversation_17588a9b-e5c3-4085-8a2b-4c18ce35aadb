<template>
  <div
    class="componentsList"
    :class="{ createTab: activeName === 'component' }"
  >
    <!-- <el-tabs v-if="!showTab" v-model="activeName" class="listTabs">
      <el-tab-pane label="控件" name="component"></el-tab-pane>
      <el-tab-pane label="控件组" name="componentGroup"></el-tab-pane>
    </el-tabs> -->
    <div class="content">
      <div v-for="(li, i) of list" :key="i" class="componentsCell">
        <header>{{ li.name }}</header>
        <draggable
          v-model="li.children"
          :move="onMove"
          animation="300"
          :scroll="true"
          :group="{ name: 'formDesign', pull: 'clone' }"
        >
          <transition-group>
            <div
              v-for="c of li.children"
              :key="c.type"
              @click="formClickCell(c)"
              :id="c.type"
              class="formCell"
              :class="{ noClick: clickValidate }"
            >
              <span class="iconfont" :class="c.icon"></span>
              {{ c.name }}
            </div>
          </transition-group>
        </draggable>
      </div>
    </div>
  </div>
</template>
<script>
  import draggable from 'vuedraggable';
  import formDataList from './data/index.js';
  import ydFormDataList from './data/yd-data.js';
  import htFormDataList from './data/ht-data.js';
  export default {
    components: { draggable },
    props: {
      showTab: {
        type: String,
        default: ''
      },
      exclude: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        list: [],
        activeName: 'component'
      };
    },
    computed: {
      clickValidate() {
        // 控件组只能拖拽一个
        const groupList = this.$store.state.oaSetUp.formDesignList;
        const isGroup = groupList.some(
          (g) =>
            [
              'rest',
              'replacecard',
              'work',
              'out',
              'trip',
              'turnFormal',
              'leave'
            ].includes(g.type) ||
            g.type.startsWith('yd-') ||
            g.type.startsWith('htTo')
        );
        return this.activeName === 'componentGroup' && isGroup;
      }
    },
    watch: {
      activeName(val) {
        let list = formDataList[val];
        if (this.$route.query.appCode === 'ydzk' && val === 'componentGroup') {
          list = list.concat(ydFormDataList);
        }
        if (val === 'componentGroup') {
          list = list.concat(htFormDataList);
        }
        this.list = list;
      }
    },
    created() {
      this.list = formDataList[this.showTab || 'component'];
      this.list.forEach((li) => {
        li.children = li.children.filter(
          (lc) => !this.exclude.includes(lc.type)
        );
      });
      this.list = this.list.filter((l) => l.children.length);
    },
    methods: {
      formClickCell(c) {
        // 控件组有一个之后禁止点击
        if (!this.clickValidate) {
          this.$emit('click-cell', c);

          // 组件点击之后定位到最底部
          this.$nextTick(() => {
            document.querySelector('.active').scrollIntoView();
          });
        }
      },
      onMove(e) {
        // 控件组只能拖拽一个
        if (this.clickValidate) {
          return false;
        }

        // 禁止控件组拖拽到明细
        if (
          this.activeName === 'componentGroup' &&
          e.to.className.indexOf('formContent') !== -1
        ) {
          return false;
        }

        // 禁止明细拖拽到明细
        if (
          e.dragged.id === 'form' &&
          e.to.className.indexOf('formContent') !== -1
        ) {
          return false;
        }

        // 禁止控件之间相互拖拽
        if (e.to.className.indexOf('formDesign') === -1) {
          return false;
        }
      }
    }
  };
</script>
<style lang="scss">
  .createTab {
    .el-tabs__active-bar {
      width: 28px !important;
    }
  }

  .componentsList {
    width: 360px;

    // height: 100%;
    height: calc(100% - 40px) !important;
    background: #fff;

    .el-tabs__active-bar {
      margin-left: 0 !important;
    }

    .noClick {
      cursor: not-allowed !important;
    }

    .content {
      height: calc(100% - 25px);
      margin-top: 16px;

      // margin-bottom: 32px;
      overflow-y: auto;
    }

    .formCell {
      cursor: pointer;

      span {
        margin-right: 10px;
        color: #9da5b2;
      }
    }

    .listTabs {
      .el-tabs__header {
        box-shadow: inherit;
      }

      .el-tabs__nav {
        margin-left: calc(50% - 54px) !important;
        line-height: 50px !important;
      }

      .is-active {
        color: #409eff !important;
      }

      div {
        color: #333 !important;
        font-weight: 400;
        font-size: 14px !important;
      }
    }

    .componentsCell {
      margin: 0 24px 16px;

      header {
        margin-bottom: 16px;
        color: #333;
      }

      & > div > span {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      .formCell {
        box-sizing: border-box;
        width: 148px;
        margin-bottom: 16px;
        padding: 11px 13px;
        line-height: 14px;
        background-color: #f4f5fa;
        border-radius: 4px;

        &:hover {
          background-color: #e2e3ec;
        }
      }
    }
  }
</style>
