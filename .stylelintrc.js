module.exports = {
  root: true,
  plugins: ["stylelint-scss", "stylelint-order"],
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-recommended-vue',
    'stylelint-config-recommended-vue/scss',
    'stylelint-config-rational-order',
    // 'stylelint-config-clean-order', // 对关联属性进行分组和排序
    'stylelint-config-prettier'],
  "overrides": [
    {
      "files": ["**/*.scss"],
      // "customSyntax": "postcss-scss"
    }
  ],
  rules: {
    "selector-type-no-unknown": null,
    // 注释后要求或禁止空格
    "scss/double-slash-comment-whitespace-inside": "always",
    // 指定一个模式类选择符
    "selector-class-pattern": null,
    // 禁止未知的伪类选择器
    // "selector-pseudo-class-no-unknown": [
    //   true,
    //   {
    //     ignorePseudoClasses: ["global"],
    //   },
    // ],
    "selector-pseudo-class-no-unknown": null,
    // 不允许未知的规则
    "at-rule-no-unknown": [
      true,
      {
        ignoreAtRules: ["function", "if", "each", "include", "mixin", "for", "else"],
      },
    ],
    // 不允许空块
    "block-no-empty": true,
    // 不允许重复的选择器
    "no-duplicate-selectors": null,
    // 不允许空的来源
    "no-empty-source": null,
    // 要求声明前不允许空一行
    "declaration-empty-line-before": null,
    // 不允许缺少源代码结尾换行符（可自动修复）
    "no-missing-end-of-source-newline": null,
    // 要求或禁止 Unicode BOM
    "unicode-bom": "never",
    // 不允许选择器之后覆盖选择器的低特异性更高的特异性
    "no-descending-specificity": null,
    // 不允许在字体系列名称列表中缺少通用系列
    "font-family-no-missing-generic-family-keyword": null,
    // 在声明的冒号后需要一个空格或不允许有空格（"always"|"never"|"always-single-line"）
    "declaration-colon-space-after": "always-single-line",
    // 冒号之前的声明需要一个空格或不允许空白
    "declaration-colon-space-before": "never",
    // 要求或不允许在声明块后面的分号
    "declaration-block-trailing-semicolon": null,
    // 为适用的颜色功能指定现代或传统符号
    "color-function-notation": null,
    // 要求或不允许在规则之前有一个空行
    "rule-empty-line-before": [
      "always",
      {
        ignore: ["after-comment", "first-nested"],
      },
    ],
    // 不允许未知属性
    "property-no-unknown": [
      true,
      {
        ignoreProperties: ["lines"],
      },
    ],
    // 不允许未知的媒体功能的名字
    "media-feature-name-no-unknown": [
      true,
      {
        ignoreMediaFeatureNames: ["min-device-pixel-ratio"],
      },
    ],
    // 不允许未知的单位
    "unit-no-unknown": [
      true,
      {
        ignoreUnits: [/rpx/],
      },
    ],
    // 不允许未知的伪元素选择器
    "selector-pseudo-element-no-unknown": [
      true,
      {
        ignorePseudoElements: ["v-deep"],
      },
    ]
  },
};
