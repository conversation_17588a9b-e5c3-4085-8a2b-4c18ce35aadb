<template>
  <el-dialog
    @open="openDialog"
    :title="title"
    :visible.sync="rangeVisible"
    width="544px"
    :append-to-body="true"
    custom-class="selectOriginator"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane label="成员" name="1"></el-tab-pane>
      <el-tab-pane label="部门" name="3"></el-tab-pane>
      <el-tab-pane label="岗位" name="4"></el-tab-pane>
      <!-- <el-tab-pane label="职务" name="2"></el-tab-pane> -->
    </el-tabs>
    <div v-loading="loading" class="content">
      <dept
        @setCheckIds="setCheckIds"
        :checked-list.sync="checkedList"
        :tree-data="treeData"
        :radio="activeTab"
        :is-radio="isRadio"
        :check-strictly="activeTab === '3'"
        :max-select-length="maxSelectLength"
      ></dept>
    </div>
    <div class="footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button @click="save" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dept from '@/components/dept/dept';
  import { getList } from '@/api/system/post';
  import { jobList } from '@/api/system/company';
  import { getTreeList, getStaffTreeList } from '@/api/system/dept';
  export default {
    components: { dept },
    props: {
      rangeVisible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '发起人范围'
      },
      ids: {
        type: Array,
        default: () => []
      },
      radio: {
        type: String,
        default: '1'
      },
      isRadio: {
        type: Boolean,
        default: true
      },
      // 最大选择数量
      maxSelectLength: {
        type: Number,
        default: 20
      }
    },
    data() {
      return {
        activeTab: '1',
        checkedList: [],
        selectList: [],
        treeData: [],
        loading: false
      };
    },
    watch: {
      activeTab() {
        this.getTreeList();
        this.treeData = [];
        this.checkedList = [];
        this.selectList = [];
      }
    },
    methods: {
      openDialog() {
        this.activeTab = this.radio;
        this.$nextTick(() => {
          this.checkedList = this.ids;
          this.getTreeList();
        });
      },
      setCheckIds(list) {
        this.selectList = list;
      },
      getTreeList() {
        this.loading = true;
        const url = {
          1: getStaffTreeList,
          2: jobList,
          3: getTreeList,
          4: getList
        };
        url[this.activeTab]()
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.treeData = res.data.data;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      save() {
        this.$emit('rangeSave', {
          activeTab: this.activeTab,
          list: this.selectList
        });
        this.handleClose();
      },
      handleClose() {
        this.$emit('update:rangeVisible', false);
      }
    }
  };
</script>
<style lang="scss">
  .el-dialog__wrapper .selectOriginator .el-dialog__header {
    padding-bottom: 17px !important;
  }

  .selectOriginator {
    .el-tabs--top {
      padding-right: 24px;
      padding-left: 24px;

      .el-tabs__header {
        margin-top: 20px;
        margin-bottom: 0;
      }
    }

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      padding: 0 !important;

      .header-tip {
        margin-bottom: 4px;
        padding-bottom: 16px;
        padding-left: 24px;
        border-bottom: 1px solid #d9d9d9;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88px;

      button {
        width: 140px;
        height: 40px;
      }
    }
  }
</style>
