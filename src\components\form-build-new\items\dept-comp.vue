<template>
  <div class="bc-form-build-dept">
    <el-input
      @click.native="onClick"
      :value="names"
      :placeholder="data.placeholder"
      readonly
      :title="names"
      :size="data.inputSize"
    />
    <b-c-transfer-dept-tree
      @change="onChange"
      :visible.sync="visible"
      :default-checked-ids="ids"
      :multiple="data.deptSelect === 2"
    ></b-c-transfer-dept-tree>
  </div>
</template>

<script>
  import BCTransferDeptTree from '@/components/transfer-dept-tree';

  export default {
    name: 'DeptComp',
    components: { BCTransferDeptTree },
    props: {
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false
      };
    },
    computed: {
      names() {
        return Array.from(this.value, (item) => item.name).join('，');
      },
      ids() {
        return Array.from(this.value, (item) => item.id);
      }
    },
    methods: {
      onClick() {
        if (!this.disabled) {
          this.visible = true;
        }
      },
      onChange(ids, list = []) {
        this.$emit(
          'input',
          Array.from(list, (item) => {
            return { id: item.id, name: item.title };
          })
        );
      }
    }
  };
</script>

<style lang="scss">
  .bc-form-build-dept {
    .el-input__inner {
      cursor: pointer;
    }
  }
</style>
