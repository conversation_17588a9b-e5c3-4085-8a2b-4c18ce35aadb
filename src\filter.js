import Vue from 'vue';
import { dateFormat } from '@/util/date';

Vue.filter('filterDafenType', function (type) {
  switch (type) {
    case '1':
      return '赋分法';
    case '2':
      return '分值';
    default:
      return '---';
  }
});

Vue.filter('filterDafenStatus', function (type) {
  switch (type) {
    case 0:
      return '未生效';
    case 1:
      return '生效';
    default:
      return '---';
  }
});

Vue.filter('changeStatus', function (val) {
  switch (val) {
    case '1':
      return '未评价';
    case '2':
      return '已评价';
    case '3':
      return '已完成';
    default:
      return '---';
  }
});

Vue.filter('changeExamineStatus', function (val) {
  switch (val) {
    case '0':
      return '审批中';
    case '1':
      return '已通过';
    case '2':
      return '已拒绝';
    case '3':
      return '已撤回';
    case '4':
      return '已删除';
    case '6':
      return '已删除';
    default:
      return '---';
  }
});

Vue.filter('fillStatus', function (type) {
  switch (type) {
    case '0':
      return '未填报';
    case '1':
      return '已填报';
    case '2':
      return '已提交';
    case '3':
      return '已完成';
    default:
      return '---';
  }
});

Vue.filter('fillStatusProgress', function (type) {
  switch (type) {
    case 0:
      return '未填报';
    case 1:
      return '已填报';
    default:
      return '---';
  }
});

Vue.filter('receiptStatus', function (type) {
  switch (type) {
    case '0':
      return '审批中';
    case '1':
      return '已通过';
    case '2':
      return '已拒绝';
    case '3':
      return '已撤回';
    default:
      return '---';
  }
});

Vue.filter('formatDate', function (val, format = 'yyyy-MM-dd') {
  if (val) {
    return dateFormat(new Date(val), format);
  } else {
    return '---';
  }
});

Vue.filter('receiptStatusResult', function (type) {
  switch (type) {
    case '1':
      return '待提交';
    case '2':
      return '已提交';
    case '3':
      return '已完成';
    default:
      return '---';
  }
});

Vue.filter('scoreFilter', function (val) {
  switch (val) {
    case null:
      return '---';
    case undefined:
      return '---';
    default:
      return Number(val).toFixed(2);
  }
});
