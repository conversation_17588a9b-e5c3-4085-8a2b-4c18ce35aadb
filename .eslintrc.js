module.exports = {
  root: true,
  env: {
    node: true
  },
  'extends': [
    'plugin:vue/essential',
    'eslint:recommended',
    "@vue/prettier"
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    quotes: [0, 'single'],
    "prettier/prettier": [
      "error",
      {
        "singleQuote": true, // 使用单引号
        "tabWidth": 2, // 默认缩进
        "useTabs": false, // tab缩进
        "bracketSpacing": true, // 对象空格
        "semi": true, // 结尾分号
        "trailingComma": "none", // 结尾处不加逗号
        "vueIndentScriptAndStyle": true, // <script> 内部缩进
        "no-inline-config": true // 禁用所有内联配置注释
      }
    ],
  },
  parserOptions: {
    parser: 'babel-eslint'
  }
}