export default {
  state: {
    // 表单的所有数据
    formDesignList: [],
    // 表单设计删除的对象
    delectDesignObj: {},
    // 表单设计报错的数据集合
    oaComponentsErrorList: {},
    // 当前的状态，是否触发校验
    isValidate: false,
    // 表单设计选中id
    oaComponentsId: ''
  },
  mutations: {
    CLEAR_FORM: (state) => {
      state.formDesignList = [];
      state.delectDesignObj = {};
      state.oaComponentsErrorList = {};
      state.isValidate = false;
      state.oaComponentsId = '';
    },
    SET_COMPONENTS_ID: (state, id) => {
      state.oaComponentsId = id;
    },
    SET_FORM_DESIGN_LIST: (state, list) => {
      state.formDesignList = list;
    },
    SET_DELETE_DESIGN_OBJ: (state, obj) => {
      state.delectDesignObj = obj;
    },
    SET_ERROR_LIST: (state, obj) => {
      state.oaComponentsErrorList = { ...state.oaComponentsErrorList, ...obj };
    },
    DELETE_ERROR_LIST: (state, id) => {
      delete state.oaComponentsErrorList[id];
    },
    SET_VALIDATE: (state) => {
      state.isValidate = true;
    }
  }
};
