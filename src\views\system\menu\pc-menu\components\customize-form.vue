<template>
  <el-form
    v-loading="loading"
    ref="form"
    :model="form"
    :rules="rules"
    label-width="100px"
    size="small"
  >
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="上级菜单">
          <yk-menu-select
            v-model="form.parentId"
            placeholder="默认主目录"
            type="PC"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="菜单类型" prop="category">
          <el-radio-group v-model="form.category" @change="menuTypeChange">
            <el-radio :label="0">目录</el-radio>
            <el-radio :label="1">菜单</el-radio>
            <el-radio :label="2">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24" v-if="form.category !== 2">
        <el-form-item label="菜单图标" prop="source">
          <el-input
            v-model="form.source"
            placeholder="请选择菜单图标"
            @focus="iconVisit = true"
            clearable
          />
          <yk-icon-select v-model="iconVisit" @selectFont="selectFont" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="菜单名称" prop="name">
          <div class="form_div">
            <el-input
              v-model="form.name"
              placeholder="请输入菜单名称"
              clearable
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="菜单编号" prop="code">
          <div class="form_div">
            <el-input
              v-model="form.code"
              placeholder="请输入菜单编号"
              maxlength="100"
              clearable
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="菜单别名" prop="alias">
          <div class="form_div">
            <el-input
              v-model="form.alias"
              placeholder="请输入菜单别名"
              maxlength="100"
              clearable
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <div class="form_div">
            <el-input-number
              style="width: 100%"
              v-model="form.sort"
              controls-position="right"
              :min="0"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category !== 2">
        <el-form-item label="新窗口" prop="isOpen">
          <div class="form_div">
            <el-radio-group v-model="form.isOpen">
              <el-radio :label="1">否</el-radio>
              <el-radio :label="2">是</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category !== 2">
        <el-form-item label="路由地址" prop="path">
          <div class="form_div">
            <el-input
              v-model="form.path"
              placeholder="请输入路由地址"
              clearable
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category === 1">
        <el-form-item label="激活地址">
          <div class="form_div">
            <el-input v-model="form.activeMenu" placeholder="请输入激活地址" />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category === 1">
        <el-form-item label="组件名称">
          <div class="form_div">
            <el-input
              v-model="form.componentName"
              placeholder="请输入组件名称"
              clearable
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category === 1">
        <el-form-item prop="componentPath">
          <span slot="label">
            <el-tooltip
              content="访问的组件路径，如：`system/user/index`，默认在`views`目录下"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
            组件路径
          </span>
          <div class="form_div">
            <el-input
              v-model="form.componentPath"
              placeholder="请输入组件路径"
              clearable
            />
          </div>
        </el-form-item>
      </el-col>

      <el-col :span="12" v-if="form.category === 1">
        <el-form-item>
          <span slot="label">
            <el-tooltip
              content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
            是否缓存
          </span>
          <div class="form_div">
            <el-radio-group v-model="form.isKeepAlive">
              <el-radio :label="1">缓存</el-radio>
              <el-radio :label="2">不缓存</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category !== 2">
        <el-form-item label="显示状态" prop="isHide">
          <div class="form_div">
            <el-radio-group v-model="form.isHide">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="2">隐藏</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注">
          <el-input type="textarea" :row="3" v-model="form.remark" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import YkIconSelect from '@/components/yk-icon-select';
  import YkMenuSelect from '@/components/yk-menu-select';
  import { getMenu } from '@/api/system/menu';

  const rules = {
    source: {
      required: true,
      message: '请选择菜单图标',
      trigger: 'change'
    },
    name: {
      required: true,
      message: '请输入菜单名称',
      trigger: 'blur'
    },
    code: {
      required: true,
      message: '请输入菜单编号',
      trigger: 'blur'
    },
    alias: {
      required: true,
      message: '请输入菜单别名',
      trigger: 'blur'
    },
    isOpen: {
      required: true,
      message: '请选择新窗口',
      trigger: 'change'
    },
    sort: {
      required: true,
      message: '请输入菜单排序',
      trigger: 'blur'
    },
    path: {
      required: true,
      message: '请输入路由地址',
      trigger: 'blur'
    }
  };
  export default {
    inheritAttrs: false,
    name: 'customize-form',
    props: {
      isEdit: {
        type: Boolean,
        default: false
      },
      id: {
        type: String,
        default: ''
      },
      childId: {
        type: String,
        default: ''
      }
    },
    components: {
      YkIconSelect,
      YkMenuSelect
    },
    data() {
      return {
        rules,
        form: {
          parentId: '0',
          category: 1,
          source: '',
          name: '',
          sort: 0,
          isOpen: 1,
          path: '',
          activeMenu: '',
          componentName: '',
          componentPath: '',
          code: '',
          alias: '',
          isKeepAlive: 2,
          isHide: 1,
          remark: ''
        },
        iconVisit: false,
        loading: false
      };
    },
    mounted() {
      if (this.isEdit) {
        this.init();
      }
      if (this.childId.length) {
        this.form.parentId = this.childId;
      }
    },
    methods: {
      menuTypeChange() {
        if (this.form.category === 0) {
          this.form.componentName = '';
          this.form.componentPath = '';
          this.form.activeMenu = '';
          this.form.isKeepAlive = 2;
        }
      },
      // 编辑获取初始数据
      async init() {
        this.loading = true;
        try {
          const res = await getMenu(this.id);
          this.form = res.data.data;
          if (
            this.form.category === 1 &&
            !this.form.componentName &&
            !this.form.componentPath
          ) {
            this.form.category = 0;
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 图标同步
      selectFont(fontStr) {
        this.form.source = fontStr;
      },
      // 表单校验
      validateFn() {
        return new Promise((resolve, reject) => {
          this.$refs.form.validate((valid) => {
            if (valid) {
              resolve(this.form);
            } else {
              reject(new Error('please complete the options'));
            }
          });
        });
      }
    }
  };
</script>
<style scoped>
  .form_div {
    height: 34px;
  }
</style>
