.theme-d2 {
  .avue-logo{
    color: #409EFF;
    background-color: #ebf1f6;
    box-shadow: none;

    .avue-logo_title{
      font-weight: 400;
      font-size: 20px;
    }
  }

  .avue-top{
    background-color: #ebf1f6;
    box-shadow: none;
  }

  .avue-main{
    padding: 0 5px;
  }

  .avue-tags{
    margin-left: 6px;
    padding: 0;
    background-color: #ebf1f6;
    border: 1px solid #e4e7ed;
    border-radius: 3px;
    box-shadow: none;

    .el-tabs__item{
      margin: 0 !important;
      color: #606266 !important;
      font-weight: 500 !important;
      font-size: 14px !important;
      background-color: rgba(0,0,0,3%) !important;
      border-left: 1px solid #cfd7e5 !important;

      &:first-child{
        border-left: none !important;
      }
    }

    .is-active{
      color: #409EFF !important;
      background-color: #fff !important;
      border-bottom: 1px solid #fff !important;
    }
  }

  .avue-sidebar{
    background-color: #ebf1f6;
    box-shadow: none;

    .el-menu-item,.el-submenu__title{
      i,span{
          color: #606266
      }

      &:hover,&.is-active{
        background: hsla(0deg,0%,100%,50%);

          i,span{
            color: #409EFF;
          }
      }
    }
  }
}