<template>
  <el-dialog
    append-to-body
    title="请选择菜单图标"
    :visible.sync="open"
    width="75%"
    @close="close"
  >
    <div style="height: 560px; overflow-y: auto">
      <el-tabs v-model="active">
        <el-tab-pane
          v-for="(item, index) in iconList"
          :label="item.label"
          :name="item.index"
          :key="index"
        >
          <div class="icon-input-icon__list">
            <div
              class="icon-input-icon__item"
              v-for="(li, idx) in item.list"
              :key="idx"
              @click="selectFn(li)"
            >
              <i class="icon-input-icon__icon" :class="li"></i>
              <p>{{ li }}</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
  import iconList from '@/config/iconList';

  export default {
    name: 'YkIconSelect',
    props: {
      open: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        iconList,
        active: '0'
      };
    },
    model: {
      prop: 'open',
      event: 'change'
    },
    methods: {
      close() {
        this.$emit('change', false);
      },
      selectFn(fontStr) {
        this.$emit('selectFont', fontStr);
        this.close();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .icon-input-icon__list {
    display: flex;
    flex-wrap: wrap;
  }

  .icon-input-icon__item {
    width: 60px;
    padding: 10px 20px 0;
    text-align: center;
    transition: all 0.2s;

    &:hover {
      transform: scale(1.2);
    }

    p {
      margin-top: 5px;
      font-size: 12px;
    }
  }

  .icon-input-icon__icon {
    font-size: 32px !important;
  }
</style>
