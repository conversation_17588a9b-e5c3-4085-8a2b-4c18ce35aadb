<template>
  <div class="bc-form-build-input-number">
    <el-col :span="12">
      <el-form-item :prop="`${prop}.provinceCode`" :label="`${data.name}：`">
        <region-select
          ref="region"
          v-model="hValue1"
          :level="data.provinceType === 3 ? 2 : 3"
          placeholder="请选择"
          class="bc-form-build-region"
        ></region-select>
      </el-form-item>
    </el-col>
    <el-col v-if="data.provinceType === 2" :span="12">
      <el-form-item :prop="`${prop}.street`" label="街道：">
        <el-input
          v-model="hValue2"
          :maxlength="50"
          show-word-limit
          placeholder="请输入"
          clearable
        />
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
  import RegionSelect from '@/components/region-select';

  export default {
    name: 'RegionComp',
    components: { RegionSelect },
    props: {
      value: Object,
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      prop: String
    },
    computed: {
      hValue1: {
        get() {
          let {
            provinceCode = '',
            cityCode = '',
            countyCode = ''
          } = this.value || {};
          return [provinceCode, cityCode, countyCode];
        },
        set(val) {
          let value = this.$refs.region.getRegionInfoByValue(val);
          this.$emit('input', {
            ...this.value,
            ...value
          });
        }
      },
      hValue2: {
        get() {
          let { street = '' } = this.value || {};
          return street;
        },
        set(val) {
          this.$emit('input', {
            ...this.value,
            street: val
          });
        }
      }
    },
    created() {
      if (!this.value) {
        let value = { provinceCode: '' };
        if (this.data && this.data.provinceType === 2) {
          value.street = '';
        }
        this.$emit('input', value);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .bc-form-build-region {
    &.bc-region-select {
      width: 100%;
    }
  }
</style>
