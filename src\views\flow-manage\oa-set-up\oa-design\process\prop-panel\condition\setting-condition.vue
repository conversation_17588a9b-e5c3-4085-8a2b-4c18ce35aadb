<template>
  <div class="oa-setting-condition">
    <div class="oa-approvel-condition-content">
      <div v-for="(item, index) in conditionList" :key="index">
        <div
          :class="[
            { 'oa-condition-and-top': index === 0 },
            'oa-condition-head'
          ]"
        >
          <div class="oa-condition-and-hr">
            {{ index === 0 ? '条件' : '且' }}
          </div>
          <i @click="deleteCondition(index)" class="el-icon-delete"></i>
        </div>
        <one-condition
          @get-one-condition="(data) => getOneCondition(data, index)"
          :one-list="item"
        ></one-condition>
      </div>
    </div>
    <div @click="addConditon()" class="oa-approvel-condition-button">
      <i class="el-icon-plus"></i>
      <span>添加条件</span>
    </div>
  </div>
</template>

<script>
  import oneCondition from './one-condition.vue';
  import { deepClone } from '@/util/util.js';
  export default {
    name: 'SettingConditon',
    components: { oneCondition },
    props: {
      conditionList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        arrList: []
      };
    },
    watch: {
      conditionList: {
        handler(val) {
          this.arrList = val;
        },
        deep: true
      },
      arrList: {
        handler(val) {
          this.$emit('get-conditions', val);
        },
        deep: true
      }
    },
    created() {
      if (Object.keys(this.conditionList).length !== 0) {
        this.arrList = deepClone(this.conditionList);
      }
    },
    methods: {
      getOneCondition(data, index) {
        this.arrList[index] = data;
      },
      addConditon() {
        this.arrList.push({
          type: '',
          name: '',
          form: {
            condition: '',
            relation: '',
            value: ''
          }
        });
      },
      deleteCondition(index) {
        this.arrList.splice(index, 1);
      }
    }
  };
</script>
<style lang="scss" scoped>
  .oa-setting-condition {
    padding: 16px 24px;

    .oa-condition-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 15px;
      margin-bottom: 16px;

      .oa-condition-and-hr {
        color: #333;
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
      }

      i {
        color: #8e8e8e;
        font-size: 16px;
        line-height: 16px;

        &:hover {
          color: #ff4648;
          cursor: pointer;
        }
      }
    }

    .oa-condition-and-top {
      margin-top: 0;
    }

    .oa-condition-choose-staff {
      width: 100%;
      margin-top: 12px;
    }

    .condition-name,
    .condition-relation {
      margin-bottom: 12px;
    }

    .oa-approvel-condition-button {
      display: flex;
      align-items: center;
      margin-top: 15px;
      color: #fe6b06;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      cursor: pointer;

      i {
        margin-right: 2px;
        font-size: 16px;
      }
    }
  }
</style>
