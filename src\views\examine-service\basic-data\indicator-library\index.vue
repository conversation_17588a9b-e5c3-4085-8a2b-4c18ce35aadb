<template>
  <basic-container>
    <search @query="queryFn" />
    <el-row :gutter="10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="!multipleSelection.length"
          @click="handleDel"
          >删除
        </el-button>
      </el-col>
    </el-row>
    <table-list :source="tableData" @dispatch="dispatch" @choice="choice" />
    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <add-edit v-if="addEdit" :id="id" :title="title" @close="handleClose" />
  </basic-container>
</template>

<script>
  import { Search, TableList, AddEdit } from './components';
  import { getList, postDelete } from '@/api/examine/indicator-library';
  // 指标库
  export default {
    name: 'IndicatorLibrary',
    components: { Search, TableList, AddEdit },
    data() {
      return {
        total: 0,
        queryParams: {
          current: 1,
          size: 10
        },
        tableData: [],
        multipleSelection: [],
        addEdit: false,
        title: '',
        id: undefined
      };
    },
    methods: {
      // 列表操作
      dispatch(type, row) {
        switch (type) {
          case 'edit':
            return this.handleEdit(row);
          case 'delete':
            return this.rowDel(row);
          default:
            return false;
        }
      },
      // 选择的数据
      choice(data) {
        this.multipleSelection = data.map((item) => item.id);
      },
      // 编辑
      handleEdit(row) {
        this.id = row.id;
        this.title = '编辑';
        this.addEdit = true;
      },
      // 删除
      async rowDel(row) {
        try {
          const param = [row.id];
          const { data } = await postDelete(param);
          if (data.success) {
            this.$message.success('删除成功');
            await this.request();
          } else {
            this.$message.warning(data.msg);
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 新增
      handleAdd() {
        this.title = '新增';
        this.addEdit = true;
      },
      // 批量删除
      async handleDel() {
        try {
          const { data } = await postDelete(this.multipleSelection);
          if (data.success) {
            this.multipleSelection = [];
            this.$message.success('删除成功');
            await this.request();
          } else {
            this.$message.warning(data.msg);
          }
        } catch (e) {
          console.log(e);
        }
      },
      // 关闭弹窗
      handleClose(val) {
        this.addEdit = false;
        this.title = '';
        this.id = undefined;
        if (val) {
          this.request();
        }
      },
      // 分页查询
      getList({ page, limit }) {
        console.log(page, limit);
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 查询
      queryFn(params) {
        Object.assign(this.queryParams, params, {
          current: 1,
          size: 10
        });
        this.request();
      },
      // api接口查询
      async request() {
        try {
          const res = await getList(this.queryParams);
          const data = res.data.data;
          const { records, total } = data;
          this.total = total;
          this.tableData = records;
        } catch (e) {
          console.error(e);
        }
      }
    }
  };
</script>
