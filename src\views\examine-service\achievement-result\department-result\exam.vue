<template>
  <div class="wrapper" v-loading="loading">
    <!--  基础信息  -->
    <el-collapse style="border: 0" v-model="collapse">
      <el-collapse-item name="one">
        <h2 slot="title" class="title">基础信息</h2>
        <basic-info
          v-model="detailForm.submitDate"
          :form="detailForm"
          :isDetail="isDetail"
        />
      </el-collapse-item>
    </el-collapse>
    <section>
      <div class="title_wrapper">
        <h2 class="title">业绩明细</h2>
        <el-tooltip effect="dark" content="全屏展示" placement="top-start">
          <i class="el-icon-full-screen icon-full" @click="fullScreen"></i>
        </el-tooltip>
      </div>
      <!--   详情   -->
      <depart-list-detail v-if="isDetail" :list="listData" />
      <!--   评价   -->
      <depart-list
        v-else
        :max-height="300"
        :list="listData"
        @sync="handleExamine"
      />
      <!-- 审批流程 -->
      <div class="title_wrapper">
        <!--   普通展示审批环节   -->
        <div v-if="!isDetail && !isApproval">
          <h2 class="title">审批流程</h2>
          <div style="padding: 30px 70px 10px 40px">
            <init-flow ref="flow" v-if="processId" :id="processId" />
          </div>
        </div>
        <!-- 审批流查看 -->
        <div
          style="padding: 20px 0"
          v-else-if="
            detailForm.processInstanceId &&
            detailForm.status &&
            detailForm.status !== '0'
          "
        >
          <h2 class="title">审批流程</h2>
          <approval-flow
            v-if="approvalId"
            ref="approvalFlow"
            :id="approvalId"
            @repeal="handleRepeal"
            @submit="handleSubmit"
            @close="approvalClose"
          />
        </div>
      </div>

      <div class="end-btn">
        <el-button
          v-if="!isDetail"
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </section>
    <!--   全屏操作   -->
    <full-screen-table v-model="visited" title="业绩明细">
      <depart-list-detail
        v-if="isDetail"
        full
        :list="listData"
        :max-height="0"
      />
      <depart-list
        v-else
        full
        :list="listData"
        :max-height="0"
        @sync="handleExamine"
      />
      <div class="end-btn" v-if="!isApproval">
        <el-button
          v-if="!isDetail"
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </full-screen-table>
  </div>
</template>

<script>
  import { getDeptsRank } from './utils';
  import { BasicInfo, DepartList, DepartListDetail } from './components';
  import { FullScreenTable } from '@/views/examine-service/components/full-screen';
  import {
    getDetail,
    postSave,
    postSubmit,
    postApprove,
    getRepeal
  } from '@/api/examine/depart-results';
  import { getProcessId } from '@/api/flow/process';
  import {
    InitFlow,
    ApprovalFlow
  } from '@/views/examine-service/components/approval';
  import { service_code, service_process } from '@/constant/service';

  export default {
    name: 'depart-result-exam',
    components: {
      BasicInfo,
      DepartList,
      DepartListDetail,
      FullScreenTable,
      InitFlow,
      ApprovalFlow
    },
    props: {
      rowCode: {
        type: String,
        default: ''
      },
      isDetail: {
        type: Boolean,
        default: false
      },
      isApproval: {
        type: Boolean,
        required: true
      }
    },
    data() {
      return {
        collapse: 'one',
        visited: false,
        detailForm: {},
        listData: [],
        // 页面加载
        loading: false,
        // 业务流程id
        processId: '',
        approvalId: '',
        // 保存/提交 按钮状态控制
        loadingSave: false,
        loadingSubmit: false,
        disabled: false
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      // 页面初始化
      async init() {
        this.loading = true;
        await this.getDetail();
        // 填报时, 先获取processId
        if (!this.isDetail && !this.isApproval) {
          await this.getProcessId();
        }
        this.loading = false;
      },
      // 获取详情
      async getDetail() {
        try {
          this.loading = true;
          const res = await getDetail({ code: this.rowCode });
          const data = res.data.data;
          // 获取processInstanceId
          this.approvalId = data.processInstanceId;
          // 获取详情
          this.detailForm = data.pluginForm;
          this.listData = this.detailForm.itemList || [];
          // 如果本周期考核得分为0分, 将系统计算得分赋值给本周期考核得分
          this.listData.map((item) => {
            if (
              item.assessScore === 0 &&
              item.systemCalculateScore !== item.assessScore
            ) {
              item.assessScore = item.systemCalculateScore;
            } else if (item.assessScore === null) {
              item.assessScore = undefined;
            } else if (typeof item.assessScore === 'string') {
              item.assessScore = Number(item.assessScore);
            }
            return item;
          });
          // 根据本周期考核得分获取部门名次
          this.listData = getDeptsRank(this.listData);
          console.log(this.listData);
          // 如果详情里没有填报日期, 置为当前时间
          if (!this.detailForm.submitDate) {
            this.detailForm.submitDate = new Date()
              .toLocaleString()
              .replaceAll('/', '-');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 根据后台定义好的code和key获取processDefinationId
      async getProcessId() {
        try {
          const res = await getProcessId({
            code: service_code,
            key: service_process.depart_result
          });
          this.processId = res.data.data;
        } catch (e) {
          console.error(e);
        }
      },
      fullScreen() {
        this.visited = true;
      },
      // 流程撤销
      async handleRepeal() {
        try {
          const res = await getRepeal(this.approvalId);
          if (res && res.data && res.data.success) {
            this.$message.success('撤回成功');
            this.$emit('close');
            this.$emit('refresh');
          }
          // 重置审批流
          await this.$refs.approvalFlow.resetDetails();
        } catch (e) {
          console.error(e);
        }
      },
      // 审批
      async handleSubmit(approvalData) {
        console.log(approvalData);
        try {
          const formData = {
            ...approvalData,
            pluginForm: this.detailForm
          };
          const res = await postApprove(formData);
          const data = res.data;
          if (data.code === 200) {
            this.$message.success(data.msg);
            this.$emit('close');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 审批关闭
      approvalClose() {
        this.$emit('close');
      },
      // 同步数据
      handleExamine(val) {
        this.listData = val;
      },
      // 提交校验
      validate() {
        const res = { flag: true, msg: '' };
        if (
          this.listData.some((item) => typeof item.assessScore !== 'number')
        ) {
          const index = this.listData.findIndex(
            (item) => !item.assessScore && item.assessScore !== 0
          );
          res.flag = false;
          res.msg = `第${index + 1}行 本周期考核得分 字段不能为空, 请核对`;
        }
        return res;
      },
      // 保存/提交
      async save(type) {
        // 保存
        if (type === 'save') {
          this.postData(type);
        }
        // 提交
        if (type === 'submit') {
          if (!this.detailForm.submitDate) {
            return this.$message.warning('提报日期未选择，请选择！');
          }
          // 判断审批人
          const initFlow = this.$refs.flow.$data.users;
          if (!initFlow.length) {
            return this.$message.warning(
              '审批流程没有审批人节点，请完善审批流程！'
            );
          }
          const isTrue = initFlow.some((item) => {
            if (item.users === null || item.users.length === 0) {
              return true;
            }
          });
          if (isTrue) {
            return this.$message.warning('请选择审批人！');
          }
          // 校验必填项
          const { flag, msg } = this.validate();
          if (!flag) {
            return this.$message.warning(msg);
          }
          this.$confirm('是否确定发起审批？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.postData(type);
            })
            .catch(() => {});
        }
      },
      // 提交数据
      async postData(type) {
        try {
          this.disabled = true;
          if (type === 'save') {
            this.loadingSave = true;
          } else {
            this.loadingSubmit = true;
          }
          const formData = {
            processDefinitionId: this.processId,
            usersNodeinfos: this.$refs.flow.$data.users,
            pluginForm: {
              ...this.detailForm,
              itemList: this.listData
            }
          };
          const apiObj = {
            save: postSave,
            submit: postSubmit
          };
          const res = await apiObj[type](formData);
          const data = res.data;
          if (data.code === 200) {
            this.$message.success(data.msg);
            this.$emit('close');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.disabled = false;
          this.loadingSave = false;
          this.loadingSubmit = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;

    .icon-full {
      position: absolute;
      right: 0;
      bottom: 0;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
