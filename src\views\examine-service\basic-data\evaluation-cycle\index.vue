<template>
  <basic-container>
    <transition-group name="fade">
      <div v-show="!open" :key="1">
        <search @query="queryFn" />
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="!multipleSelection.length"
              @click="handleDel"
              >删除
            </el-button>
          </el-col>
        </el-row>
        <table-list :source="source" @dispatch="dispatch" @choice="choice" />
        <yk-pagination
          small
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.current"
          :limit.sync="queryParams.size"
          @pagination="getList"
        />
      </div>
      <yk-local-model :key="2" :title="modelTitle" :open="open" @close="close">
        <detail v-if="detail" :detail="detail" :id="id" @close="close" />
        <add-edit v-if="addEdit" :id="id" @close="close" />
      </yk-local-model>
    </transition-group>
  </basic-container>
</template>

<script>
  import { Search, TableList, Detail, AddEdit } from './components';
  import { getList, postDelete } from '@/api/examine/cycle';

  // 考核周期
  export default {
    name: 'EvaluationCycle',
    components: { Search, TableList, Detail, AddEdit },
    data() {
      return {
        total: 0,
        queryParams: {
          current: 1,
          size: 10
        },
        open: false,
        modelTitle: '',
        detail: false,
        id: '',
        source: [],
        addEdit: false,
        multipleSelection: [] // 选择的数据
      };
    },
    methods: {
      // 新增
      handleAdd() {
        this.open = true;
        this.id = '';
        this.addEdit = true;
        this.modelTitle = '新增';
      },
      // 批量删除
      async handleDel() {
        try {
          const { data } = await postDelete({ idList: this.multipleSelection });
          if (data.success) {
            this.multipleSelection = [];
            this.$message.success('删除成功');
            await this.request();
          } else {
            this.$message.warning(data.msg);
          }
        } catch (e) {
          console.log(e);
        }
      },
      // 单个删除
      async rowDel(row) {
        try {
          const idList = [row.id];
          const { data } = await postDelete({ idList: idList });
          if (data.success) {
            this.$message.success('删除成功');
            await this.request();
          } else {
            this.$message.warning(data.msg);
          }
        } catch (e) {
          console.log(e);
        }
      },
      // 选择的数据
      choice(data) {
        this.multipleSelection = data.map((item) => item.id);
      },
      // 编辑
      handleEdit(row) {
        this.open = true;
        this.id = row.id;
        this.addEdit = true;
        this.modelTitle = '编辑';
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 查询
      queryFn(params) {
        Object.assign(this.queryParams, params, {
          current: 1,
          size: 10
        });
        this.request();
      },
      // api接口查询
      async request() {
        try {
          const res = await getList(this.queryParams);
          const data = res.data.data;
          const { records, total } = data;
          this.source = records;
          this.total = total;
        } catch (e) {
          console.error(e);
        }
      },
      // 新增, 编辑，详情关闭
      close(bool) {
        this.open = false;
        this.id = '';
        this.modelTitle = '';
        this.detail = false;
        this.addEdit = false;
        if (bool) {
          this.request();
        }
      },
      // 表格操作
      dispatch(type, row) {
        switch (type) {
          case 'detail':
            return this.getDetail(row);
          case 'edit':
            return this.handleEdit(row);
          case 'delete':
            return this.rowDel(row);
          default:
            return false;
        }
      },
      // 获取详情
      getDetail(row) {
        console.log(row);
        this.open = true;
        this.id = row.id;
        this.detail = true;
        this.modelTitle = '详情';
      }
    }
  };
</script>
