<template>
  <div>
    <!--    <el-alert :title="title" type="warning" show-icon :closable="false" />-->
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
      :span-method="spanMethod"
    >
      <el-table-column
        fixed
        prop="serialNumber"
        label="序号"
        width="50"
        align="center"
      >
      </el-table-column>
      <el-table-column fixed label="部门名称" width="120" align="center">
        <template slot-scope="scope">
          {{ scope.row.deptName || '---' }}
        </template>
      </el-table-column>
      <el-table-column fixed label="指标名称" width="150" align="center">
        <template slot-scope="scope">
          {{ scope.row.indexName || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="基本分值" width="80" align="center">
        <template slot-scope="scope">
          {{ scope.row.basicScore || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        label="考核目标及计分标准（点击查看）"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <div
            class="show-text"
            @click="handleDetail(scope.row, 'standard', '考核目标及计分标准')"
          >
            {{ scope.row.standard || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="完成情况（点击查看）"
        align="center"
        prop="completionSituation"
        width="100"
      >
        <template slot-scope="scope">
          <div
            class="show-text"
            @click="handleDetail(scope.row, 'completionSituation', '完成情况')"
          >
            {{ scope.row.completionSituation || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="累计完成（点击查看）" align="center" width="100">
        <template slot-scope="scope">
          <div
            class="show-text"
            @click="
              handleDetail(scope.row, 'accumulatedCompletion', '累计完成')
            "
          >
            {{ scope.row.accumulatedCompletion || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="考核结果（点击查看）"
        align="center"
        prop="assessmentResult"
        width="100"
      >
        <template slot-scope="scope">
          <div
            class="show-text"
            @click="handleDetail(scope.row, 'assessmentResult', '考核结果')"
          >
            {{ scope.row.assessmentResult || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="证明材料" align="center" width="240">
        <template slot-scope="{ row }">
          <show-file
            v-if="row.attachList && row.attachList.length"
            :list="row.attachList"
          ></show-file>
          <span v-else>暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="年度累计考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.accumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="年度累计考核加减分（合计）"
        align="center"
        prop="sum1"
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.sum1 | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="截止上周期累计考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.lastMonthAccumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="截止上周期累计考核加减分（合计）"
        align="center"
        width="100"
        prop="sum2"
      >
        <template slot-scope="scope">
          {{ scope.row.sum2 | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本周期考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本周期考核加减分（合计）" align="center">
        <template slot-scope="scope">
          {{ scope.row.sum3 | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本周期考核得分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="本周期考核得分（合计）"
        align="center"
        prop="sum4"
      >
        <template slot-scope="scope">
          {{ scope.row.sum4 | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="说明（点击输入）" align="center">
        <template slot-scope="scope">
          <div
            v-show="scope.row.comment && scope.row.comment.length"
            class="show-text"
            @click="() => handleInput(scope.row)"
          >
            {{ scope.row.comment || '---' }}
          </div>
          <div
            v-show="!scope.row.comment || !scope.row.comment.length"
            class="placeholder_style"
            @click="() => handleInput(scope.row)"
          >
            点击输入内容
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--  同步文本输入  -->
    <el-dialog
      width="600px"
      :title="headerTitle"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        :placeholder="placeholder"
        show-word-limit
        :maxlength="maxLength"
        :disabled="disabled"
        :rows="12"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
        <el-button
          v-if="!disabled"
          size="small"
          type="primary"
          @click="inputSave"
          >完 成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  import { cloneDeep } from 'lodash';
  import { getSpanNumber } from '@/util/examine';
  import { ShowFile } from '@/components/yk-upload-file';

  export default {
    name: 'kpi-list',
    components: { ShowFile },
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      },
      periodNum: {
        type: Number,
        default: 1
      }
    },
    watch: {
      list: {
        handler(newVal) {
          this.tempList = cloneDeep(newVal);
          this.tempList.map((item) => {
            item.calculatePmScore = Number(item.calculatePmScore);
            return item;
          });
          this.handleSum();
          this.countFunction();
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        tempList: [],
        inputVisited: false,
        tempInput: '',
        temp: {},
        headerTitle: '',
        maxLength: 50,
        disabled: false,
        count: 0,
        placeholder: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      },
      deptObj() {
        let res = {};
        this.tempList.forEach((item) => {
          if (!res[item.deptId]) {
            res[item.deptId] = [];
          }
          res[item.deptId].push(item);
        });
        return Object.values(res);
      },
      title() {
        return `共 ${this.deptObj.length} 个被评价机构 ，已评价 ${this.count} 个`;
      }
    },
    mounted() {
      document.addEventListener(
        'resize',
        () => {
          this.bindProps();
        },
        false
      );
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      // 同步数据
      handleChange() {
        this.$emit('sync', this.tempList);
      },
      // 合并单元格
      spanMethod({ rowIndex, columnIndex }) {
        let spanAry = ['index', 'deptId'];
        if (
          columnIndex < spanAry.length ||
          columnIndex === 10 ||
          columnIndex === 12 ||
          columnIndex === 14 ||
          columnIndex === 16
        ) {
          //合并相同的名字
          let itemSpan = getSpanNumber(this.tempList, 'deptId');
          return {
            rowspan: itemSpan[rowIndex],
            colspan: 1
          };
        }
      },
      // 统计评价状态
      countFunction() {
        this.count = this.deptObj.filter((dept) =>
          dept.every((item) => typeof item.calculatePmScore === 'number')
        ).length;
      },
      // 编辑-说明/备注
      handleInput(row) {
        this.inputVisited = true;
        this.temp = row;
        this.tempInput = row.comment;
        this.headerTitle = '说明';
        this.maxLength = 50;
        this.disabled = false;
        this.placeholder = '请输入说明';
      },
      // 弹窗查看
      handleDetail(row, field, title) {
        this.inputVisited = true;
        this.tempInput = row[field];
        this.headerTitle = title;
        this.maxLength = 1000;
        this.disabled = true;
      },
      // 关闭
      inputClose() {
        this.inputVisited = false;
        this.tempInput = '';
        this.placeholder = '';
      },
      // 备注保存
      inputSave() {
        this.tempList.map((item) => {
          if (item.id === this.temp.id) {
            item.comment = this.tempInput;
          }
          return item;
        });
        this.handleChange();
        this.inputVisited = false;
      },
      // 计算合计
      handleSum() {
        this.tempList.map((item) => {
          item.lastMonthAccumulatedPmScore = Number(
            item.lastMonthAccumulatedPmScore
          );
          item.accumulatedPmScore = Number(item.accumulatedPmScore || 0);
          item.currentPmScore = Number(item.currentPmScore || 0);
          item.currentScore = Number(item.currentScore || 0);
          item.sum1 = 0;
          item.sum2 = 0;
          item.sum3 = 0;
          item.sum4 = 0;
          return item;
        });
        let sum1 = {},
          sum2 = {},
          sum3 = {},
          sum4 = {};
        this.tempList.forEach((item) => {
          if (sum1[item.deptId] === undefined) {
            sum1[item.deptId] = item.accumulatedPmScore;
          } else {
            sum1[item.deptId] = sum1[item.deptId] + item.accumulatedPmScore;
          }

          if (sum2[item.deptId] === undefined) {
            sum2[item.deptId] = item.lastMonthAccumulatedPmScore;
          } else {
            sum2[item.deptId] =
              sum2[item.deptId] + item.lastMonthAccumulatedPmScore;
          }

          if (sum3[item.deptId] === undefined) {
            sum3[item.deptId] = item.currentPmScore;
          } else {
            sum3[item.deptId] = sum3[item.deptId] + item.currentPmScore;
          }

          if (sum4[item.deptId] === undefined) {
            sum4[item.deptId] = item.currentScore;
          } else {
            sum4[item.deptId] = sum4[item.deptId] + item.currentScore;
          }
        });

        this.tempList.map((item) => {
          item.sum1 = sum1[item.deptId];
          item.sum2 = sum2[item.deptId];
          item.sum3 = sum3[item.deptId];
          item.sum4 = sum4[item.deptId];
          return item;
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }

  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
    cursor: pointer;
  }

  .placeholder_style {
    color: #8a8a8a;
    cursor: pointer;
  }
</style>
