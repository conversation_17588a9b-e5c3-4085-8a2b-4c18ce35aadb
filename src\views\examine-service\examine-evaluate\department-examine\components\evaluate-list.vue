<template>
  <div>
    <!--    <el-alert :title="title" type="warning" show-icon :closable="false" />-->
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column type="index" label="序号" width="50" align="center">
      </el-table-column>
      <el-table-column
        label="被评价机构"
        prop="deptName"
        width="250"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="原始分"
        align="center"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.originalScore"
            size="small"
            style="width: 100%"
            controls-position="right"
            :min="0"
            @change="(val) => handleBase(val, scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="扣分"
        align="center"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.deductScore"
            size="small"
            style="width: 100%"
            controls-position="right"
            :min="0"
            @change="countFunction"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { mapState } from 'vuex';
  export default {
    name: 'evaluate-list',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      list: {
        handler(newVal) {
          this.tempList = cloneDeep(newVal);
          let count = 0;
          this.tempList.forEach((item) => {
            if (
              typeof item.originalScore === 'number' &&
              typeof item.deductScore === 'number'
            ) {
              count += 1;
            }
          });
          this.count = count;
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        temp: {},
        tempList: {},
        count: 0
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      },
      title() {
        return `共 ${this.tempList.length} 个被评价机构 ，已评价 ${this.count} 个`;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      handleBase(num, row) {
        if (typeof num === 'number') {
          if (num >= 90) {
            if (typeof row.deductScore !== 'number') {
              row.deductScore = 0;
            }
          } else {
            if (typeof row.deductScore !== 'number') {
              row.deductScore = undefined;
            }
          }
        }
        this.countFunction();
      },
      // 统计评价状态
      countFunction() {
        let count = 0;
        this.tempList.forEach((item) => {
          if (
            typeof item.originalScore === 'number' &&
            typeof item.deductScore === 'number'
          ) {
            count += 1;
          }
        });
        this.count = count;
        this.handleChange();
      },
      // 同步数据
      handleChange() {
        this.$emit('sync', this.tempList);
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }
</style>
