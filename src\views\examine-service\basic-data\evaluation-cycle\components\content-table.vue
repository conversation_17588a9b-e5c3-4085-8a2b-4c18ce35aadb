<template>
  <div>
    <el-table border :data="list" size="small" style="width: 100%">
      <el-table-column type="index" label="序号" align="center" width="60">
      </el-table-column>
      <el-table-column prop="itemName" label="期间名称" align="center">
        <template slot-scope="scope">
          <div v-if="detail">{{ scope.row.itemName || '--' }}</div>
          <div v-else>
            <el-input
              type="text"
              v-model="scope.row.itemName"
              placeholder="请输入名称"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开始时间" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.startTime || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="end" label="结束时间" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.endTime || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="deadline" label="考评截止日期" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.deadline || '--' }}</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { cloneDeep } from 'lodash';
  export default {
    name: 'content-table',
    props: {
      source: {
        type: Array,
        default() {
          return [];
        }
      },
      detail: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        value: '',
        list: [
          {
            name: '牛逼1',
            start: '2022-06-22',
            end: '2022-09-21'
          },
          {
            name: '牛逼2',
            start: '2022-09-22',
            end: '2022-12-21'
          },
          {
            name: '牛逼3',
            start: '2022-12-22',
            end: '2023-03-21'
          },
          {
            name: '牛逼4',
            start: '2023-03-22',
            end: '2023-06-21'
          }
        ]
      };
    },
    watch: {
      source: {
        handler(arr) {
          this.list = cloneDeep(arr);
        },
        deep: true
      }
    },
    methods: {}
  };
</script>

<style lang="scss" scoped>
  .handleClick {
    cursor: pointer;
  }
</style>
