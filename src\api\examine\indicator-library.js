import request from '@/router/axios';

// 指标库 分页查询
export function getList(params) {
  return request({
    url: '/api/examine/index/page',
    method: 'get',
    params
  });
}

// 指标库--新增
export function postAdd(data) {
  return request({
    url: '/api/examine/index/save',
    method: 'post',
    data
  });
}

// 指标库--编辑
export function postEdit(data) {
  return request({
    url: '/api/examine/index/update',
    method: 'post',
    data
  });
}

// 指标库--详情
export function getDetail(id) {
  return request({
    url: `/api/examine/index/detail/${id}`,
    method: 'get'
  });
}

// 指标库--删除
export function postDelete(data) {
  return request({
    url: `/api/examine/index/remove`,
    method: 'post',
    data
  });
}
