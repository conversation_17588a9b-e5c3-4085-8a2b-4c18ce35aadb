<template>
  <div class="wrapper" v-loading="loading">
    <!--  基础信息  -->
    <el-collapse style="border: 0" v-model="collapse">
      <el-collapse-item name="one">
        <h2 slot="title" class="title">基础信息</h2>
        <basic-info
          v-model="detailForm.submitDate"
          :form="detailForm"
          :isDetail="isDetail"
        />
      </el-collapse-item>
    </el-collapse>
    <section>
      <div class="title_wrapper">
        <h2 class="title">业绩明细</h2>
        <el-tooltip effect="dark" content="全屏展示" placement="top-start">
          <i
            class="el-icon-full-screen icon-full"
            @click="() => (visited = true)"
          ></i>
        </el-tooltip>
      </div>
      <!--   详情   -->
      <el-tabs v-model="activeName">
        <template v-if="isDetail">
          <el-tab-pane label="经济效益目标" name="eco">
            <eco-list-detail :list="detailForm.economyList" :max-height="300" />
          </el-tab-pane>
          <el-tab-pane label="KPI指标" name="kpi">
            <kpi-list-detail :list="detailForm.kpiList" :max-height="300" />
          </el-tab-pane>
          <el-tab-pane label="工作效能" name="efficiency">
            <efficiency-list-detail
              :list="detailForm.workList"
              :max-height="300"
            />
          </el-tab-pane>
        </template>
        <template v-else>
          <el-tab-pane label="经济效益目标" name="eco">
            <eco-list
              :list="detailForm.economyList"
              :max-height="300"
              :period-num="periodNum"
              @sync="(val) => (detailForm.economyList = val)"
            />
          </el-tab-pane>
          <el-tab-pane label="KPI指标" name="kpi">
            <kpi-list
              :list="detailForm.kpiList"
              :max-height="300"
              :period-num="periodNum"
              @sync="(val) => (detailForm.kpiList = val)"
            />
          </el-tab-pane>
          <el-tab-pane label="工作效能" name="efficiency">
            <efficiency-list
              :list.sync="detailForm.workList"
              :max-height="300"
            />
          </el-tab-pane>
        </template>
      </el-tabs>
      <!--   审批环节展示   -->
      <div class="title_wrapper">
        <!--   审批详情     -->
        <div
          v-if="isDetail && !isApproval && detailForm.processInstanceId"
          style="padding: 10px 8px 10px 0"
        >
          <approval-flow
            :id="detailForm.processInstanceId"
            @repeal="handleRepeal"
            @close="$emit('close')"
          />
        </div>
        <!--   审批   -->
        <div
          v-if="isDetail && isApproval && detailForm.processInstanceId"
          style="padding-bottom: 20px"
        >
          <h2 class="title">审批流程</h2>
          <approval-flow
            :id="detailForm.processInstanceId"
            @submit="handleSubmit"
            @close="$emit('close')"
          />
        </div>
        <!--   普通展示审批环节   -->
        <div v-if="!isDetail && !isApproval">
          <h2 class="title">审批流程</h2>
          <div style="padding: 30px 70px 10px 40px">
            <init-flow v-if="processId" :id="processId" ref="initFlow" />
          </div>
        </div>
      </div>
      <!--   全屏操作   -->
      <full-screen-table v-model="visited" :title="fullTitle">
        <template v-if="isDetail">
          <eco-list-detail
            v-if="activeName === 'eco'"
            full
            :list="detailForm.economyList"
            :max-height="0"
          />
          <kpi-list-detail
            v-if="activeName === 'kpi'"
            full
            :list="detailForm.kpiList"
            :max-height="0"
          />
          <efficiency-list-detail
            v-if="activeName === 'efficiency'"
            full
            :list="detailForm.workList"
            :max-height="0"
          />
        </template>
        <template v-else>
          <eco-list
            v-if="activeName === 'eco'"
            full
            :list="detailForm.economyList"
            :max-height="0"
            :period-num="periodNum"
            @sync="(val) => (detailForm.economyList = val)"
          />
          <kpi-list
            v-if="activeName === 'kpi'"
            full
            :list="detailForm.kpiList"
            :max-height="0"
            :period-num="periodNum"
            @sync="(val) => (detailForm.kpiList = val)"
          />
          <efficiency-list
            v-if="activeName === 'efficiency'"
            full
            :list.sync="detailForm.workList"
            :max-height="0"
          />
        </template>
      </full-screen-table>

      <div class="end-btn" v-if="!isApproval">
        <el-button
          v-if="!isDetail"
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </section>
  </div>
</template>

<script>
  import { dateFormat } from '@/util/date';
  import { service_process, service_code } from '@/constant/service';
  import {
    BasicInfo,
    EcoList,
    EcoListDetail,
    KpiList,
    KpiListDetail,
    EfficiencyList,
    EfficiencyListDetail
  } from './components';
  import {
    postSave,
    postSubmit,
    getDetail,
    getProcessId,
    postApprove,
    getRevocation
  } from '@/api/examine/economic-kpi-result';
  import { FullScreenTable } from '@/views/examine-service/components/full-screen';
  import {
    InitFlow,
    DetailFlow,
    ApprovalFlow
  } from '@/views/examine-service/components/approval';
  export default {
    name: 'evaluate',
    components: {
      BasicInfo,
      EcoList,
      EcoListDetail,
      KpiList,
      KpiListDetail,
      EfficiencyList,
      EfficiencyListDetail,
      FullScreenTable,
      InitFlow,
      DetailFlow,
      ApprovalFlow
    },
    props: {
      isDetail: {
        type: Boolean,
        required: true
      },
      isApproval: {
        type: Boolean,
        required: true
      },
      code: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        service_code,
        collapse: 'one',
        visited: false,
        activeName: 'eco',
        detailForm: {
          code: '',
          submitDate: undefined,
          economyList: [],
          kpiList: [],
          workList: []
        },
        processId: '',
        approvalId: '',
        temp: {},
        periodNum: 0,
        loadingSave: false,
        loadingSubmit: false,
        disabled: false,
        loading: false
      };
    },
    computed: {
      fullTitle() {
        return this.activeName === 'eco' ? '经济效益目标' : 'KPI指标(工作效能)';
      }
    },
    async mounted() {
      await this.init();
      if (!this.isDetail && !this.isApproval) {
        await this.getProcess();
      }
    },
    methods: {
      // 获取流程定义id
      async getProcess() {
        try {
          const res = await getProcessId({
            code: service_code,
            key: service_process.economy_kip_result
          });
          this.processId = res.data.data;
        } catch (e) {
          console.error(e);
        }
      },
      // 获取详情
      async init() {
        try {
          this.loading = true;
          const res = await getDetail({
            code: this.code
          });
          const data = res.data.data;
          this.temp = data;
          this.detailForm = data.pluginForm;
          if (!this.detailForm.submitDate && !this.isDetail) {
            this.detailForm.submitDate = dateFormat(new Date(), 'yyyy-MM-dd');
          }
          // 经济效益数据
          this.detailForm.economyList.map((item) => {
            item.systemCalculatePmScore =
              Number(item.systemCalculatePmScore) || 0;
            item.calculatePmScore = Number(item.calculatePmScore) || 0;
            item.accumulatedPmScore = Number(item.accumulatedPmScore) || 0;
            return item;
          });
          // KPI指标数据
          this.detailForm.kpiList.map((item) => {
            item.systemCalculatePmScore =
              Number(item.systemCalculatePmScore) || 0;
            item.calculatePmScore = Number(item.calculatePmScore) || 0;
            item.accumulatedPmScore = Number(item.accumulatedPmScore) || 0;
            return item;
          });
          // 工作效能数据
          this.detailForm.workList.map((item) => {
            item.assessScore = Number(item.assessScore) || 0;
            return item;
          });
          const economyList = this.detailForm.economyList || [];
          const kpiList = this.detailForm.kpiList || [];
          // 计算考核周期数
          const ecoListLen = economyList.length;
          const ecoSystem =
            (ecoListLen && economyList[0].systemCalculatePmScore) || 0;
          const ecoCalculate =
            (ecoListLen && economyList[0].calculatePmScore) || 0;
          const ecoAccumulated =
            (ecoListLen && economyList[0].accumulatedPmScore) || 0;

          const kpiListLen = kpiList.length;
          const kpiSystem =
            (kpiListLen && kpiList[0].systemCalculatePmScore) || 0;
          const kpiCalculate = (kpiListLen && kpiList[0].calculatePmScore) || 0;
          const kpiAccumulated =
            (kpiListLen && kpiList[0].accumulatedPmScore) || 0;

          if (ecoListLen && ecoSystem === ecoCalculate) {
            this.periodNum = ecoAccumulated / ecoSystem;
          } else if (ecoListLen && ecoSystem !== ecoCalculate) {
            this.periodNum = ecoAccumulated / ecoCalculate;
          } else if (kpiListLen && kpiSystem === kpiCalculate) {
            this.periodNum = kpiAccumulated / kpiSystem;
          } else if (kpiListLen && kpiSystem !== kpiCalculate) {
            this.periodNum = kpiAccumulated / kpiCalculate;
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      save(type) {
        const initFlow = this.$refs.initFlow.$data.users;
        const data = Object.assign({}, this.temp, {
          processDefinitionId: this.processId,
          pluginForm: Object.assign({}, this.detailForm, {
            performanceCode: this.detailForm.code
          }),
          usersNodeinfos: initFlow
        });
        // 保存
        if (type === 'save') {
          this.saveData(data);
        }
        // 提交
        if (type === 'submit') {
          if (!initFlow.length) {
            return this.$message.warning(
              '审批流程没有审批人节点，请完善审批流程！'
            );
          }

          if (!this.detailForm.submitDate) {
            return this.$message.warning('提报日期未选择，请选择！');
          }

          const isTrue = initFlow.some((item) => {
            if (item.users === null || item.users.length === 0) {
              return true;
            }
          });
          // 选择审批人校验
          if (isTrue) {
            return this.$message.warning('请选择审批人！');
          }

          if (
            this.detailForm.economyList.length === 0 ||
            this.detailForm.kpiList.length === 0 ||
            this.detailForm.workList.length === 0
          ) {
            return this.$message.warning('存在未提交的评价单！');
          }
          // 经济效益目标
          const ecoStatus = this.detailForm.economyList.some((item) => {
            if (typeof item.calculatePmScore !== 'number') {
              return true;
            }
          });

          if (ecoStatus) {
            return this.$message.warning('请完善经济效益目标列表数据！请核对');
          }

          // KPI指标
          const kipStatus = this.detailForm.kpiList.some((item) => {
            if (typeof item.calculatePmScore !== 'number') {
              return true;
            }
          });

          if (kipStatus) {
            return this.$message.warning('请完善KPI指标列表数据！请核对');
          }

          // 工作效能
          const efficiencyStatus = this.detailForm.workList.some((item) => {
            if (typeof item.assessScore !== 'number') {
              return true;
            }
          });

          if (efficiencyStatus) {
            return this.$message.warning('请完善工作效能列表数据！请核对');
          }

          // 自行选择审批人校验
          if (initFlow) this.submitData(data);
        }
      },
      // 保存
      async saveData(data) {
        try {
          this.disabled = true;
          this.loadingSave = true;
          const res = await postSave(data);
          const { code } = res.data;
          if (code === 200) {
            this.$message.success('数据保存成功！');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.disabled = false;
          this.loadingSave = false;
        }
      },
      // 提交
      async submitData(data) {
        try {
          this.disabled = true;
          this.loadingSubmit = true;
          const res = await postSubmit(data);
          const { code } = res.data;
          if (code === 200) {
            this.$message.success('数据提交成功！');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.disabled = false;
          this.loadingSubmit = false;
        }
      },
      // 撤回
      async handleRepeal() {
        try {
          const res = await getRevocation({
            processInstanceId: this.detailForm.processInstanceId
          });
          if (res.data.code === 200) {
            this.$message.success('评价撤回成功！');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 审批
      async handleSubmit(val) {
        const pluginForm = Object.assign({}, this.detailForm, {
          performanceCode: this.detailForm.code
        });
        const data = Object.assign({}, val, {
          pluginForm: pluginForm
        });
        try {
          const res = await postApprove(data);
          if (res.data.code === 200) {
            this.$message.success('审批成功！');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;

    .icon-full {
      position: absolute;
      right: 0;
      bottom: 0;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
