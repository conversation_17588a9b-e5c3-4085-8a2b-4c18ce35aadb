<template>
  <h-image-upload
    :value="value"
    multiple
    show-loading
    show-file-list
    disabled
    :image-width="80"
    :image-height="80"
    class="bc-apply-component-image"
  >
  </h-image-upload>
</template>

<script>
  export default {
    name: 'ImageComp',
    props: {
      value: {
        type: Array,
        default() {
          return [];
        }
      }
    }
  };
</script>

<style lang="scss">
  .bc-apply-component-image {
    &.h-image-upload {
      .h-image-upload-multiple {
        .image-area {
          .image-list {
            .image-block {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
</style>
