<template>
  <span class="wrapper">
    <el-button
      v-if="!btnTitle.length && circle"
      size="mini"
      type="primary"
      :circle="circle"
      :icon="icon"
      :disabled="disabled"
      @click="visible = true"
    ></el-button>
    <el-button
      v-else
      size="mini"
      type="primary"
      :icon="icon"
      :disabled="disabled"
      @click="visible = true"
      >{{ btnTitle }}</el-button
    >
    <!--  弹框  -->
    <el-dialog
      :title="modelTitle"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      width="60%"
    >
      <div v-if="visible">
        <el-row :gutter="15">
          <el-col :md="16" :lg="16">
            <div class="list_wrapper">
              <search @search="queryText" />
              <!--   人员列表   -->
              <list
                :loading="loading"
                :tableData="tableData"
                @selectEmit="selectFn"
              />
              <yk-pagination
                small
                v-show="total > 0"
                :total="total"
                :page.sync="pages.pageNum"
                :limit.sync="pages.pageSize"
                :page-sizes="[10, 20]"
                @pagination="getList"
              />
            </div>
          </el-col>
          <el-col :md="8" :lg="8" class="h100">
            <!--  已选择人员  -->
            <select-people :list="personArr" @removeEmit="removeFn" />
          </el-col>
        </el-row>
      </div>
      <span slot="footer">
        <el-button size="small" @click="cancel">返 回</el-button>
        <el-button type="primary" size="small" @click="ok">确 定</el-button>
      </span>
    </el-dialog>
  </span>
</template>

<script>
  import Search from './search.vue';
  import List from './list';
  import SelectPeople from './select';
  import { getList as getUserList } from '@/api/system/user';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'Person',
    components: {
      Search,
      List,
      SelectPeople
    },
    props: {
      // 按钮标题
      btnTitle: {
        type: String,
        default: '人员选择'
      },
      // 弹框标题
      modelTitle: {
        type: String,
        default: '选择人员'
      },
      // 是否为圆形按钮
      circle: {
        type: Boolean,
        default: false
      },
      // 选择按钮 icon
      icon: {
        type: String,
        default: 'el-icon-s-custom'
      },
      // 已选人员数组
      userArr: {
        type: Array,
        default: function () {
          return [];
        }
      },
      // 单选/多选 默认多选
      single: {
        type: Boolean,
        default: false
      },
      appendToBody: {
        type: Boolean,
        default: true
      },
      secondDept: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      async visible(val) {
        if (val) {
          let arr = this.userArr;
          if (this.single && arr.length > 1) {
            arr.length = 1;
          }
          this.personArr = arr.length ? cloneDeep(arr) : [];
          await this.request();
        } else {
          this.queryUser = '';
        }
      }
    },
    data() {
      return {
        visible: false,
        loading: false,
        total: 0,
        pages: {
          pageNum: 1,
          pageSize: 10
        },
        queryUser: undefined,
        tableData: [],
        personArr: []
      };
    },
    methods: {
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.pages, {
          pageNum: page,
          pageSize: limit
        });
        this.request();
      },
      // 返回按钮
      cancel() {
        this.visible = false;
        this.total = 0;
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        this.queryUser = '';
        this.tableData.splice(0, this.tableData.length);
      },
      // 确定按钮
      ok() {
        this.$emit('result', this.personArr);
        this.cancel();
      },
      // 名字检索
      queryText(queryUser) {
        this.queryUser = queryUser;
        Object.assign(this.pages, {
          pageNum: 1,
          pageSize: 10
        });
        this.request();
      },
      // 人员改动
      selectFn({ id, realName }, status) {
        if (status) {
          // 新增
          if (this.single) {
            this.personArr = [];
            this.tableData.map((item) => {
              item.status = id === item.id;
              return item;
            });
          }
          this.personArr.push({ id, name: realName });
        } else {
          // 移除
          const index = this.personArr.findIndex((item) => item.id === id);
          if (index !== -1) {
            this.personArr.splice(index, 1);
          }
        }
      },
      // 删除人员
      removeFn(index, id) {
        this.personArr.splice(index, 1);
        // 修改状态
        this.tableData.map((item) => {
          if (id === item.id) {
            item.status = false;
          }
          return item;
        });
      },
      // 请求人员列表
      async request() {
        this.loading = true;
        const params = {
          realName: this.queryUser
        };
        try {
          const res = await getUserList(
            this.pages.pageNum,
            this.pages.pageSize,
            params
          );
          const data = res.data.data;
          this.total = data.total;
          this.tableData = data.records.map((item) => {
            item.status = this.personArr.some(
              (person) => person.id === item.id
            );
            return item;
          });
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .h100 {
    height: 100%;
  }

  .list_wrapper {
    width: 100%;
    padding-bottom: 20px;
    overflow: hidden;
  }
</style>
