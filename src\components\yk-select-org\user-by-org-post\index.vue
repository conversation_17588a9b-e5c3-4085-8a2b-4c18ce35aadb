<template>
  <el-dialog
    :title="title"
    width="80%"
    :visible.sync="visited"
    append-to-body
    :close-on-click-modal="false"
  >
    <div style="height: 500px" v-if="visited">
      <el-row :gutter="15">
        <el-col :span="6">
          <div class="title">部门</div>
          <div style="max-height: 460px; overflow: auto">
            <dept-com
              v-bind="$attrs"
              :dept-ids="deptIds"
              :single="single"
              @emitDept="deptFn"
            />
          </div>
        </el-col>
        <el-col :span="6">
          <div class="title">岗位</div>
          <post-com :postIds="postIds" :single="single" @emitPost="postFn" />
        </el-col>
        <el-col :span="6" class="user_wrapper">
          <div class="title">人员</div>
          <el-table
            :data="userList"
            style="width: 100%"
            height="460"
            size="small"
            v-loading="loading"
          >
            <el-table-column prop="name" label="姓名" />
            <el-table-column
              prop="deptName"
              label="部门"
              show-tooltip-when-overflow
            />
            <el-table-column
              prop="postName"
              label="岗位"
              show-tooltip-when-overflow
            />
            <el-table-column
              prop="action"
              label="操作"
              width="80"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.ok"
                  @change="(bool) => switchFn(bool, scope.$index)"
                >
                </el-switch>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="6">
          <div class="title">已选取人员（{{ dynamicUsers.length }}）</div>
          <div class="user-container">
            <el-tooltip
              :key="tag"
              v-for="tag in dynamicUsers"
              effect="dark"
              :content="`${tag.deptName} - ${tag.postName}`"
              placement="top-start"
            >
              <el-tag style="margin: 5px">
                {{ tag.name }}
              </el-tag>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visited = false" size="small">返 回</el-button>
      <el-button type="primary" size="small" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import DeptCom from './dept-com';
  import PostCom from './post-com';
  import { getUserByDeptAndPost } from '@/api/examine/evaluation-scheme';

  export default {
    name: 'yk-select-user',
    components: { PostCom, DeptCom },
    data() {
      return {
        deptIds: [],
        deptIdData: [],
        postIds: [],
        postIdData: [],
        userList: [],
        loading: false
      };
    },
    computed: {
      visited: {
        set(bool) {
          this.deptIds = [];
          this.deptIdData = [];
          this.postIds = [];
          this.postIdData = [];
          this.userList = [];
          this.$emit('close', bool);
        },
        get() {
          return this.open;
        }
      },
      dynamicUsers: {
        get() {
          return this.userList.filter((item) => item.ok);
        }
      }
    },
    props: {
      title: String,
      open: {
        type: Boolean,
        default: false
      },
      rowData: {
        type: Object,
        default() {
          return {};
        }
      },
      single: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      rowData: {
        handler(data) {
          if (this.visited && data.assessedDeptList && data.assessedPostList) {
            this.deptIds = data.assessedDeptList.map((item) => item.id);
            this.postIds = data.assessedPostList.map((item) => item.id);
            this.deptIdData = data.assessedDeptList.map(
              ({ id, name: title }) => ({ id, title })
            );
            this.postIdData = data.assessedPostList.map(
              ({ id, name: postName }) => ({ id, postName })
            );
          }
        },
        deep: true
      }
    },
    methods: {
      // 根据机构和岗位获取被评价人
      async getUserByDeptAndPost(isInit = false) {
        if (this.deptIds.length === 0 || this.postIds.length === 0) {
          this.userList = [];
          return;
        }
        this.loading = true;
        try {
          const params = {
            deptIdList: this.deptIds,
            postIdList: this.postIds
          };
          const res = await getUserByDeptAndPost(params);
          const data = res.data.data;
          if (data.length === 0) {
            this.userList.splice(0, this.userList.length);
            this.$message.warning('当前机构及岗位下暂无被评价人');
            return;
          }
          if (this.single) {
            this.userList = data.map((item) => {
              console.log(isInit, this.rowData.assessedUserList);

              if (
                isInit &&
                this.rowData.assessedUserList &&
                this.rowData.assessedUserList.length
              ) {
                const isOk = this.rowData.assessedUserList.some((user) => {
                  return (
                    user.id === item.userId &&
                    user.deptId === item.deptId &&
                    user.postId === item.postId
                  );
                });
                console.log(isOk);
                item.ok = isOk;
              } else {
                item.ok = false;
              }
              return item;
            });
          } else {
            this.userList = data.map((item) => {
              if (
                isInit &&
                this.rowData.assessedUserList &&
                this.rowData.assessedUserList.length
              ) {
                const isOk = this.rowData.assessedUserList.some((user) => {
                  return (
                    user.id === item.userId &&
                    user.deptId === item.deptId &&
                    user.postId === item.postId
                  );
                });
                item.ok = isOk;
              } else {
                item.ok = true;
              }
              return item;
            });
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 部门选择返回值
      deptFn(deptArr) {
        this.deptIdData = deptArr;
        this.deptIds = deptArr.map((item) => item.id);
        this.getUserByDeptAndPost();
      },
      // 岗位选择返回值
      postFn(postArr, isInit = false) {
        this.postIdData = postArr;
        this.postIds = postArr.map((item) => item.id);
        this.getUserByDeptAndPost(isInit);
      },
      // 部门，岗位，人员选取
      onSubmit() {
        // 判断是否选择被评价人
        if (!this.dynamicUsers.length) {
          return this.$message.warning('请根据机构和岗位生成至少一个被评价人');
        }
        // 根据选择被评价人筛选机构及岗位
        this.$emit(
          'emitSubmit',
          this.dynamicUsers,
          this.deptIdData,
          this.postIdData
        );
        this.visited = false;
      },
      // 人员选取
      switchFn(bool, idx) {
        if (this.single) {
          this.userList.map((item, index) => {
            item.ok = false;
            if (idx === index) {
              item.ok = bool;
            }
            return item;
          });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    box-sizing: border-box;
    margin: 0;
    padding: 6px 0 6px 10px;
    font-weight: 600;
    font-size: 14px;
    line-height: 26px;
    background-color: #e3e3e3;
    border-radius: 5px;
  }

  .user-container {
    max-height: 460px;
    overflow: auto;
  }
</style>
