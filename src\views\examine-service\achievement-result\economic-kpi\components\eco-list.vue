<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column
        type="index"
        label="序号"
        width="50"
        align="center"
        fixed
      />
      <el-table-column label="指标名称" align="center" width="150" fixed>
        <template slot-scope="scope">
          {{ scope.row.indexName || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="基本分值" align="center" width="80" fixed>
        <template slot-scope="scope">
          {{ scope.row.basicScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="考核目标及计分标准（点击查看）"
        align="center"
        width="260"
      >
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleDetail2(scope.row)">
            {{ scope.row.standard || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" width="60">
        <template slot-scope="scope">
          {{ scope.row.unit || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="年度指标" align="center">
        <template slot-scope="scope">
          <div>
            <strong style="font-size: 13px">考核目标</strong>
            <div class="text-value">
              {{
                (scope.row.yearlyTargetCopy &&
                  scope.row.yearlyTargetCopy.yearBasicTarget) ||
                '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">奋斗目标</strong>
            <div class="text-value">
              {{
                (scope.row.yearlyTargetCopy &&
                  scope.row.yearlyTargetCopy.yearFightTarget) ||
                '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">力争目标</strong>
            <div class="text-value">
              {{
                (scope.row.yearlyTargetCopy &&
                  scope.row.yearlyTargetCopy.yearBestTarget) ||
                '---'
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="截止本考核周期指标" align="center">
        <template slot-scope="scope">
          <div>
            <strong style="font-size: 13px">考核目标</strong>
            <div class="text-value">
              {{
                (scope.row.currentTargetCopy &&
                  scope.row.currentTargetCopy.currentBasicTarget) ||
                '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">奋斗目标</strong>
            <div class="text-value">
              {{
                (scope.row.currentTargetCopy &&
                  scope.row.currentTargetCopy.currentFightTarget) ||
                '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">力争目标</strong>
            <div class="text-value">
              {{
                (scope.row.currentTargetCopy &&
                  scope.row.currentTargetCopy.currentBestTarget) ||
                '---'
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="完成情况（点击查看）" align="center" width="100">
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleDetail1(scope.row)">
            {{ scope.row.completionSituation || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="累计完成" align="center" width="90">
        <template slot-scope="scope">
          {{ scope.row.accumulatedCompletion || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="考核结果（点击查看）" align="center" width="100">
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleDetail3(scope.row)">
            {{ scope.row.assessmentResult || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="年度累计考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.accumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="截止上周期累计考核加减分"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.lastMonthAccumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本考核周期考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本考核周期考核得分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="说明（点击输入）" align="center">
        <template slot-scope="scope">
          <div
            v-show="scope.row.comment && scope.row.comment.length"
            class="show-text"
            @click="() => handleInput(scope.row)"
          >
            {{ scope.row.comment || '---' }}
          </div>
          <div
            v-show="!scope.row.comment || !scope.row.comment.length"
            class="placeholder_style"
            @click="() => handleInput(scope.row)"
          >
            点击输入内容
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--  同步文本输入  -->
    <el-dialog
      width="600px"
      :title="headerTitle"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        :placeholder="placeholder"
        show-word-limit
        :maxlength="maxLength"
        :disabled="disabled"
        :rows="12"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
        <el-button
          v-if="!disabled"
          size="small"
          type="primary"
          @click="inputSave"
          >完 成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { mapState } from 'vuex';
  export default {
    name: 'eco-list',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      },
      periodNum: {
        type: Number,
        default: 1
      }
    },
    watch: {
      list: {
        handler(newVal) {
          this.tempList = cloneDeep(newVal);
          this.tempList.map((item) => {
            item.calculatePmScore = Number(item.calculatePmScore);
            item.yearlyTargetCopy = JSON.parse(item.yearlyTarget || '{}');
            item.currentTargetCopy = JSON.parse(item.currentTarget || '{}');
            return item;
          });
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        tempList: [],
        inputVisited: false,
        tempInput: '',
        temp: {},
        headerTitle: '',
        maxLength: 50,
        disabled: false,
        placeholder: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      // 同步数据
      handleChange() {
        this.$emit('sync', this.tempList);
      },
      // 编辑-说明/备注
      handleInput(row) {
        this.inputVisited = true;
        this.temp = row;
        this.tempInput = row.comment;
        this.headerTitle = '说明';
        this.maxLength = 50;
        this.disabled = false;
        this.placeholder = '请输入说明';
      },
      // 完成情况查看
      handleDetail1(row) {
        this.inputVisited = true;
        this.tempInput = row.completionSituation;
        this.headerTitle = '完成情况';
        this.maxLength = 1000;
        this.disabled = true;
      },
      // 考核目标及计分标准查看
      handleDetail2(row) {
        this.inputVisited = true;
        this.tempInput = row.standard;
        this.headerTitle = '考核目标及计分标准';
        this.maxLength = 1000;
        this.disabled = true;
      },
      // 考核结果查看
      handleDetail3(row) {
        this.inputVisited = true;
        this.tempInput = row.assessmentResult;
        this.headerTitle = '考核结果';
        this.maxLength = 1000;
        this.disabled = true;
      },
      // 关闭
      inputClose() {
        this.inputVisited = false;
        this.tempInput = '';
        this.placeholder = '';
      },
      // 备注保存
      inputSave() {
        this.tempList.map((item) => {
          if (item.id === this.temp.id) {
            item.comment = this.tempInput;
          }
          return item;
        });
        this.handleChange();
        this.inputVisited = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }

  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
    cursor: pointer;
  }

  .placeholder_style {
    color: #8a8a8a;
    cursor: pointer;
  }
</style>
