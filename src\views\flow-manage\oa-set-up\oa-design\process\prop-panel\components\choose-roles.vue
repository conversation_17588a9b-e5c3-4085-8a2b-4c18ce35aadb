<template>
  <el-dialog
    @open="getAppList"
    :title="title"
    :visible.sync="hVisible"
    width="544px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="app-center-dialog"
  >
    <div v-loading="loading" class="tranferbox">
      <div class="conbox">
        <div class="all-select">
          <el-checkbox v-model="checkAll" :indeterminate="indeterminate">
            全选
          </el-checkbox>
        </div>
        <div class="titbox">
          <el-input
            v-model="filterText"
            size="mini"
            placeholder="搜索"
            suffix-icon="el-icon-search"
          ></el-input>
        </div>
        <div class="wordbox">
          <el-tree
            ref="tree"
            @check="onCheck"
            :data="data"
            show-checkbox
            node-key="id"
            default-expand-all
            :check-strictly="true"
            :check-on-click-node="true"
            :filter-node-method="filterNode"
            class="filter-tree"
          >
            <div slot-scope="{ data }">
              <div class="check-item">
                <div class="check-info">
                  <div class="check-info-content">
                    {{ data.roleName }}
                  </div>
                </div>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div class="conbox">
        <div class="all-select">
          <div class="all-select-value">
            已选 {{ checkedNodes.length }} 个角色
          </div>
        </div>
        <div class="wordbox choose-roles">
          <div class="dept-selected">
            <div v-for="item in checkedNodes" :key="item.value">
              <div class="check-item">
                <div class="check-info">
                  <div class="check-info-content">
                    {{ item.roleName }}
                  </div>
                </div>
                <i @click="removeData(item)" class="iconfont icon-default"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="button-area">
      <el-button @click="handleClose">返回</el-button>
      <el-button @click="submit" type="primary">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import { getUserRole } from '@/api/system/post';
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      defaultCheckedCodes: {
        type: Array,
        default() {
          return [];
        }
      },
      title: {
        type: String,
        default: '选择角色'
      }
    },
    data() {
      return {
        loading: false,
        data: [
          { label: '主管理员', value: 1 },
          { label: '子管理员', value: 2 },
          { label: '部门管理员', value: 3 }
        ],
        appList: [],
        checkedNodes: [],
        filterText: ''
      };
    },
    computed: {
      hVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      },
      checkAll: {
        get() {
          return this.checkedNodes.length === this.appList.length;
        },
        set(val) {
          this.checkedNodes = val ? this.appList : [];
          this.$refs.tree.setCheckedNodes(this.checkedNodes);
        }
      },
      indeterminate() {
        return (
          !!this.checkedNodes.length &&
          this.checkedNodes.length < this.appList.length
        );
      }
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      }
    },
    created() {
      this.getData();
    },
    methods: {
      async getData() {
        this.loading = true;
        getUserRole()
          .then((data) => {
            if (data && data.data && data.data.success) {
              this.data = data.data.data;
            } else if (data && data.data && !data.data.success) {
              this.$message.error(data.data.message);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      getAppList() {
        this.filterText = '';
        this.appList = this.data;
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.setCheckedKeys(this.defaultCheckedCodes);
            this.onCheck();
          }
        });
      },
      filterNode(value, data) {
        if (!value) {
          return true;
        }
        return data.label.indexOf(value) !== -1;
      },
      // 点击选择
      onCheck() {
        this.checkedNodes = this.$refs.tree.getCheckedNodes();
      },
      // 删除已选
      removeData(data) {
        let filterArr = this.checkedNodes.filter(
          (item) => item.value !== data.value
        );
        this.checkedNodes = filterArr;
        this.$refs.tree.setChecked(data, false);
      },
      submit() {
        // 返回参数：1.ids；2.list
        if (this.checkedNodes.length) {
          this.$emit('change', this.checkedNodes);
          this.handleClose();
        } else {
          this.$message.warning('请选择角色');
        }
      },
      handleClose() {
        this.hVisible = false;
      }
    }
  };
</script>
<style lang="scss">
  .app-center-dialog {
    .el-tree-node__expand-icon.is-leaf {
      display: none;
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background-color: transparent !important;
    }

    .el-tree {
      .el-tree-node {
        padding: 0 16px 0 30px;

        &:hover {
          background: #f5f7fa;
        }

        .el-tree-node__content {
          height: auto;

          &:hover {
            background: none;
            cursor: default;
          }
        }
      }
    }

    .button-area {
      text-align: center;

      button {
        width: 140px;
        height: 40px;
      }
    }

    .el-dialog {
      .el-dialog__body {
        padding-bottom: 0 !important;
      }
    }
  }
</style>
<style lang="scss" scoped>
  .tranferbox {
    display: flex;
    justify-content: space-between;

    .conbox {
      display: flex;
      flex-direction: column;
      width: 240px;
      height: 388px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;

      .all-select {
        padding: 12px 16px;
        color: #333;
        font-weight: 400;
        font-size: 14px;
        line-height: 19px;
        border-bottom: 1px solid #d9d9d9;

        .all-select-value {
          color: #666;
        }
      }

      .titbox {
        padding: 12px 16px;
      }

      .wordbox {
        // padding: 0 16px;
        flex: 1;
        overflow: auto;

        .check-item {
          display: flex;
          align-items: center;
          padding: 12px 0;

          .check-logo {
            width: 36px;
            height: 36px;
            margin-right: 8px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .check-info {
            flex: 1;

            .check-info-title {
              width: 116px;
              margin-bottom: 6px;
              overflow: hidden;
              color: #333;
              font-weight: 400;
              font-size: 14px;
              line-height: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .check-info-content {
              width: 180px;
              overflow: hidden;
              color: #333;
              font-weight: 400;
              font-size: 14px;
              line-height: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }

      .choose-roles {
        padding-top: 10px;
      }

      .dept-selected {
        // padding: 16px;
        .check-item {
          position: relative;
          padding: 0 16px;

          .icon-close-circle {
            position: absolute;
            right: 16px;
            width: 16px;
            height: 16px;
            cursor: pointer;
          }

          .check-info-content {
            padding: 9px 0;
          }

          &:hover {
            background: #f5f7fa;
          }

          .icon-default {
            color: #a0a0a0;
            cursor: pointer;

            &:hover {
              color: #6a6a6a;
            }
          }
        }
      }
    }
  }
</style>
