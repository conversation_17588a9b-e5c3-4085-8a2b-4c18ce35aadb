// 全局变量
@import './variables';

// ele样式覆盖
@import './element-ui';

// 顶部右侧显示
@import './top';

// 导航标签
@import './tags';

// 工具类函数
@import './mixin';

// 侧面导航栏
@import './sidebar';

// 动画
@import './animate/vue-transition';

// 主题
@import './theme/index';

// 适配
@import './media';

// 通用配置
@import './normalize';

// 右下角消息弹窗
@import './notification';

a{
  color: #333;
  text-decoration: none;
}

*{
  outline: none;
}

// 滚动条样式
@include scrollBar;
