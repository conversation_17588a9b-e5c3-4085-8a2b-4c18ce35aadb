//合并单元格 - 获取要合并的行数
export function getSpanNumber(data, prop) {
  //数组的长度，有时候后台可能返回个null而不是[]
  let length = Array.isArray(data) ? data.length : 0;
  if (length > 0) {
    //用于标识位置
    let position = 0;
    //用于对比的数据
    let temp = data[0][prop];
    //要返回的结果
    let result = [1];
    //假设数据是AABCC，我们的目标就是返回20120
    for (let i = 1; i < length; i++) {
      if (data[i][prop] == temp) {
        //标识位置的数据加一
        result[position] += 1;
        //当前位置添0
        result[i] = 0;
      } else {
        //不相同时，修改标识位置，该位置设为1，修改对比值
        position = i;
        result[i] = 1;
        temp = data[i][prop];
      }
    }
    //返回结果
    return result;
  } else {
    return [0];
  }
}
