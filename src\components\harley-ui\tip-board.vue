<template>
  <div class="h-tip-board">
    <div class="tip-title">{{ title }}</div>
    <div class="tip-main">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'HTipBoard',
    props: {
      title: String
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-ui';

  .h-tip-board {
    padding: 20px 0;

    // color: $--color-text-regular;
    background: #edf8ff;
    border-radius: 4px;

    .tip-title {
      margin: 0 30px;
      padding-bottom: 5px;
      font-weight: 550;
      font-size: 16px;
    }

    .tip-main {
      margin: 0 30px;
      line-height: 22px;
    }
  }
</style>
