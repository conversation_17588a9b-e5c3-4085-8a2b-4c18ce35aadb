<template>
  <el-dialog
    v-loading="dialogLoading"
    :visible.sync="createGroupVisible"
    append-to-body
    :before-close="closeDialog"
    width="460px"
    class="createGroup"
    title="新建分组"
  >
    <section>
      <p>分组名称：</p>
      <el-input v-model.trim="resetName" size="small" maxlength="20"></el-input>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">返回</el-button>
      <el-button @click="submit" type="primary">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
  import { resetName, createName } from '@/api/flow/group';

  export default {
    props: {
      createGroupVisible: {
        type: Boolean,
        default: false
      },
      group: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        dialogLoading: false,
        resetName: ''
      };
    },
    watch: {
      createGroupVisible() {
        if (this.createGroupVisible) {
          this.resetName = this.group.groupName;
        } else {
          this.resetName = '';
        }
      }
    },
    methods: {
      submit() {
        if (!this.resetName) {
          this.$message.error('请输入分组名称');
          return;
        }
        const url = this.group.groupId ? resetName : createName;
        this.dialogLoading = true;
        url(this.resetName, this.group.groupId)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.$message.success(
                this.group.groupId ? '修改成功' : '新建成功'
              );
              this.$emit('update:createGroupVisible', false);
              this.$emit('updateList');
            }
          })
          .finally(() => {
            this.dialogLoading = false;
          });
      },
      closeDialog() {
        this.$emit('update:createGroupVisible', false);
        //   this.dialogVisible = false;
      }
    }
  };
</script>
<style lang="scss">
  .createGroup {
    section {
      display: flex;
      align-items: center;
      p {
        width: 93px;
      }
    }
  }
</style>
