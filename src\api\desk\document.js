/*
 * @description:
 * @param:
 * @author: Fei
 * @return:
 * @Date: 2021-01-18 10:34:17
 */
import request from '@/router/axios';

/**
 * 获取文件列表
 * @param {String} size 每页显示数量
 * @param {Boolean} page 当前显示的页数
 */
export const getFileList = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/attiladoc/page-org',
    method: 'get',
    params
  });
};
/**
 * 获取群文件列表
 * @param {String} size 每页显示数量
 * @param {Boolean} page 当前显示的页数
 */
export const getGroupFileList = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/attiladoc/page-group',
    method: 'get',
    params
  });
};

// 获取后台文件预览地址
export const getViewUrlDbPath = (params) => {
  return request({
    url: '/wpsapi/file/getViewUrlDbPath',
    method: 'get',
    params
  });
};
/**
 * 删除文档(单删/批量删)
 */
export const removeData = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/attiladoc/remove',
    method: 'post',
    data: params
  });
};
/**
 * 获取当前目录的删除记录查询
 * @param {String} parentId 文件夹id
 * @param {Boolean} docName 文件名称
 * @param {String} size 每页显示数量
 * @param {Boolean} page 当前显示的页数
 */
export const getRecordRemoveList = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/attiladoc/record-remove',
    method: 'get',
    params
  });
};
/**
 * 获取新建文档wpsUrl
 * @param {String} id 文件夹id
 * @param {String} fileType 文件类型传值word、excel、ppt
 * @param {String} folderId 文件夹id
 */
export const getCreateWordUrl = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/wps/get-template-wps-url',
    method: 'get',
    params
  });
};

/**
 * 获取编辑、预览文档wpsUrl
 * @param {String} id 文件夹id
 * @param {String} editType 编辑类型：read预览；write可编辑
 * @param {String} folderId 文件夹id
 */
export const getWpsUrl = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/wps/get-wps-url',
    method: 'get',
    params
  });
};

/**
 * 校验文件的大小和名称是否重复
 * @param {String} fileName 文件名称
 * @param {String} fileSize 文件大小
 * @param {String} folderId 文件夹id
 */
export const checkFileNameSize = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/attiladoc/check-file-before-upload',
    method: 'get',
    params
  });
};

/**
 * 获取文件详情
 * @param {String} fileName 文件名称
 * @param {String} fileSize 文件大小
 * @param {String} folderId 文件夹id
 */
export const getFileDetail = (params) => {
  return request({
    // url: "/wpsapi/file/getFileListByPage",
    url: '/api/attila-resource/attiladoc/detail',
    method: 'get',
    params
  });
};
