<template>
  <basic-container>
    <h-block title="审批管理" class="approval">
      <template v-if="!$store.getters.isVisitor">
        <app-tab
          v-loading="loading"
          @set-tab-value="setTabValue"
          @refresh="search"
        ></app-tab>
        <h-search-bar @search="onSearch" @reset="reset">
          <el-form-item label="审批名称：">
            <el-input
              v-model="form.processName"
              clearable
              placeholder="请输入审批名称"
              maxlength="100"
              show-word-limit
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="摘要：">
            <el-input
              v-model="form.processName"
              clearable
              placeholder="请输入摘要"
              maxlength="100"
              show-word-limit
            ></el-input>
          </el-form-item> -->
          <el-form-item label="审批分组：">
            <h-select
              v-model="form.groupId"
              :data-source="statusList"
              :props="prop"
              placeholder="请选择审批分类"
            ></h-select>
          </el-form-item>
          <el-form-item v-if="tabValue !== 3" label="发起人：">
            <el-input
              v-model="form.createUserName"
              clearable
              placeholder="请输入发起人"
              maxlength="100"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="发起时间：">
            <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm"
            />
          </el-form-item>
          <el-form-item v-if="tabValue !== 1" label="完成时间：">
            <el-date-picker
              v-model="cTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm"
            />
          </el-form-item>
        </h-search-bar>
        <h-operation-bar :button-list="buttonList" :link-list="linkList">
          <h-table
            ref="table"
            v-loading="loading"
            @size-change="sizeChange"
            @pagination-current-change="currentChange"
            @sort-change="sortChange"
            :show-overflow-tooltip="true"
            :data="data"
            :columns="tableColumns"
            reserve-selection
            row-key="id"
            show-pagination
            :page-size="size"
            :current-page="current"
            :total="total"
            :all-selectable="false"
          >
            <template slot="left" slot-scope="{ row }">
              <span @click="goApply(row)" class="formName">
                {{ processName(row) }}
              </span>
            </template>
            <div slot="abstract" slot-scope="{ row }" class="abstract">
              <div v-if="!row.isForm">
                <div
                  v-for="(l, i) of row.newDataJson"
                  :key="i"
                  v-html="getAbstractHtml(l)"
                />
              </div>
              <span v-else>---</span>
            </div>
            <template slot="statusName" slot-scope="{ row }">
              <i
                class="iconfont icon-status_dot"
                :class="statusColor[row.status]"
              ></i>
              <span>
                {{ row.statusStr }}
              </span>
            </template>
            <el-table-column v-if="tabValue === 4" label="已读/未读">
              <template slot-scope="{ row }">
                <i
                  class="iconfont icon-status_dot"
                  :class="isReadColor[row.isRead]"
                ></i>
                <span v-if="row.isRead === 1">已读</span>
                <span v-else>未读</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="tabValue === 1"
              fixed="right"
              label="操作"
              width="120"
            >
              <template slot-scope="{ row }">
                <el-button @click="goApply(row)" type="text" size="small"
                  >审批</el-button
                >
              </template>
            </el-table-column>
          </h-table>
        </h-operation-bar>
      </template>
      <create-organization-page v-else></create-organization-page>
    </h-block>
  </basic-container>
</template>
<script>
  import appTab from './approval/app-tab';
  import { processList, groupList, readAllCC } from '@/api/flow/process';
  import createOrganizationPage from './approval/create-organization-page.vue';
  import { trim } from '@/util/util';

  import './approval/index.scss';

  const TABLE_COLUMNS = [
    { label: '审批名称', slotName: 'left', showOverflowTooltip: true },
    { label: '摘要', slotName: 'abstract' },
    { label: '审批分组', value: 'groupName' },
    { label: '发起人', value: 'createUserName' },
    { label: '发起组织', value: 'orgName' },
    {
      label: '发起时间',
      value: 'createDate',
      sortable: 'custom',
      width: '180px'
    },
    { label: '审批状态', value: 'statusName', slotName: 'statusName' }
  ];

  const STATUS_COLOR = ['blue', 'green', 'red', 'gray', 'red', 'red'];
  const ISREAD_COLOR = ['', 'green', 'red'];

  export default {
    components: { appTab, createOrganizationPage },
    data() {
      return {
        statusColor: STATUS_COLOR,
        isReadColor: ISREAD_COLOR,
        form: {
          processName: '',
          createUserName: '',
          groupId: '',
          startTime: '',
          endTime: '',
          cStartTime: '',
          cEndTime: '',
          seq: ''
        },
        prop: {
          value: 'id',
          label: 'groupName'
        },
        buttonList: [
          {
            caption: '发起审批',
            icon: 'el-icon-plus',
            callback: this.goNewApproval,
            type: 'primary'
          }
        ],
        data: [],
        tabValue: 1,
        statusList: [],
        applyList: [],
        tableColumns: TABLE_COLUMNS,
        size: 10,
        current: 1,
        total: 0,
        loading: false,
        keyword: '',
        interval: null
      };
    },
    computed: {
      linkList() {
        return [
          {
            caption: '全部已读',
            callback: this.readAllCC,
            disabled: this.tabValue !== 4
          }
        ];
      },
      time: {
        get() {
          let { startTime = '', endTime = '' } = this.form;
          return [startTime, endTime];
        },
        set(val) {
          let [startTime = '', endTime = ''] = val || [];
          this.$set(this.form, 'startTime', startTime);
          this.$set(this.form, 'endTime', endTime);
        }
      },
      cTime: {
        get() {
          let { cStartTime = '', cEndTime = '' } = this.form;
          return [cStartTime, cEndTime];
        },
        set(val) {
          let [cStartTime = '', cEndTime = ''] = val || [];
          this.$set(this.form, 'cStartTime', cStartTime);
          this.$set(this.form, 'cEndTime', cEndTime);
        }
      }
    },
    mounted() {
      if (!this.$store.getters.isVisitor) {
        this.typeSelect();
        this.search();
      }
    },
    methods: {
      processName(row) {
        let name = row.processName;
        if (row.oldProcessId) {
          name = `销假-${row.processName}`;
        }
        return name;
      },
      // 表单审批分类下拉
      typeSelect() {
        groupList().then((res) => {
          if (res && res.data && res.data.success) {
            this.statusList = res.data.data;
          }
        });
      },
      goApply({ processInstanceId, isOld, flowableInstanceId }) {
        let jump;
        if (isOld) {
          jump = this.$router.resolve({
            name: 'applyOldDetail',
            params: { id: flowableInstanceId }
          });
        } else {
          jump = this.$router.resolve({
            name: 'apply',
            // params: { id: processInstanceId },
            query: {
              id: processInstanceId,
              ...this.form,
              tabNum: this.tabValue
            }
          });
        }
        this.$router.push(jump.location);
      },
      onSearch() {
        this.current = 1;
        this.search();
      },
      search() {
        this.keyword = '';
        const params = {
          queryType: this.tabValue,
          processName: trim(this.form.processName),
          createUserName: trim(this.form.createUserName),
          groupId: this.form.groupId,
          startTime: this.form.startTime,
          endTime: this.form.endTime,
          cStartTime: this.form.cStartTime,
          cEndTime: this.form.cEndTime,
          seq: this.form.seq,
          current: this.current,
          size: this.size
        };
        this.loading = true;
        processList(params)
          .then((res) => {
            this.keyword = this.form.processName;
            if (res && res.data && res.data.data && res.data.data.records) {
              this.data = res.data.data.records;
              this.total = res.data.data.total;
              if (this.current > 1 && !this.data.length) {
                this.current = parseInt(this.total / this.size, 10);
                this.search();
              }
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      setTabValue(value) {
        this.tabValue = value;
        this.form = {
          processName: '',
          createUserName: '',
          groupId: '',
          startTime: '',
          endTime: '',
          cStartTime: '',
          cEndTime: '',
          seq: ''
        };
        this.current = 1;
        if (this.$refs.table) {
          this.$refs.table.clearSort();
        }
        this.search();
      },
      getAbstractHtml(l) {
        let str = `${l.key || '--'}：${l.value || '--'}`;
        if (!this.keyword) {
          return str;
        }
        let arr = str.split(this.keyword);
        let str2 = '';
        arr.forEach((item, index) => {
          if (index) {
            str2 += `<span class="search-keyword">${this.keyword}</span>`;
          }
          str2 += item;
        });
        return str2;
      },
      reset() {
        this.current = 1;
        this.form = {
          processName: '',
          groupId: '',
          createUserName: '',
          startTime: '',
          endTime: '',
          cStartTime: '',
          cEndTime: '',
          seq: ''
        };
        this.search();
      },
      goNewApproval() {
        this.$router.push({ name: 'newApproval' });
      },
      currentChange(currentPage) {
        this.current = currentPage;
        this.search();
      },
      sizeChange(size) {
        this.size = size;
        this.search();
      },
      readAllCC() {
        this.$confirm('是否将所有抄送我的流程全部置为已读？', '提示')
          .then(() => {
            return readAllCC();
          })
          .then(() => {
            this.$message.success('操作成功');
            this.$store.dispatch('cornerMark');
          });
      },
      sortChange({ prop, order }) {
        if (prop === 'createDate') {
          this.form.seq = !order ? '' : order === 'ascending' ? '2' : '1';
          this.search();
        }
      }
    }
  };
</script>
