import { setStore, getStore } from '@/util/store';

const commonComp = {
  state: {
    font:
      getStore({
        name: 'font'
      }) || 13
  },
  mutations: {
    SET_ADD_FONT(state) {
      state.font += 1;
      setStore({
        name: 'font',
        content: state.font
      });
    },
    SET_REDUCE_FONT(state) {
      state.font -= 1;
      setStore({
        name: 'font',
        content: state.font
      });
    }
  }
};

export default commonComp;
