<template>
  <el-table
    size="small"
    row-key="id"
    :data="list"
    :tree-props="{ children: 'children', hasChildren: 'hasChildrens' }"
    @selection-change="handleSelections"
  >
    <el-table-column type="selection" width="55"> </el-table-column>
    <el-table-column
      v-if="columns[0].visible"
      prop="deptName"
      align="left"
      label="机构名称"
      :show-overflow-tooltip="true"
    ></el-table-column>
    <el-table-column
      v-if="columns[1].visible && website.tenantMode"
      prop="tenantId"
      align="left"
      label="所属租户"
      :show-overflow-tooltip="true"
    >
      <template slot-scope="{ row }">
        {{ getTenantNameById(row.tenantId) }}
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[2].visible"
      prop="fullName"
      label="机构全称"
      :show-overflow-tooltip="true"
    >
    </el-table-column>
    <el-table-column
      v-if="columns[3].visible"
      prop="deptCategoryName"
      align="center"
      label="机构类型"
      width="80px"
    >
      <template slot-scope="scope">
        <el-tag>{{ scope.row.deptCategoryName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[4].visible"
      width="100"
      prop="sort"
      align="center"
      label="排序"
      :show-overflow-tooltip="true"
    ></el-table-column>
    <el-table-column fixed="right" align="center" label="操作" width="250">
      <template slot-scope="scope">
        <el-button
          v-if="permission.system_dept_view"
          type="text"
          icon="el-icon-view"
          size="small"
          @click="$emit('dispatch', 'view', scope.row)"
          >查看</el-button
        >
        <el-button
          v-if="permission.system_dept_edit"
          type="text"
          icon="el-icon-edit"
          size="small"
          @click="$emit('dispatch', 'edit', scope.row)"
          >编辑</el-button
        >
        <el-button
          v-if="permission.system_dept_delete"
          type="text"
          icon="el-icon-delete"
          size="small"
          style="color: red"
          @click="$emit('dispatch', 'delete', scope.row)"
          >删除</el-button
        >
        <el-button
          v-if="
            permission.system_dept_add && userInfo.role_name.includes('admin')
          "
          type="text"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click="$emit('dispatch', 'add_child', scope.row)"
          >新增子项</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { findByvalue } from '@/util/util';
  import website from '@/config/website';

  export default {
    name: 'dept-table',
    props: {
      columns: {
        type: Array,
        default: function () {
          return [];
        }
      },
      list: {
        type: Array,
        default: function () {
          return [];
        }
      },
      tenantData: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    data() {
      return {
        website
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    },
    methods: {
      handleSelections(rows) {
        const ids = rows.map((item) => item.id);
        this.$emit('dispatch', 'selections', ids);
      },
      getTenantNameById(id) {
        return findByvalue(this.tenantData, id);
      }
    }
  };
</script>
