import Vue from 'vue';
import DataDict from '@/util/dict/service-dict';
import { getDictionary as getDicts } from '@/api/system/dictbiz';

function install() {
  Vue.use(DataDict, {
    metas: {
      '*': {
        labelField: 'dictValue',
        valueField: 'dictKey',
        serviceRequest(dictMeta) {
          return getDicts(dictMeta.type).then((res) => res.data);
        }
      }
    }
  });
}

export default {
  install
};
