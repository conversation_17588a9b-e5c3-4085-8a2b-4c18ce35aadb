/* stylelint-disable scss/dollar-variable-pattern */
/* stylelint-disable selector-pseudo-element-no-unknown */
.theme-vip {
  $color: rgba(246,202,157,70%);
  $is_active_color: #f6ca9d;

  .avue-header{
    background-color: #20222a;
  }

  .el-dropdown{
    color: $color;
  }

  .avue-logo{
    .avue-logo_title{ 
      font-weight: 400;
      background-image: -webkit-gradient(linear,left top,left bottom,from($color),to( $is_active_color)); 
      background-clip: text; 
      -webkit-text-fill-color: transparent;
    }
  }

  .avue-breadcrumb{
    i{
      color: $color;
    }
  }

  .avue-sidebar{
    .el-menu-item{
      &.is-active {
          &::before {
           background: $color;
          }

          i,span{
            color: $is_active_color;
        }
      }
    }
  }

  .avue-tags{
    .el-tabs__item{
      color: rgba(0, 0, 0, 40%)  !important;

      &.is-active{
        color: $is_active_color !important;
        border-color: $is_active_color !important;
      }

      &::before{
        background: $is_active_color;
      }
    } 
  }

  .top-search {
    .el-input__inner{
      color: $color;
    }

    input::input-placeholder,
    textarea::input-placeholder {
        /* WebKit browsers */
        color: $color;
    }

    input:placeholder,
    textarea:placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: $color;
    }

    input::placeholder,
    textarea::placeholder {
        /* Mozilla Firefox 19+ */
        color: $color;
    }

    input:input-placeholder,
    textarea:input-placeholder {
        /* Internet Explorer 10+ */
        color: $color;
    }
}

  .top-bar__item {
    i {
      color: $color;
    }
  }

  .avue-top{
   
    .el-menu-item {
        i,
        span {
          color: $color;
        }

      &:hover {
          i,
          span {
            color: $is_active_color;
          }
      }
    }
  }
}