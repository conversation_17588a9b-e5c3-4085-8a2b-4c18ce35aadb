<template>
  <el-tabs
    v-model="activeName"
    v-loading="loading"
    class="oaSetUp"
    :before-leave="beforeLeave"
  >
    <el-tab-pane name="cancel">
      <div slot="label">
        <i @click="cancel" class="el-icon-arrow-left"></i>
      </div>
    </el-tab-pane>
    <el-tab-pane label="基本信息" name="information">
      <information
        ref="information"
        @update-error-number="getValidateNumber"
        :information-details="informationDetails"
      ></information>
    </el-tab-pane>
    <el-tab-pane label="表单设计" name="formDesign">
      <form-design
        ref="formDesign"
        @update-error-number="getValidateNumber"
        :form-design-list="formDesignList"
      ></form-design>
    </el-tab-pane>
    <el-tab-pane label="流程设计" name="third">
      <oa-design
        ref="oaDesign"
        @update-error-number="getValidateNumber"
        :oa-design-details="oaDesignDetails"
        :rule-list="ruleList"
      ></oa-design>
    </el-tab-pane>
    <el-tab-pane name="save">
      <div slot="label">
        <span v-if="errorNumber" class="errorTip"
          >{{ errorNumber }}条内容不完善</span
        >
        <!-- <el-button @click="preview" size="small">预览</el-button> -->
        <el-button @click="submit" size="small" type="primary">发布</el-button>
      </div>
    </el-tab-pane>
    <el-dialog
      title="提示"
      :visible.sync="hVisible"
      width="360px"
      append-to-body
      :close-on-click-modal="false"
      class="previewCode"
    >
      <div v-if="clearCode" ref="qrCodeUrl"></div>
      <div class="footer">
        <img v-oss src="/login/qr-icon.png" /> APP扫一扫预览
      </div>
    </el-dialog>
  </el-tabs>
</template>

<script>
  import information from './oa-set-up/information';
  import formDesign from '@/components/new-form-design';
  import oaDesign from './oa-set-up/oa-design';
  import QRCode from 'qrcodejs2';
  // import { getRuleList } from '@/api/desk/flow';
  import {
    processDefinitionDetail,
    create,
    qrCreate
  } from '@/api/flow/process';

  export default {
    components: { information, formDesign, oaDesign },
    data() {
      return {
        activeName: 'information',
        errorNumber: 0,
        hVisible: false,
        clearCode: true,
        informationDetails: {},
        oaDesignDetails: {},
        dataId: '',
        formDesignList: [],
        loading: false,
        ruleList: []
      };
    },
    watch: {
      hVisible() {
        if (!this.hVisible) {
          this.clearCode = false;
          this.$nextTick(() => {
            this.clearCode = true;
          });
        }
      }
    },
    created() {
      if (this.$route.query.id) {
        // 20221025
        // getRuleList().then((res) => {
        //   let { data } = res.data;
        //   data.map((item) => {
        //     this.ruleList.push({ value: item.ruleName });
        //   });
        // });
        processDefinitionDetail(this.$route.query.id).then((res) => {
          if (res && res.data && res.data.success) {
            const data = res.data.data;
            this.dataId = data.dataId;
            this.formDesignList = data.formInfo;

            this.informationDetails = {
              icon: data.icon,
              processName: data.processName,
              remark: data.remark,
              groupId: data.groupId,
              isForm: data.isForm !== null ? data.isForm : 0,
              startScopeType: data.startScopeType,
              startScopeIds: data.startScopeIds,
              startDetailNames: data.startDetailNames,
              ids: data.ids || []
            };

            this.oaDesignDetails = {
              informDeptIds: data.informDeptIds,
              informJobIds: data.informJobIds,
              informPostIds: data.informPostIds,
              informUserIds: data.informUserIds,
              // 兼容老数据
              processJson: data.nodeNewJson || data.processJson,
              personsAll: data.personsAll,
              nodeFieldList: data.nodeFieldList
            };
          }
        });
      }
    },
    beforeDestroy() {
      this.$store.commit('CLEAR_FORM');
    },
    methods: {
      beforeLeave(currentName) {
        if (['save', 'cancel'].includes(currentName)) {
          return false;
        }
      },
      getValidateNumber(val) {
        this.$store.commit('SET_VALIDATE');
        this.$nextTick(() => {
          const information = this.$refs.information.validate() || 0;
          const formDesign = this.$refs.formDesign.validate() || 0;
          const oaDesign =
            this.$refs.oaDesign.validate(val ? true : false) || 0;
          this.errorNumber = information + formDesign + oaDesign;
        });
      },
      async preview() {
        const obj = await this.submitData();
        if (!obj) {
          return;
        }

        this.loading = true;
        qrCreate(obj)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.hVisible = true;
              this.$nextTick(() => {
                this.codeCreate(res.data.data);
              });
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      codeCreate(text) {
        this.qrcode = new QRCode(this.$refs.qrCodeUrl, {
          text,
          width: 160,
          height: 160,
          colorDark: '#000000',
          colorLight: '#ffffff'
        });
      },
      async submitData() {
        await this.getValidateNumber();
        if (!this.errorNumber) {
          const information = this.$refs.information.submit();
          const formInfo = this.$refs.formDesign.submit();
          const oaDesign = this.$refs.oaDesign.submit();
          // const isForm = this.$refs.information.form.isForm; // 是否固定表单 1 是 0 否

          if (!formInfo.length) {
            this.$message({
              type: 'error',
              message: '请选择表单',
              duration: 1500
            });
            return false;
          }
          if (!oaDesign.processJson) {
            this.$message({
              type: 'error',
              message: '请选择流程',
              duration: 1500
            });
            return false;
          }
          const obj = { ...information, formInfo, ...oaDesign };

          if (this.dataId && this.$route.query.type !== 'copy') {
            obj.dataId = this.dataId;
          }
          return obj;
        }
        return false;
      },
      async submit() {
        const obj = await this.submitData();
        if (!obj) {
          return;
        }

        this.loading = true;
        create(obj)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.$message.success('发布成功');
              this.cancel();
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      findTag(value) {
        let tag, key;
        let tagList = this.$store.state.tags.tagList;
        tagList.map((item, index) => {
          if (item.value === value) {
            tag = item;
            key = index;
          }
        });
        return { tag: tag, key: key };
      },
      cancel() {
        let { tag } = this.findTag(this.$route.fullPath);
        this.$store.commit('DEL_TAG', tag);
        setTimeout(() => {
          this.$router.push({ name: 'applyManager' });
        }, 500);
      }
    }
  };
</script>
<style lang="scss">
  .oaSetUp {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 1280px !important;
    height: 100%;
    margin-top: 0 !important;
    background: #f0f2f5;

    .el-tabs__header {
      width: 100%;
      margin-bottom: 0 !important;
      background: #fff;

      // box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 12%);
      .el-tabs__nav-scroll {
        display: flex;
        justify-content: center;
      }

      .el-tabs__nav {
        width: 100%;
        line-height: 64px;

        & > div {
          color: #999;
          font-size: 18px;
        }

        .is-active {
          color: #333;
        }
      }
    }

    .el-tab-pane {
      height: 100%;

      & > div {
        height: 100%;
      }
    }

    #tab-cancel {
      margin-left: 32px;
      padding-right: 0;

      i {
        color: #333;
        font-weight: bold;
        font-size: 16px;
      }
    }

    #tab-information {
      margin-left: calc(50% - 198px);
    }

    &.el-tabs {
      > .el-tabs__header {
        .el-tabs__nav-wrap {
          .el-tabs__nav-scroll {
            .el-tabs__nav {
              .el-tabs__active-bar {
                margin-left: calc(50% - 165px);
              }
            }
          }
        }
      }

      > .el-tabs__content {
        flex: 1;
        width: 100%;
        height: 100%;
      }
    }

    #tab-save {
      position: absolute;
      right: 0;
      height: 100%;

      & > div {
        display: flex;
        align-items: center;
        height: 100%;

        button {
          min-width: 80px;
          margin-right: 16px;
          margin-left: 0;
        }
      }

      .errorTip {
        margin-right: 40px;
        color: #ff5151;
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        cursor: auto;
      }
    }
  }

  .previewCode {
    .el-dialog__body {
      padding: 40px !important;

      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .footer {
      margin-top: 25px;
      color: #333;

      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }
</style>
