<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    :component-list="componentList"
    :disabled="disabled"
    :rules="rules"
    :loading="loading"
  />
</template>

<script>
  import { getEntryTime } from '@/api/user/staff';
  import { str2Date } from '@/util/date';

  export default {
    name: 'LeaveComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: {
          leave1: false
        },
        rules: {
          leave1: [
            { required: true, message: '请选择' },
            { validator: this.validateLeave1 }
          ],
          leave2: [
            { required: true, message: '请选择' },
            { validator: this.validateLeave2 }
          ]
        }
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        this.$set(this.loading, 'leave1', true);
        getEntryTime()
          .then((res) => {
            this.$set(this.hValue, 'leave1', res.data.data || '');
            this.$set(this.loading, 'leave1', false);
          })
          .catch(() => {
            this.$set(this.loading, 'leave1', false);
          });
      },
      validateLeave1(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('入职日期不得晚于预计离职日期'));
          return;
        }
        callback();
      },
      validateLeave2(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('预计离职日期不得早于入职日期'));
          return;
        }
        callback();
      },
      validateTime() {
        let { leave1, leave2 } = this.hValue;
        if (!leave1 || !leave2) {
          return true;
        }
        let startDate = str2Date(leave1);
        let endDate = str2Date(leave2);
        return startDate.getTime() <= endDate.getTime();
      },
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
