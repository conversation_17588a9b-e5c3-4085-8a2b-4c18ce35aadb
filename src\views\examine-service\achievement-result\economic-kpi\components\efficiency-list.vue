<template>
  <el-table
    style="width: 100%"
    border
    size="small"
    v-bind="bindProps()"
    :data="list"
    :cell-style="fontStyle"
    :header-cell-style="fontStyle"
  >
    <el-table-column fixed type="index" label="序号" width="50" align="center">
    </el-table-column>
    <el-table-column fixed label="部门名称" min-width="120px" align="center">
      <template slot-scope="scope">
        {{ scope.row.deptName || '---' }}
      </template>
    </el-table-column>
    <el-table-column label="主要领导（40%）" align="center">
      <el-table-column
        label="原始分"
        align="center"
        prop="mainLeaderOriginalScore"
        width="100"
      >
      </el-table-column>
      <el-table-column
        label="折算分"
        align="center"
        prop="mainLeaderConvertScore"
        width="100"
      >
      </el-table-column>
    </el-table-column>
    <el-table-column label="其他班子成员（30%）" align="center">
      <el-table-column
        label="原始分"
        align="center"
        prop="leaderOriginalScore"
        width="100"
      >
      </el-table-column>
      <el-table-column
        label="折算分"
        align="center"
        prop="leaderConvertScore"
        width="100"
      >
      </el-table-column>
    </el-table-column>
    <el-table-column label="部门负责人（30%）" align="center">
      <el-table-column
        label="原始分"
        align="center"
        prop="directLeaderOriginalScore"
        width="100"
      >
      </el-table-column>
      <el-table-column
        label="折算分"
        align="center"
        prop="directLeaderConvertScore"
        width="100"
      >
      </el-table-column>
    </el-table-column>
    <el-table-column label="系统计算分值" min-width="120" align="center">
      <template slot-scope="scope">
        {{ scope.row.systemCalculateScore | scoreFilter }}
      </template>
    </el-table-column>
    <el-table-column
      label="本周期考核得分"
      min-width="140px"
      align="center"
      label-class-name="label_required"
    >
      <template slot-scope="scope">
        <el-input-number
          v-model.number="scope.row.assessScore"
          size="small"
          controls-position="right"
          style="width: 100%"
          :precision="2"
          :step="0.01"
          :min="0"
          @change="() => handleChange()"
        ></el-input-number>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { mapState } from 'vuex';

  export default {
    name: 'efficiency-list',
    data() {
      return {};
    },
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener(
        'resize',
        () => {
          this.bindProps();
        },
        false
      );
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      handleChange() {
        this.$emit(
          'update:list',
          this.list.map((item) => {
            return item;
          })
        );
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }
</style>
