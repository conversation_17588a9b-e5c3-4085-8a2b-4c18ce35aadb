// 过渡动画 横向渐变
.fade-transverse-leave-active,
.fade-transverse-enter-active {
    transition: all .5s;
}

.fade-transverse-enter {
    transform: translateX(-30px);
    opacity: 0;
}

.fade-transverse-leave-to {
    transform: translateX(30px);
    opacity: 0;
}

// 过渡动画 缩放渐变
.fade-scale-leave-active,
.fade-scale-enter-active {
    transition: all .5s;
}

.fade-scale-enter {
    transform: scale(1.2);
    opacity: 0;
}

.fade-scale-leave-to {
    transform: scale(0.8);
    opacity: 0;
}

@keyframes animate-cloud {
    from {
      background-position: 600px 100%;
    }

    to {
      background-position: 0 100%;
    }
  }
  @keyframes animate-cloud {
    from {
      background-position: 600px 100%;
    }

    to {
      background-position: 0 100%;
    }
  }
  @keyframes animate-cloud {
    from {
      background-position: 600px 100%;
    }

    to {
      background-position: 0 100%;
    }
  }
  @keyframes animate-cloud {
    from {
      background-position: 600px 100%;
    }

    to {
      background-position: 0 100%;
    }
  }
  