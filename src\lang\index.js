import Vue from 'vue';
import Avue from '@smallwei/avue';
import VueI18n from 'vue-i18n';
import elementEnLocale from 'element-ui/lib/locale/lang/en'; // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'; // element-ui lang
import enLocale from './en';
import zhLocale from './zh';
import { getStore } from '@/util/store';
Vue.use(VueI18n);
let AvueVar;
if (
  process.env.VUE_APP_ENV === 'production' ||
  process.env.VUE_APP_ENV === 'test'
) {
  AvueVar = window.AVUE;
} else {
  AvueVar = Avue;
}
const messages = {
  en: {
    ...enLocale,
    ...elementEnLocale,
    ...AvueVar.locale.en
  },
  zh: {
    ...zhLocale,
    ...elementZhLocale,
    ...AvueVar.locale.zh
  }
};

const i18n = new VueI18n({
  locale: getStore({ name: 'language' }) || 'zh',
  messages
});

export default i18n;
