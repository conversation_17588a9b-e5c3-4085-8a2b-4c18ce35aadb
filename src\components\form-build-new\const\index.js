// 单行类型
export const ROW_TYPES = ['textarea', 'tips', 'image', 'file', 'form', 'desc'];

// 套件组类型
export const GROUP_TYPES = [
  'rest',
  'replacecard',
  'work',
  'out',
  'trip',
  'turnFormal',
  'leave',
  'htToXd',
  'htToHt',
  'htToGp',
  'htToJmgp1',
  'htToJmgp2',
  'htToJmbm1',
  'htToJmbm2'
];

// 不需校验类型
export const NO_RULE_TYPES = [
  'daterange',
  'form',
  'text',
  'desc',
  ...GROUP_TYPES
];

// 输入校验类型
export const INPUT_RULE_TYPES = [
  'input',
  'textarea',
  'inputNumber',
  'inputMoney',
  'idcard',
  'phone'
];
