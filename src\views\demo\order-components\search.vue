<template>
  <div>
    <el-form ref="form" :model="queryParams" :inline="true">
      <el-form-item label="订单号" prop="num">
        <el-input
          v-model="queryParams.num"
          placeholder="请输入订单号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  export default {
    name: 'OrderSearch',
    data() {
      return {
        queryParams: {
          num: ''
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        this.$emit('search', this.queryParams);
      }
    }
  };
</script>
<style lang=""></style>
