<template>
  <basic-container autoHeight>
    <h-block v-loading="groupLoading" title="审批管理后台" class="applyManager">
      <el-button @click="createApply" type="primary" size="small"
        >创建审批</el-button
      >
      <el-button @click="createGroup('', '')" size="small">新建分组</el-button>
      <draggable
        v-if="applyList.length"
        v-model="applyList"
        @update="datadragEnd"
        animation="300"
        handle=".cursor-sort"
      >
        <div v-for="li of applyList" :key="li.groupId" class="cellList">
          <header>
            <span
              >{{ li.groupName }} ({{
                li.definitionList ? li.definitionList.length : 0
              }})</span
            >
            <div>
              <el-button
                @click="createGroup(li.groupId, li.groupName)"
                type="text"
                >重命名</el-button
              >
              <el-button @click="deleteGroup(li.groupId)" type="text"
                >删除</el-button
              >
              <i
                @click="folding(li.groupId)"
                class="el-icon-arrow-down"
                :class="{ dropArrow: foldIds.includes(li.groupId) }"
              ></i>
              <el-tooltip effect="dark" content="拖动排序" placement="top">
                <span class="iconfont icon-paixu cursor-sort" />
              </el-tooltip>
            </div>
          </header>
          <transition name="slide-fade">
            <draggable
              v-model="li.definitionList"
              @add="add(li.groupId)"
              @end="end"
              :move="move"
              group="site"
              animation="300"
              handle=".cursor-sort"
            >
              <transition-group name="list">
                <template v-if="!foldIds.includes(li.groupId)">
                  <div
                    v-for="c of li.definitionList"
                    :key="c.id"
                    class="contentList"
                  >
                    <img v-oss :src="c.icon || '/oa/icon_diy_1.png'" />
                    <div class="oaInfo">
                      <p>{{ c.processName }}</p>
                      <p>{{ c.remark }}</p>
                    </div>
                    <div class="visible">{{ c.startScopeName }}</div>
                    <el-switch
                      @change="changeStatus(c.id, c.status)"
                      :value="c.status === 1"
                    >
                    </el-switch>
                    <div class="footer">
                      <el-button @click="edit(c.id, 'edit')" type="text"
                        >编辑</el-button
                      >
                      <el-button @click="edit(c.id, 'copy')" type="text"
                        >复制</el-button
                      >
                      <el-button
                        v-if="c.status === 0"
                        @click="deleteProcess(c.id)"
                        type="text"
                        >删除</el-button
                      >
                      <el-tooltip
                        effect="dark"
                        content="拖动排序"
                        placement="top"
                      >
                        <span class="iconfont icon-paixu cursor-sort" />
                      </el-tooltip>
                    </div>
                  </div>
                </template>
                <div key="empty" class="empty">
                  <p
                    v-if="
                      !li.definitionList.length && !foldIds.includes(li.groupId)
                    "
                  >
                    暂无数据
                  </p>
                </div>
              </transition-group>
            </draggable>
          </transition>
        </div>
      </draggable>
      <div v-if="!applyList.length" class="empty-box">
        <img v-oss src="/common/noData.png" class="empty-img" />
      </div>
      <create-group
        @updateList="getList"
        :create-group-visible.sync="createGroupVisible"
        :group="group"
      ></create-group>
      <create-apply
        :create-apply-visible.sync="createApplyVisible"
      ></create-apply>
    </h-block>
  </basic-container>
</template>
<script>
  import draggable from 'vuedraggable';
  import {
    processList,
    processListYd,
    stopProcess,
    startProcess,
    deleteGroup,
    moveGroup,
    moveFlow,
    deleteProcess
  } from '@/api/flow/group';
  import createGroup from './apply-manager/create-group';
  import createApply from './apply-manager/create-apply';
  import { deepClone } from '@/util/util.js';

  export default {
    components: { draggable, createGroup, createApply },
    data() {
      return {
        applyList: [],
        foldIds: [],
        group: {},
        fromGroup: {},
        currentId: '',
        cloneGroupList: [],
        statusValue: true,
        groupLoading: false,
        createGroupVisible: false,
        createApplyVisible: false
      };
    },
    mounted() {
      this.getList();
    },
    methods: {
      getList() {
        this.groupLoading = true;
        const url =
          this.$route.query.appCode === 'ydzk' ? processListYd : processList;
        url()
          .then((res) => {
            if (res && res.data && res.data.success) {
              let arr = res.data.data;
              arr.forEach((item) => {
                item.definitionList = item.definitionList || [];
              });
              this.cloneGroupList = deepClone(arr);
              this.applyList = arr;
            }
          })
          .finally(() => {
            this.groupLoading = false;
          });
      },
      changeStatus(id, status) {
        // 停用时而且isChangeSwitch为false需要弹出停用确认提示
        if (status === 1) {
          this.close(id);
        } else {
          this.start(id);
        }
      },
      edit(id, type) {
        this.$router.push({ name: 'oaSetUp', query: { id, type } });
      },
      createApply() {
        this.$router.push({ name: 'oaSetUp' });
        // this.createApplyVisible = true;
      },
      createGroup(groupId = '', groupName = '') {
        this.group = { groupId, groupName };
        this.createGroupVisible = true;
      },
      deleteGroup(id) {
        this.$confirm('是否确认删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.groupLoading = true;
            deleteGroup(id)
              .then((res) => {
                this.groupLoading = false;
                if (res && res.data && res.data.success) {
                  this.getList();
                  this.$message.success('删除成功');
                }
              })
              .catch(() => {
                this.groupLoading = false;
              });
          })
          .catch(() => {});
      },
      deleteProcess(id) {
        document.querySelectorAll('.manager-more').forEach((e) => {
          this.$nextTick(() => {
            e.style.display = 'none';
          });
        });
        this.$confirm('是否确认删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.groupLoading = true;
            deleteProcess(id).then((res) => {
              if (res && res.data && res.data.success) {
                this.$message.success('删除成功');
                this.getList();
              }
            });
          })
          .catch(() => {});
      },
      start(id) {
        this.groupLoading = true;
        startProcess(id)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.$message.success('启用成功');
              this.getList();
            }
          })
          .finally(() => {
            this.groupLoading = false;
          });
      },
      close(id) {
        let str = '正在进行中的审批将失效，是否确认停用？';
        this.$confirm(str, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.groupLoading = true;
            stopProcess(id)
              .then((res) => {
                if (res && res.data && res.data.success) {
                  this.$message.success('停用成功');
                  this.getList();
                }
              })
              .finally(() => {
                this.groupLoading = false;
              });
          })
          .catch(() => {});
      },
      folding(id) {
        if (this.foldIds.includes(id)) {
          this.foldIds = this.foldIds.filter((f) => f !== id);
        } else {
          this.foldIds.push(id);
        }
      },
      datadragEnd() {
        const ids = this.applyList.map((l) => l.dataId).join(',');
        moveGroup(ids);
      },
      move(e) {
        this.currentId = e.draggedContext.element.id;
      },
      end() {
        // 获取排序前分组id以及分组list
        this.cloneGroupList.forEach((c) => {
          c.definitionList.forEach((d) => {
            if (d.id === this.currentId) {
              this.fromGroup.preGroupId = c.groupId;
              this.fromGroup.preDefinitionList = c.definitionList.map(
                (l) => l.id
              );
            }
          });
        });

        // 获取排序后分组id以及分组list
        this.applyList.forEach((c) => {
          c.definitionList.forEach((d) => {
            if (d.id === this.currentId) {
              this.fromGroup.afterGroupId = c.groupId;
              this.fromGroup.afterDefinitionList = c.definitionList.map(
                (l) => l.id
              );
            }
          });
        });

        this.fromGroup.processId = this.currentId;
        moveFlow(this.fromGroup);
        this.cloneGroupList = deepClone(this.applyList);
      },
      // 跨数组拖拽
      add(id) {
        this.$nextTick(() => {
          if (this.foldIds.includes(id)) {
            this.foldIds = this.foldIds.filter((f) => f !== id);
          }
        });
      }
    }
  };
</script>
<style lang="scss">
  .applyManager {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-height: calc(100% - 12px - 24px) !important;

    .manager-more {
      width: 120px;
      min-width: 120px;
      padding: 0;

      .table-status {
        display: flex;
        flex-direction: column;
        font-size: 14px;

        .el-button {
          margin: 0;
          color: #333;

          &:hover {
            color: #ff5151;
          }
        }

        div {
          height: 34px;
          padding-left: 16px;
          color: #333;
          line-height: 34px;
          cursor: pointer;

          &:last-child:hover {
            color: #ff5151;
          }

          &:hover {
            color: #409eff;
            background-color: #fafafa;
          }
        }
      }
    }

    .list-item {
      display: inline-block;
      margin-right: 10px;
    }

    .list-enter-active,
    .list-leave-active {
      transition: all 0.5s;
    }

    .list-enter,
    .list-leave-to {
      transform: translateY(-50px);
      opacity: 0;
    }

    .h-block-content {
      flex: 1;

      & > .el-button {
        margin-bottom: 18px;
      }

      .empty-box {
        display: flex;
        align-items: center;
        justify-content: center;

        .empty-img {
          width: 140px;
          margin-top: 112px;
        }
      }
    }

    .empty {
      // height: 32px;
      padding: 16px 0;

      p {
        margin: 16px 0;
        color: #999;
        text-align: center;
      }
    }

    header {
      display: flex;
      justify-content: space-between;
      padding: 18px;
      color: #252525;
      font-size: 18px;
      line-height: 18px;
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;

      .el-button {
        padding: 0;

        span {
          font-size: 14px;
        }
      }

      i {
        margin-left: 16px;
      }
    }

    .slide-fade-enter-active,
    .slide-fade-leave-active {
      transition: all 0.25s ease;
    }

    .slide-fade-enter,
    .slide-fade-leave-to {
      opacity: 0;
    }

    .status-more {
      width: 16px !important;
      height: 16px !important;
      margin-left: 16px;
      cursor: pointer;
    }

    .el-icon-arrow-down {
      color: #999;
      cursor: pointer;
    }

    .el-button--text {
      padding: 0;
    }

    .dropArrow {
      transform: rotate(180deg);
    }

    .cursor-sort {
      margin-left: 16px;
      color: #e3e3e3;
      cursor: move;
    }

    .contentList {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;

      .footer {
        display: flex;
        flex: 1;
        justify-content: flex-end;
      }

      .oaInfo {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 31%;

        p {
          overflow: hidden;
          color: #999;
          line-height: 22px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        p:first-child {
          color: #333;
        }
      }

      .visible {
        width: 30%;
        margin-right: 51px;
        text-align: center;
      }

      p {
        margin: 0;
        margin-left: 24px;
      }

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
    }
  }
</style>
