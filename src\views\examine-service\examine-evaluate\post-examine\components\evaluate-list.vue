<template>
  <div>
    <person
      v-if="
        form.status === '1' ||
        (form.status === '2' && form.examineStatus === '2')
      "
      v-model="userList"
      btn-title="新增"
      model-title="选择被评价人"
      :dept-id="deptId"
      @change="handleChangeUser"
    />
    <el-table
      style="width: 100%; margin-top: 10px"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column type="index" label="序号" width="50" align="center">
      </el-table-column>
      <el-table-column label="被评价人" width="250" align="center">
        <template slot-scope="scope">
          {{ scope.row.userName || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        label="被评价人员岗位"
        prop="post"
        width="250"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.post || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="工号" align="center">
        <template slot-scope="scope">
          {{ scope.row.employeeNumber || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="基本分值" prop="basicScore" align="center">
        <template slot-scope="scope">
          {{ scope.row.basicScore || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        label="岗位业绩考核得分"
        align="center"
        width="180"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.score"
            size="small"
            style="width: 100%"
            controls-position="right"
            :min="0"
            :precision="2"
            :step="0.01"
            @change="(val) => syncData()"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="80"
        v-if="
          form.status === '1' ||
          form.status === '2' ||
          form.examineStatus === '2'
        "
      >
        <template slot-scope="scope">
          <el-button
            type="danger"
            icon="el-icon-delete"
            circle
            size="mini"
            @click="() => handleDelete(scope.row, scope.$index)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { Person } from '@/components/yk-organization-select';
  import { deleteItem, addItem } from '@/api/examine/post-examine';
  import { cloneDeep } from 'lodash';
  import { mapState } from 'vuex';
  export default {
    name: 'evaluate-list',
    components: {
      Person
    },
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      form: {
        type: Object,
        default() {
          return {};
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      },
      deptId: {
        type: String,
        default: ''
      }
    },
    watch: {
      list: {
        handler(newVal) {
          const temp = cloneDeep(newVal);
          this.tempList = temp.filter((item) => {
            return item.isMinister === 0;
          });
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        tempList: {},
        userList: []
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      },
      title() {
        return `共 ${this.tempList.length} 个被评价机构 ，已评价 ${this.count} 个`;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      // 同步数据
      syncData() {
        this.$emit('sync', this.tempList);
      },
      // 去重校验
      removal() {
        const isCharge = this.userList.some((item) => {
          return this.list.some((list) => {
            if (list.userId === item.id && list.isMinister === 1) {
              return true;
            }
          });
        });
        if (isCharge) {
          this.$message({
            message: '部门负责人无需被评价！',
            type: 'warning',
            duration: 5000
          });
          return true;
        }
        const isHas = this.userList.some((item) => {
          return this.tempList.some((list) => {
            if (list.userId === item.id) {
              return true;
            }
          });
        });
        if (isHas) {
          this.$message({
            message: '有重复人员被选择，请核对需要新增人员！',
            type: 'warning',
            duration: 5000
          });
          return true;
        }
        return false;
      },
      // 删除人员列表
      handleDelete(row, index) {
        this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await deleteItem({
                resultId: row.id
              });
              this.tempList.splice(index, 1);
              this.syncData();
            } catch (e) {
              console.error(e);
            }
          })
          .catch(() => {});
      },
      // user change
      handleChangeUser() {
        if (this.userList.length) {
          const repeat = this.removal();
          if (!repeat) {
            this.handleAddUser(this.userList);
          }
        }
      },
      // 增加人员
      async handleAddUser(userList = []) {
        const newUserList = userList.map((item) => {
          return {
            userName: item.realName,
            userId: item.id,
            deptId: item.deptId,
            deptName: item.deptName,
            isMinister: 0
          };
        });
        const basic = Object.assign({}, this.form, {
          resultList: this.tempList,
          newUserList: newUserList
        });
        try {
          const res = await addItem(basic);
          const resultList = res.data.data.resultList;
          this.tempList = resultList;
          this.userList = [];
          this.syncData();
          console.log('res', res);
        } catch (e) {
          console.error(e);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }
</style>
