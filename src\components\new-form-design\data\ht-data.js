export default [
  {
    name: '交易管理',
    children: [
      // 下单审批
      {
        icon: 'icon-order',
        name: '下单审批',
        type: 'htToXd',
        children: [
          {
            type: 'input',
            id: 'publishType',
            valueJson: {
              name: '交易类型',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'sellerName',
            valueJson: {
              name: '发布企业',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'publishName',
            valueJson: {
              name: '挂牌标题',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'itemName',
            valueJson: {
              name: '商品名称',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'price',
            valueJson: {
              name: '商品单价',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'buyNum',
            valueJson: {
              name: '采购数量',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'inputNumber',
            id: 'amount',
            valueJson: {
              name: '交易总额',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'depositAmount',
            valueJson: {
              name: '交易保证金',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'serviceAmount',
            valueJson: {
              name: '服务费',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'input',
            id: 'deliveryStr',
            valueJson: {
              name: '交割地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'originAddress',
            valueJson: {
              name: '产地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'deliveryMethodsStr',
            valueJson: {
              name: '交货方式',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          }
        ]
      },
      // 一口价挂牌
      {
        icon: 'icon-guapai2',
        name: '一口价挂牌',
        type: 'htToGp',
        children: [
          {
            type: 'input',
            id: 'publishType',
            valueJson: {
              name: '交易类型',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'publishName',
            valueJson: {
              name: '挂牌标题',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'date',
            id: 'publishEndTime',
            valueJson: {
              name: '交易截止时间',
              placeholder: '请选择',
              dateType: 3,
              required: true
            }
          },
          {
            type: 'input',
            id: 'itemName',
            valueJson: {
              name: '商品名称',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'categoryName',
            valueJson: {
              name: '所属类目',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'originAddress',
            valueJson: {
              name: '产地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'unit',
            valueJson: {
              name: '计量单位',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'price',
            valueJson: {
              name: '起拍单价',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'publishNum',
            valueJson: {
              name: '供应数量',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'deliveryMethodsStr',
            valueJson: {
              name: '交货方式',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'deliveryStr',
            valueJson: {
              name: '交割地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'laboratoryReportPics',
            valueJson: {
              name: '化验单',
              required: true
            }
          },
          {
            type: 'file',
            id: 'inspectionReportPics',
            valueJson: {
              name: '检测报告',
              required: true
            }
          },
          {
            type: 'file',
            id: 'publishAnnexes',
            valueJson: {
              name: '其他附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 200,
              required: false
            }
          }
        ]
      },
      // 竞买挂牌
      {
        icon: 'icon-buy1',
        name: '竞买挂牌',
        type: 'htToJmgp1',
        children: [
          {
            type: 'input',
            id: 'publishType',
            valueJson: {
              name: '交易类型',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'publishName',
            valueJson: {
              name: '挂牌标题',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'date',
            id: 'signUpTime',
            valueJson: {
              name: '报名时间',
              placeholder: '请选择',
              dateType: 3,
              required: true
            }
          },
          {
            type: 'date',
            id: 'competeStartTime',
            valueJson: {
              name: '竞价时间',
              placeholder: '请选择',
              dateType: 3,
              required: true
            }
          },
          {
            type: 'input',
            id: 'itemName',
            valueJson: {
              name: '商品名称',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'categoryName',
            valueJson: {
              name: '商品品种',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'originAddress',
            valueJson: {
              name: '产地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'unit',
            valueJson: {
              name: '计量单位',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'price',
            valueJson: {
              name: '起拍单价',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'publishNum',
            valueJson: {
              name: '供应数量',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'inputNumber',
            id: 'addPriceLimit',
            valueJson: {
              name: '加价梯度',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'deliveryMethodsStr',
            valueJson: {
              name: '交货方式',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'deliveryStr',
            valueJson: {
              name: '交割地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'laboratoryReportPics',
            valueJson: {
              name: '化验单',
              required: true
            }
          },
          {
            type: 'file',
            id: 'inspectionReportPics',
            valueJson: {
              name: '检测报告',
              required: true
            }
          },
          {
            type: 'file',
            id: 'publishAnnexes',
            valueJson: {
              name: '其他附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 200,
              required: false
            }
          }
        ]
      },
      // 竞卖挂牌
      {
        icon: 'icon-sell1',
        name: '竞卖挂牌',
        type: 'htToJmgp2',
        children: [
          {
            type: 'input',
            id: 'publishType',
            valueJson: {
              name: '交易类型',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'publishName',
            valueJson: {
              name: '挂牌标题',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'date',
            id: 'signUpTime',
            valueJson: {
              name: '报名时间',
              placeholder: '请选择',
              dateType: 3,
              required: true
            }
          },
          {
            type: 'date',
            id: 'competeStartTime',
            valueJson: {
              name: '竞价时间',
              placeholder: '请选择',
              dateType: 3,
              required: true
            }
          },
          {
            type: 'input',
            id: 'itemName',
            valueJson: {
              name: '商品名称',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'categoryName',
            valueJson: {
              name: '商品品种',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'originAddress',
            valueJson: {
              name: '产地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'unit',
            valueJson: {
              name: '计量单位',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'price',
            valueJson: {
              name: '起拍单价',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'publishNum',
            valueJson: {
              name: '供应数量',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'inputNumber',
            id: 'addPriceLimit',
            valueJson: {
              name: '减价梯度',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'deliveryMethodsStr',
            valueJson: {
              name: '交货方式',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'deliveryStr',
            valueJson: {
              name: '交割地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'laboratoryReportPics',
            valueJson: {
              name: '化验单',
              required: true
            }
          },
          {
            type: 'file',
            id: 'inspectionReportPics',
            valueJson: {
              name: '检测报告',
              required: true
            }
          },
          {
            type: 'file',
            id: 'publishAnnexes',
            valueJson: {
              name: '其他附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 200,
              required: false
            }
          }
        ]
      },
      // 竞买报名
      {
        icon: 'icon-buy2',
        name: '竞买报名',
        type: 'htToJmbm1',
        children: [
          {
            type: 'input',
            id: 'publishType',
            valueJson: {
              name: '交易类型',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'sellerName',
            valueJson: {
              name: '发布企业',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'publishTitle',
            valueJson: {
              name: '挂牌标题',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'itemName',
            valueJson: {
              name: '商品名称',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'price',
            valueJson: {
              name: '起拍单价',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'publishNum',
            valueJson: {
              name: '供应数量',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'deliveryStr',
            valueJson: {
              name: '交割地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'originAddress',
            valueJson: {
              name: '产地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'deliveryMethodsStr',
            valueJson: {
              name: '交货方式',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          }
        ]
      },
      // 竞卖报名
      {
        icon: 'icon-sell2',
        name: '竞卖报名',
        type: 'htToJmbm2',
        children: [
          {
            type: 'input',
            id: 'publishType',
            valueJson: {
              name: '交易类型',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'sellerName',
            valueJson: {
              name: '发布企业',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'publishTitle',
            valueJson: {
              name: '挂牌标题',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'itemName',
            valueJson: {
              name: '商品名称',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'price',
            valueJson: {
              name: '起拍单价',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'publishNum',
            valueJson: {
              name: '供应数量',
              placeholder: '请输入',
              unit: '',
              required: true,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'deliveryStr',
            valueJson: {
              name: '交割地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'originAddress',
            valueJson: {
              name: '产地',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'deliveryMethodsStr',
            valueJson: {
              name: '交货方式',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          }
        ]
      },
      // 合同审批
      {
        icon: 'icon-hetong',
        name: '合同审批',
        type: 'htToHt',
        children: [
          {
            type: 'input',
            id: 'contractName',
            valueJson: {
              name: '合同名称',
              placeholder: '请输入',
              maxLength: 50,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractCode',
            valueJson: {
              name: '合同编号',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'launchCompanyName',
            valueJson: {
              name: '发起方单位',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'signCompanyName',
            valueJson: {
              name: '对方单位',
              placeholder: '请输入',
              maxLength: 30,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'sealType',
            valueJson: {
              name: '用章类型',
              placeholder: '请输入',
              maxLength: 50,
              required: true,
              scanCode: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 150,
              required: false
            }
          },
          {
            type: 'file',
            id: 'file',
            valueJson: {
              name: '附件',
              required: true
            }
          }
        ]
      }
    ]
  }
];
