<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    v-loading="loading"
    :component-list="componentList"
    :disabled="disabled"
  />
</template>

<script>
  import { getMakeUpInfo } from '@/api/desk/flow';
  import { deepClone } from '@/util/util';

  export default {
    name: 'ReplacecardComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      'hValue.replacecard1'(val) {
        this.$set(this.hValue, 'replacecard3', val);
      },
      'hValue.replacecard3'(val) {
        this.$set(this.hValue, 'replacecard1', val);
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        this.loading = true;
        getMakeUpInfo()
          .then((res) => {
            this.loading = false;
            let { dateList = [], makeupMsg = '' } = res.data.data || {};
            if (dateList.length) {
              let radio = {
                id: 'replacecard3',
                type: 'radio',
                valueJson: {
                  name: '缺卡时间',
                  placeholder: '请选择',
                  options: Array.from(dateList, (item) => {
                    return { value: item };
                  }),
                  required: false
                }
              };
              let componentList = deepClone(this.componentList);
              componentList.splice(1, 0, radio);
              this.$emit('update:component-list', componentList);
              this.$set(this.hValue, 'replacecard1', dateList[0]);
            }
            this.$set(this.hValue, 'replacecard2', makeupMsg);
          })
          .catch(() => {
            this.loading = false;
          });
      },
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
