<template>
  <el-table :data="tableData" border style="width: 100%" size="small">
    <el-table-column type="index" label="序号" width="50" align="center" />
    <el-table-column prop="code" label="评价单编号" align="center">
    </el-table-column>
    <!-- 2023/04/10  要求隐藏接收人列 -->
    <!-- <el-table-column prop="assessorNames" label="接收人" align="center">
    </el-table-column> -->
    <el-table-column prop="periodItemName" label="考核周期" align="center">
    </el-table-column>
    <el-table-column prop="deadline" label="截止日期" align="center">
    </el-table-column>
    <el-table-column prop="status" label="单据状态" align="center">
      <template slot-scope="scope">
        {{ scope.row.status | changeStatus }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="160" align="center">
      <template slot-scope="scope">
        <el-button
          type="text"
          size="small"
          icon="el-icon-view"
          @click="$emit('dispatch', 'detail', scope.row)"
          >查看</el-button
        >
        <el-button
          v-if="scope.row.status == '1'"
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'evaluate', scope.row)"
          >评价</el-button
        >
        <el-button
          v-if="scope.row.status == '2'"
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'edit', scope.row)"
          >评价</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-list',
    props: {
      tableData: {
        type: Array,
        default() {
          return [];
        }
      }
    }
  };
</script>
