<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="list"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
      :span-method="spanMethod"
    >
      <el-table-column
        prop="serialNumber"
        label="序号"
        width="50"
        align="center"
      />
      <el-table-column label="被评价机构" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.deptName || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="指标名称" align="center" width="150">
        <template slot-scope="scope">
          {{ scope.row.indexName || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="基本分值" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.basicScore || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        label="考核目标及计分标准（点击查看）"
        align="center"
        width="260"
      >
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput1(scope.row)"
          >
            {{ scope.row.standard || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" width="60">
        <template slot-scope="scope">
          {{ scope.row.unit || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="年度指标" align="center">
        <template slot-scope="scope">
          <div>
            <strong style="font-size: 13px">考核目标</strong>
            <div class="text-value">
              {{
                (scope.row.target && scope.row.target.yearBasicTarget) || '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">奋斗目标</strong>
            <div class="text-value">
              {{
                (scope.row.target && scope.row.target.yearFightTarget) || '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">力争目标</strong>
            <div class="text-value">
              {{
                (scope.row.target && scope.row.target.yearBestTarget) || '---'
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="截止本考核周期指标" align="center">
        <template slot-scope="scope">
          <div>
            <strong style="font-size: 13px">考核目标</strong>
            <div class="text-value">
              {{
                (scope.row.target && scope.row.target.currentBasicTarget) ||
                '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">奋斗目标</strong>
            <div class="text-value">
              {{
                (scope.row.target && scope.row.target.currentFightTarget) ||
                '---'
              }}
            </div>
          </div>
          <div>
            <strong style="font-size: 13px">力争目标</strong>
            <div class="text-value">
              {{
                (scope.row.target && scope.row.target.currentBestTarget) ||
                '---'
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="完成情况（点击查看）" align="center" width="140">
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput(scope.row)"
          >
            {{ scope.row.completionSituation || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="累计完成" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.accumulatedCompletion || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="考核结果（点击查看）" align="center" width="140">
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput2(scope.row)"
          >
            {{ scope.row.assessmentResult || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="累计考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.accumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="截止上周期累计考核加减分"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.lastMonthAccumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本周期考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本周期考核得分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentScore | scoreFilter }}
        </template>
      </el-table-column>
    </el-table>
    <!--  同步文本输入  -->
    <el-dialog
      width="600px"
      :title="headerTitle"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        maxlength="1000"
        show-word-limit
        disabled
        :rows="12"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  export default {
    name: 'evaluate-list-detail',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        inputVisited: false,
        spanArr: [],
        position: 0,
        tempInput: '',
        headerTitle: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    watch: {
      list: {
        handler() {
          this.rowspan();
        },
        deep: true
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
      this.rowspan();
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      rowspan() {
        this.list.forEach((item, index) => {
          if (index === 0) {
            this.spanArr.push(1);
            this.position = 0;
          } else {
            if (
              this.list[index].serialNumber ===
              this.list[index - 1].serialNumber
            ) {
              this.spanArr[this.position] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.position = index;
            }
          }
        });
      },
      spanMethod({ rowIndex, columnIndex }) {
        if (columnIndex === 0 || columnIndex === 1) {
          const _row = this.spanArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col
          };
        }
      },
      handleInput(row) {
        this.inputVisited = true;
        this.tempInput = row.completionSituation;
        this.headerTitle = '完成情况';
      },
      handleInput1(row) {
        this.inputVisited = true;
        this.tempInput = row.standard;
        this.headerTitle = '考核目标及计分标准';
      },
      handleInput2(row) {
        this.inputVisited = true;
        this.tempInput = row.assessmentResult;
        this.headerTitle = '考核结果';
      },
      // 完成情况弹窗关闭
      inputClose() {
        this.inputVisited = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .text-value {
    line-height: 15px;
  }

  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
  }
</style>
