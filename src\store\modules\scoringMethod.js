const scoringMethod = {
  state: {
    source: []
  },
  mutations: {
    SOURCE_INCREASE(state, obj) {
      state.source.push(obj);
    },
    SOURCE_DECREASE(state) {
      state.source.pop();
    },
    SOURCE_CLEAR(state) {
      state.source = [];
    },
    SOURCE_CLEAR_VALUE(state) {
      state.source = state.source.map(() => {
        return {
          itemName: '',
          itemScore: '',
          isDefault: 0,
          ratioOperator: undefined,
          ratio: 0
        };
      });
    },
    SET_SOURCE(state, list) {
      state.source = list;
    }
  }
};

export default scoringMethod;
