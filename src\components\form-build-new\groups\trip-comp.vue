<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    :component-list="componentList"
    :disabled="disabled"
    :rules="rules"
    :loading="loading"
  />
</template>

<script>
  import { deepClone } from '@/util/util';
  import { getTripDays } from '@/api/desk/flow';

  export default {
    name: 'TripComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        days: undefined,
        loading: {
          trip8: false
        },
        rules: {
          trip8: [{ validator: this.validateTrip8 }]
        },
        tempTrip2: undefined
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      'hValue.trip2': {
        handler(val) {
          if (!this.tempTrip2) {
            this.tempTrip2 = deepClone(val);
            return;
          }
          let oldVal = deepClone(this.tempTrip2);
          this.tempTrip2 = deepClone(val);
          if (val.length !== oldVal.length) {
            this.setTrip8();
            return;
          }
          for (let i = 0; i < val.length; i++) {
            let newTrip7 = val[i].trip7 || { startTime: '', endTime: '' };
            let oldTrip7 = oldVal[i].trip7 || { startTime: '', endTime: '' };
            if (
              newTrip7.startTime !== oldTrip7.startTime ||
              newTrip7.endTime !== oldTrip7.endTime
            ) {
              this.setTrip8();
              return;
            }
          }
        },
        deep: true
      }
    },
    created() {
      if (this.value.trip2 && this.value.trip2.length) {
        this.setTrip8();
      }
    },
    methods: {
      setTrip8() {
        this.$set(this.hValue, 'trip8', '');
        this.$set(this.loading, 'trip8', true);
        this.days = -1;
        getTripDays(this.hValue)
          .then((res) => {
            this.$set(this.loading, 'trip8', false);
            let { dayNum } = res.data.data || {};
            if (dayNum === -1) {
              dayNum = undefined;
            }
            this.days = dayNum;
            if (dayNum || dayNum === 0) {
              this.$set(this.hValue, 'trip8', dayNum + '');
            }
          })
          .catch(() => {
            this.$set(this.loading, 'trip8', false);
            this.days = -2;
          });
      },
      validateTrip8(rule, value, callback) {
        if (this.days === -1) {
          callback(new Error('正在计算时长，请等待'));
        } else if (this.days === -2) {
          callback(new Error('时长计算错误，请稍后重试'));
        } else if (this.days <= 0) {
          callback(new Error('时长必须大于0'));
        } else {
          callback();
        }
      },
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
