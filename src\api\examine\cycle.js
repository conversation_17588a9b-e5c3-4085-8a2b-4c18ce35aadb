import request from '@/router/axios';

// 考核周期--分页查询
export function getList(params) {
  return request({
    url: '/api/examine-period',
    method: 'get',
    params
  });
}

// 考核周期--新增
export function postAdd(data) {
  return request({
    url: `/api/examine-period`,
    method: 'post',
    data
  });
}

// 考核周期--编辑
export function postEdit(data) {
  return request({
    url: `/api/examine-period/update`,
    method: 'post',
    data
  });
}

// 考核周期--获取考核周期
export function postExamineDetail(data) {
  return request({
    url: `/api/examine-period/get-period`,
    method: 'post',
    data
  });
}

// 考核周期--获取结束时间
export function postEndTime(data) {
  return request({
    url: `/api/examine-period/get-end-time`,
    method: 'post',
    data
  });
}

// 考核周期--考核周期详情
export function getDetail(id) {
  return request({
    url: `/api/examine-period/${id}`,
    method: 'get'
  });
}

// 考核周期--删除
export function postDelete(data) {
  return request({
    url: `/api/examine-period/delete`,
    method: 'post',
    data
  });
}
