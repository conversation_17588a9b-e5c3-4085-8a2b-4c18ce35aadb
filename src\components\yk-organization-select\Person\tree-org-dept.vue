<template>
  <div class="tree_box">
    <div class="tree-wrapper" v-loading="loading">
      <el-tree
        ref="tree"
        node-key="id"
        lazy
        empty-text="暂无数据"
        :props="props"
        :load="loadNode"
        :default-expanded-keys="expandedArr"
        :render-content="renderContent"
        @node-click="nodeClick"
      />
    </div>
  </div>
</template>

<script>
  import { getDeptLazyTreeByParent } from '@/api/system/dept';
  import website from '@/config/website';

  export default {
    name: 'TreeOrg<PERSON>erson',
    data() {
      return {
        props: {
          label: 'deptName',
          children: 'children',
          isLeaf: (data) => !data.hasChildren
        },
        loading: false
      };
    },
    props: {
      expandedArr: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    methods: {
      // 请求数据
      async request(params) {
        try {
          this.loading = true;
          const res = await getDeptLazyTreeByParent(params, website.tenantId);
          return res.data.data;
        } catch (e) {
          console.error(e);
        } finally {
          if (typeof params !== undefined) {
            this.loading = false;
          }
        }
      },
      // 加载数据
      async loadNode(node, resolve) {
        let list;
        if (node.level === 0) {
          list = [website.deptRoot];
        }
        if (node.level >= 1) {
          list = await this.request(node.data.id);
        }
        return resolve(list);
      },
      // 节点选取
      nodeClick(obj, node) {
        const nodeInfo = node.data;
        this.$emit('getNodeInfo', nodeInfo);
      },
      // 渲染内容
      renderContent(h, { data }) {
        // deptCategory (1:公司,2:部门,3:小组)
        const { deptCategory, deptName } = data;
        if (deptCategory === 2) {
          return (
            <span>
              <i class="el-icon-folder" style="margin-right: 5px" />
              {deptName}【部门】
            </span>
          );
        } else if (deptCategory === 1) {
          return (
            <span>
              <i class="el-icon-office-building" style="margin-right: 5px"></i>
              {deptName}
            </span>
          );
        } else {
          return <span>{deptName}</span>;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tree_box {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding-top: 20px;
  }

  .tree-wrapper {
    height: 100%;
    overflow: auto;
  }
</style>
<style>
  .tree-wrapper .el-tree-node > .el-tree-node__children {
    overflow: initial;
    background-color: transparent;
  }

  .tree-wrapper .el-tree-node:focus > .el-tree-node__content {
    background-color: #d0cfcf;
  }
</style>
