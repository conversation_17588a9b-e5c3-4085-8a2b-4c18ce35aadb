<template>
  <div class="bc-form-build-area">
    <el-input
      @click.native="onClick"
      :value="hValue.address || ''"
      placeholder="请选择"
      readonly
      :title="hValue.address || ''"
    />
    <el-dialog
      @open="onOpen"
      title="选择地点"
      :visible.sync="visible"
      width="672px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="bc-form-build-area-dialog"
    >
      <div class="map-container">
        <div id="addMap" @click="searchResults = []" class="map-area"></div>
        <el-input
          v-if="!located"
          v-model="location.address"
          size="small"
          class="location-search-input"
        >
          <el-button slot="append" @click="searchLocation" size="small">
            搜索
          </el-button>
        </el-input>
        <div v-if="searchResults.length > 1" class="location-search-result">
          <div
            v-for="(item, index) in searchResults"
            :key="index"
            @click="selectLocation(item.location)"
            class="location-search-result-item"
          >
            {{ item.name }}
            <span class="location-search-result-district">
              {{ item.district }}
            </span>
          </div>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="visible = false" size="medium">返回</el-button>
        <el-button @click="onChange" type="primary" size="medium">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { loadAMap } from '@/util/amap.js';

  export default {
    name: 'AreaComp',
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false,
        AMap: null,
        map: null,
        geocoder: null,
        geolocation: null,
        autoComplete: null,
        pointMarker: null,
        searchResults: [],
        located: false,
        location: {}
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    beforeCreate() {
      loadAMap()
        .then((AMap) => {
          this.AMap = AMap;
          this.map = new this.AMap.Map('addMap');
          if (this.value.address) {
            return;
          }
          this.map.plugin('AMap.Geolocation', () => {
            this.geolocation = new this.AMap.Geolocation();
            this.geolocation.getCurrentPosition();
          });
          this.AMap.event.addListener(
            this.geolocation,
            'complete',
            ({ formattedAddress, position }) => {
              this.located = true;
              this.hValue = {
                address: formattedAddress,
                lat: position.lat,
                lng: position.lng
              };
            }
          );
          this.AMap.event.addListener(this.geolocation, 'error', (err) => {
            this.located = false;
            console.log('errrrrrrr', err);
            // this.$message({ type: 'error', message: '无法获取定位' });
          });
        })
        .catch((err) => {
          console.log('errrrrrrr', err);
          // this.$message({ type: 'error', message: '地图加载失败' });
        });
    },
    methods: {
      onClick() {
        if (!this.disabled) {
          this.visible = true;
        }
      },
      onOpen() {
        this.location = { ...this.value };
        this.pointMarker = null;
        this.$nextTick(() => {
          this.map = new this.AMap.Map('addMap', {
            viewMode: '2D',
            mapStyle: 'amap://styles/normal',
            zoom: 15
          });
          this.geocoder = new this.AMap.Geocoder({ city: '010' });
          this.map.plugin('AMap.Autocomplete', () => {
            this.autoComplete = new this.AMap.Autocomplete();
          });
          let { lat, lng } = this.value;
          if (lat && lng) {
            this.drawLocation(new this.AMap.LngLat(lng, lat));
          }
        });
      },
      searchLocation() {
        if (!this.autoComplete || !this.location.address) {
          return;
        }
        this.autoComplete.search(this.location.address, (status, result) => {
          if (status === 'complete') {
            let list = result.tips || [];
            this.searchResults = list;
            list.length === 1 && this.selectLocation(list[0]);
          }
        });
      },
      selectLocation(location) {
        if (location) {
          this.drawLocation(location);
          this.getLocationByLngLat(location).then((res) => {
            this.location = {
              address: res,
              lat: location.lat,
              lng: location.lng
            };
          });
        } else {
          this.$message({ type: 'error', message: '没有搜索到具体地点' });
        }
        this.searchResults = [];
      },
      drawLocation(position) {
        if (this.pointMarker) {
          this.pointMarker.setPosition(position);
        } else {
          this.pointMarker = new this.AMap.Marker({
            map: this.map,
            position
          });
          this.map.add(this.pointMarker);
        }
        this.map.setCenter(position);
      },
      getLocationByLngLat(lngLat) {
        return new Promise((resolve) => {
          this.geocoder.getAddress(lngLat, (status, { regeocode }) => {
            resolve(
              status === 'complete' ? regeocode.formattedAddress : '未知'
            );
          });
        });
      },
      onChange() {
        let { lat = '', lng = '' } = this.location;
        if (!lat || !lng) {
          this.$message.error('请先选择地址');
          return;
        }
        this.hValue = { ...this.location };
        this.visible = false;
      }
    }
  };
</script>

<style lang="scss">
  @import '@/styles/element-ui';

  .bc-form-build-area {
    .el-input__inner {
      cursor: pointer;
    }
  }

  .bc-form-build-area-dialog {
    .map-container {
      position: relative;
      height: 328px;

      .map-area {
        height: 100%;
        border-radius: 4px;
      }

      .location-search-input {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 436px;
        transform: translate(-50%, 0);

        .el-input-group__append {
          background-color: $--color-blue;

          .el-button {
            color: #fff;
          }
        }
      }

      .location-search-result {
        position: absolute;
        top: 57px;
        left: 50%;
        width: 416px;
        padding: 10px;
        background: #fff;
        transform: translate(-50%, 0);

        .location-search-result-item {
          line-height: 24px;
          cursor: pointer;

          &:hover {
            // color: $--color-primary;
            .location-search-result-district {
              // color: $--color-primary;
            }
          }

          .location-search-result-district {
            float: right;

            // color: $--color-text-secondary;
          }
        }
      }
    }

    .location-remark-tips {
      font-size: 12px;

      // color: $--color-primary;
    }
  }
</style>
