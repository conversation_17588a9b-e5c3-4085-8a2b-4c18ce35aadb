<template>
  <div class="info_form_mb5">
    <el-form label-width="110px" label-suffix="：" size="small">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="评价单单号">
            {{ form.code || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="考核周期">
            {{ form.periodItemName || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="截止日期">
            {{ form.deadline || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="接收人">
            {{ form.assessorNames || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联评价方案">
            {{ form.schemeCode || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联绩效单">
            {{ form.correlatedResultCode || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据状态">
            {{ form.status | changeStatus }}
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="isDetail">
          <el-form-item label="评价日期">
            {{ form.evaluationDate | formatDate }}
          </el-form-item>
        </el-col>
        <el-col :span="8" v-else>
          <el-form-item label="评价日期">
            <el-date-picker
              v-model="value1"
              type="date"
              placeholder="选择评价日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              @change="handleChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <div class="text-style">
              {{ form.comment || '---' }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'basic-info',
    props: {
      form: {
        type: Object,
        default() {
          return {};
        }
      },
      isDetail: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      form: {
        handler() {
          if (this.form.evaluationDate) {
            this.value1 = this.form.evaluationDate;
          } else {
            this.handleChange();
          }
        },
        deep: true
      }
    },
    data() {
      return {
        value1: ''
      };
    },
    methods: {
      handleChange() {
        this.$emit('syncDate', this.value1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .text-style {
    padding-top: 5px;
    color: #5a5a5a;
    line-height: 20px;
  }
</style>
