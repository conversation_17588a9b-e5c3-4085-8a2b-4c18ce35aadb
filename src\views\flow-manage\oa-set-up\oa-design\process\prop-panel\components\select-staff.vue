<template>
  <div class="choose-staff-tree">
    <div class="conbox">
      <div class="all-select">
        <el-checkbox
          v-model="checkAll"
          @change="handleCheckAllChange"
          :indeterminate="indeterminate"
          >全选</el-checkbox
        >
      </div>
      <div class="titbox">
        <el-input
          v-model="filterText"
          size="mini"
          placeholder="搜索"
          suffix-icon="el-icon-search"
        ></el-input>
      </div>
      <div class="wordbox">
        <!-- 历史代码 node-key="uuid" -->
        <el-tree
          ref="tree"
          @check="getData"
          show-checkbox
          class="filter-tree"
          node-key="id"
          default-expand-all
          :data="treeData"
          :props="defaultProps"
          :check-on-click-node="true"
          :filter-node-method="filterNode"
        >
          <span slot-scope="{ data }" class="custom-tree-node">
            <!-- <span>{{ node.hasChildren }}</span> -->
            <img v-if="data.dataType === 1" v-oss src="/apply/oa-people.png" />
            <div v-if="data.dataType === 3">
              <div class="avatar">
                <img v-if="data.avatar" :src="data.avatar" />
                <img v-else v-oss src="/launch/default-photo.png" />
              </div>
            </div>
            <span class="tree-label">{{ data.label }}</span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="conbox">
      <div class="all-select">
        <div>已选 {{ checkedIds.length }} 个成员</div>
      </div>
      <div class="wordbox delete-box">
        <div class="dept-selected">
          <div v-for="(item, index) in checkedIds" :key="index">
            <div class="inli">
              <div class="avatar">
                <img v-if="item.avatar" :src="item.avatar" />
                <img v-else v-oss src="/launch/default-photo.png" />
              </div>
              <span class="tree-label">{{ item.label }}</span>
              <img
                v-oss
                @click="removeData(item)"
                class="icon-close-circle"
                src="/oa/icon-close-circle.png"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  const getAllNode = (list) => {
    let temp = [];
    const getNode = (array) => {
      array.forEach((item) => {
        temp.push(item);
        if (item.children && item.children.length) {
          getNode(item.children);
        }
      });
    };
    getNode(list);
    return temp;
  };
  const addUUID = (array) => {
    array.forEach((item) => {
      item.uuid =
        item.label +
        (item.parentId ? item.parentId : 0) +
        (item.userId ? item.userId : 0) +
        '';
      if (item.children && item.children.length) {
        addUUID(item.children);
      }
    });
  };
  import { deepClone } from '@/util/util.js';
  export default {
    props: {
      // 当前选中人的id
      checkedList: {
        type: Array,
        default: () => []
      },
      // 当前接口类型， people人员，dept部门 position岗位 job职务
      radio: {
        type: String,
        default: 'people'
      },
      // 当前数据集合
      treeData: {
        type: Array,
        default: () => []
      },
      // 父子是否强关联
      checkStrictly: {
        type: Boolean,
        default: false
      },
      // 选择的最大数量，0为不限制数量
      maxSelectLength: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        checkAll: false,
        indeterminate: false,
        checkedIds: [],
        filterText: '',
        listBg: {},
        defaultProps: {
          children: 'children',
          label: 'label',
          isLeaf: 'hasChildren'
        }
      };
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      },
      treeData() {
        this.filterText = '';
        this.checkStatus();
        this.setTree();
      }
    },
    mounted() {
      this.setTree();
    },
    methods: {
      setTree() {
        if (!this.treeData.length) {
          return;
        }
        addUUID(this.treeData);
        if (this.checkedList.length) {
          this.checkedIds = deepClone(this.checkedList);
        }
        let a = this.checkedList.map((item) => item.uuid);
        if (a.length) {
          this.$refs.tree.setCheckedKeys(a);
        }
      },
      checkStatus() {
        let treeLength = [];
        treeLength = getAllNode(this.treeData).filter(
          (item) => item.dataType === 3
        );
        let a = this.checkedIds.filter((item) => item.userId); // 获得当前已选对象
        let b = []; // 当前类型中要选中的userid
        if (a.length) {
          // 寻找其他类型中相同userid的成员
          a.forEach((m) => {
            treeLength.forEach((n) => {
              if (m.userId === n.userId) {
                b.push(n);
              }
            });
          });
          if (b.length && this.checkedIds.length) {
            this.$nextTick(() => {
              this.$refs.tree.setCheckedKeys(b.map((i) => i.uuid));
            });
          }
        } else {
          b = [];
        }
        if (treeLength.length === b.length) {
          this.checkAll = true;
          this.indeterminate = false;
          getAllNode(this.treeData).map((item) => {
            this.$nextTick(() => {
              this.$refs.tree.setChecked(item, true);
            });
          });
        } else if (b.length === 0) {
          this.checkAll = false;
          this.indeterminate = false;
          getAllNode(this.treeData).map((item) => {
            this.$nextTick(() => {
              this.$refs.tree.setChecked(item, false);
            });
          });
        } else {
          this.indeterminate = true;
        }
      },
      // 全选
      handleCheckAllChange() {
        let allchecked = getAllNode(this.treeData);
        allchecked.map((item) => {
          let own = item;
          item = Object.assign(own, { type: this.radio });
        });
        let temp = deepClone(this.checkedIds);
        if (this.checkAll || this.indeterminate) {
          // 全选状态
          allchecked.map((item) => {
            this.$nextTick(() => {
              this.$refs.tree.setChecked(item, true);
            });
          });
          allchecked = allchecked.filter((item) => item.dataType === 3);
          let obj = {};
          let list = [...temp, ...allchecked].reduce(function (item, next) {
            obj[next.userId]
              ? ''
              : (obj[next.userId] = true && item.push(next));
            return item;
          }, []);
          this.checkedIds = deepClone(list);
        } else {
          allchecked.map((item) => {
            this.$nextTick(() => {
              this.$refs.tree.setChecked(item, false);
            });
          });
          let list = allchecked.filter((item) => item.dataType === 3);
          this.checkedIds = this.checkedIds.filter(
            (item) => !list.some((ele) => ele.userId === item.userId)
          );
        }
        this.$emit('update:checkedList', this.checkedIds);
      },
      filterNode(value, data) {
        if (!value) {
          return true;
        }
        return data['label'].indexOf(value) !== -1;
      },
      // 点击选择部门
      getData(data) {
        let tList = getAllNode(this.treeData); // 获取当前类型的所有对象拼成的数组
        let allOne = tList.filter((item) => item.userId === data.userId); // 找到所有的同一用户
        let temp = deepClone(this.checkedIds);
        let arr = temp.filter((item) => item.userId === data.userId); // 判断右边是否包含当前点击
        if (arr.length) {
          // 点击取消
          if (allOne.length) {
            allOne.map((item) => {
              this.$refs.tree.setChecked(item, false);
            });
          }
          this.checkedIds = this.checkedIds.filter(
            // 取消当前选中
            ({ userId }) => userId !== data.userId
          );
        } else {
          // 点击选中
          let obj = {};
          let array = [];
          if (data.dataType === 3) {
            array.push(data);
            if (allOne.length) {
              allOne.map((item) => {
                this.$refs.tree.setChecked(item, true);
              });
            }
          } else {
            if (data.hasChildren && data.children.length) {
              array = [...array, ...data.children];
              let multip = tList.filter((item) =>
                array.map((o) => o.userId).includes(item.userId)
              );
              if (multip.length) {
                multip.map((item) => {
                  this.$refs.tree.setChecked(item, true);
                });
              }
            }
          }
          let list = [...temp, ...array].reduce(function (item, next) {
            // 给数组对象去重
            obj[next.userId]
              ? ''
              : (obj[next.userId] = true && item.push(next));
            return item;
          }, []);
          this.checkedIds = deepClone(list);
        }
        this.$emit('update:checkedList', this.checkedIds);
      },
      // 删除已选部门
      removeData(data) {
        let tList = getAllNode(this.treeData); // 获取当前类型的所有对象拼成的数组
        let allOne = tList.filter((item) => item.userId === data.userId);
        if (allOne.length) {
          allOne.map((item) => {
            this.$refs.tree.setChecked(item, false);
          });
        }
        this.checkedIds = this.checkedIds.filter(
          ({ userId }) => userId !== data.userId
        );
        this.$emit('update:checkedList', this.checkedIds);
      }
    }
  };
</script>

<style lang="scss">
  .choose-staff-tree {
    display: flex;
    justify-content: space-between;
    padding: 12px 24px 0;

    .is-leaf {
      background: #fff !important;
    }

    .el-tree-node__content {
      margin: 6px 0;

      &:hover {
        .is-leaf {
          background: #f5f7fa !important;
        }
      }
    }

    .avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      color: #fff;
      font-size: 14px;
      border-radius: 50%;

      img {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        border-radius: 50%;
      }
    }

    .conbox {
      width: 240px;
      height: 446px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;

      .all-select {
        padding: 12px 16px;
        color: #666;
        font-weight: 400;
        font-size: 14px;
        line-height: 19px;
        border-bottom: 1px solid #d9d9d9;
      }

      .titbox {
        padding: 12px 16px;
      }

      .wordbox {
        // padding: 0 16px;
        height: 320px;
        overflow-y: scroll;

        .custom-tree-node {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
          width: 150px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }

          .icon-tree-depart {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            margin-left: 4px;
          }

          .tree-label {
            display: flex;
            display: flow-root;
            align-items: center;
            margin-left: 8px;
            overflow: hidden;
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 23px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .delete-box {
        box-sizing: border-box;
        height: 400px;
        padding-top: 10px;
      }

      .dept-selected {
        // padding: 16px;
        .tree-label {
          display: inline-block;

          // margin-left: 8px;
          width: 140px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .inli {
          position: relative;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          padding: 5px 16px;

          .avatar {
            width: 30px;
            height: 30px;
            margin-right: 8px;
          }

          span {
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 30px !important;
          }

          .icon-close-circle {
            position: absolute;
            right: 16px;
            width: 16px;
            height: 16px;
            cursor: pointer;
          }

          &:hover {
            background: white;
          }
        }
      }
    }
  }
</style>
