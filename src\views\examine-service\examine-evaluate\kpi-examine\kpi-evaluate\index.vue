<template>
  <basic-container>
    <transition-group name="fade">
      <div v-show="!open" :key="1">
        <search @search="searchQuery" />
        <table-list
          v-loading="loading"
          :tableData="tableData"
          @dispatch="handleEvent"
        />
        <yk-pagination
          small
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.current"
          :limit.sync="queryParams.size"
          @pagination="getList"
        />
      </div>
      <yk-local-model :key="2" :title="title" :open="open" @close="handleClose">
        <evaluate
          :row-code="rowCode"
          :is-detail="isDetail"
          @close="handleClose"
          @refresh="getList"
        />
      </yk-local-model>
    </transition-group>
  </basic-container>
</template>

<script>
  import { Search, TableList } from './components';
  import Evaluate from './evaluate.vue';
  import { getKpiEvaluateList } from '@/api/examine/examine-evaluate';

  export default {
    name: 'KpiEvaluate',
    components: {
      Search,
      TableList,
      Evaluate
    },
    data() {
      return {
        open: false,
        // 表格参数
        queryParams: {
          current: 1,
          size: 10
        },
        total: 10,
        loading: false,
        tableData: [],
        // model参数
        rowCode: '',
        title: '',
        isDetail: false
      };
    },
    methods: {
      // 查询
      searchQuery(param) {
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          param
        );
        this.getList();
      },
      // 请求列表数据
      async getList() {
        try {
          this.loading = true;
          const res = await getKpiEvaluateList(this.queryParams);
          const data = res.data.data;
          this.total = data.total;
          this.tableData = data.records;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 监听表格事件
      handleEvent(type, data) {
        this.open = true;
        this.rowCode = data.code;
        switch (type) {
          case 'detail':
            this.title = '查看';
            this.isDetail = true;
            break;
          case 'evaluate':
            this.title = '评价';
            this.isDetail = false;
            break;
          case 'edit':
            this.title = '编辑';
            this.isDetail = false;
            break;

          default:
            break;
        }
      },
      // model关闭
      handleClose(val) {
        this.open = val;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .page-height {
    box-sizing: border-box;
    height: calc(100vh - 125px);
    overflow-y: auto;
  }
</style>
