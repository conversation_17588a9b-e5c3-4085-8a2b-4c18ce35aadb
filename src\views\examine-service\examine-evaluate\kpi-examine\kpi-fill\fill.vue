<template>
  <div class="wrapper" v-loading="loading">
    <!--  基础信息  -->
    <el-collapse style="border: 0" v-model="collapse">
      <el-collapse-item name="one">
        <h2 slot="title" class="title">基础信息</h2>
        <basic-info
          v-model="detailForm.fillingDate"
          :form="detailForm"
          :isDetail="isDetail"
        />
      </el-collapse-item>
    </el-collapse>
    <section>
      <div class="title_wrapper">
        <h2 class="title">指标完成情况</h2>
        <el-tooltip effect="dark" content="全屏展示" placement="top-start">
          <i class="el-icon-full-screen icon-full" @click="fullScreen"></i>
        </el-tooltip>
      </div>
      <!--   详情   -->
      <fill-list-detail v-if="isDetail" :list="listData" />
      <!--   评价   -->
      <fill-list
        key="1"
        v-else
        :list="listData"
        @sync="(val) => (listData = val)"
      />
      <!-- 审批流程 -->
      <div class="title_wrapper">
        <!--   普通展示审批环节   -->
        <div v-if="!isDetail && !isApproval">
          <h2 class="title">审批流程</h2>
          <div style="padding: 30px 70px 10px 40px">
            <init-flow ref="flow" v-if="processId" :id="processId" />
          </div>
        </div>
        <!-- 审批流查看 -->
        <div
          style="padding: 20px 0"
          v-else-if="
            detailForm.processInstanceId &&
            detailForm.status &&
            detailForm.status !== '0'
          "
        >
          <h2 class="title">审批流程</h2>
          <approval-flow
            v-if="approvalId"
            ref="approvalFlow"
            :id="approvalId"
            @repeal="handleRepeal"
            @submit="handleSubmit"
            @close="approvalClose"
          />
        </div>
      </div>

      <div v-if="!isDetail && !isApproval" class="end-btn">
        <el-button
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save(1)"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save(2)"
          >提交</el-button
        >
      </div>
    </section>
    <!--   全屏操作   -->
    <full-screen-table v-model="visited" title="指标完成情况">
      <fill-list-detail v-if="isDetail" full :list="listData" :max-height="0" />
      <fill-list
        v-else
        full
        key="2"
        :list="listData"
        :max-height="0"
        @sync="(val) => (listData = val)"
      />
      <div v-if="!isDetail && !isApproval" class="end-btn">
        <el-button
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save(1)"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save(2)"
          >提交</el-button
        >
      </div>
    </full-screen-table>
  </div>
</template>

<script>
  import { BasicInfo, FillList, FillListDetail } from './components';
  import { FullScreenTable } from '@/views/examine-service/components/full-screen';
  import {
    getKpiFillDetail,
    postKpiFillSave,
    postKpiFillSubmit,
    getKpiFillRepeal,
    postKpiFillApprove
  } from '@/api/examine/examine-evaluate';
  import { getProcessId } from '@/api/flow/process';
  import {
    InitFlow,
    ApprovalFlow
  } from '@/views/examine-service/components/approval';
  import { service_code, service_process } from '@/constant/service';
  import { mapGetters } from 'vuex';
  import { formatTime } from '@/util/util';

  export default {
    name: 'fill',
    components: {
      BasicInfo,
      FillList,
      FillListDetail,
      FullScreenTable,
      InitFlow,
      ApprovalFlow
    },
    props: {
      rowCode: {
        type: String,
        default: ''
      },
      isDetail: {
        type: Boolean,
        required: true
      },
      isApproval: {
        type: Boolean,
        required: true
      }
    },
    data() {
      return {
        collapse: 'one',
        visited: false,
        detailForm: {},
        listData: [],
        // 页面加载
        loading: false,
        // 业务流程id
        processId: '',
        approvalId: '',
        loadingSave: false,
        loadingSubmit: false,
        disabled: false
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      // 页面初始化
      async init() {
        await this.getDetail();
        // 填报时, 先获取processId
        if (!this.isDetail && !this.isApproval) {
          await this.getProcessId();
        }
      },
      // 获取填报单详情
      async getDetail() {
        try {
          this.loading = true;
          const res = await getKpiFillDetail(this.rowCode);
          const data = res.data.data;
          // 获取processInstanceId
          this.approvalId = data.processInstanceId;
          // 获取详情
          this.detailForm = data.pluginForm;
          this.listData = this.detailForm.resultList || [];
          // 如果详情里没有填报日期, 置为当前时间
          if (!this.detailForm.fillingDate) {
            this.detailForm.fillingDate = new Date()
              .toLocaleString()
              .replaceAll('/', '-');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 根据后台定义好的code和key获取processDefinationId
      async getProcessId() {
        try {
          const res = await getProcessId({
            code: service_code,
            key: service_process.kpi_evaluate
          });
          this.processId = res.data.data;
        } catch (e) {
          console.error(e);
        }
      },
      // 表格全屏
      fullScreen() {
        this.visited = true;
      },
      // 流程撤销
      async handleRepeal() {
        try {
          const res = await getKpiFillRepeal(this.approvalId);
          if (res && res.data && res.data.success) {
            this.$message.success('撤回成功');
            this.$emit('close');
            this.$emit('refresh');
          }
          // 重置审批流
          await this.$refs.approvalFlow.resetDetails();
        } catch (e) {
          console.error(e);
        }
      },
      // 审批
      async handleSubmit(approvalData) {
        try {
          const formData = {
            ...approvalData,
            pluginForm: this.detailForm
          };
          const res = await postKpiFillApprove(formData);
          const data = res.data;
          if (data.code === 200) {
            this.$message.success(data.msg);
            this.$emit('close');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 审批关闭
      approvalClose() {
        this.$emit('close');
      },
      // 提交校验
      validate() {
        const res = { flag: true, msg: '' };
        const validateObj = {
          accumulatedCompletion: '累计完成',
          completionSituation: '完成情况'
        };
        for (let index = 0; index < this.listData.length; index++) {
          const item = this.listData[index];
          for (let field in validateObj) {
            if (!item[field]) {
              res.flag = false;
              res.msg = `第${index + 1}行 ${
                validateObj[field]
              } 字段不能为空, 请核对`;
              return res;
            }
          }
        }
        return res;
      },
      // 保存/提交
      async save(type) {
        // 保存
        if (type === 1) {
          await this.postData(type);
        }
        // 提交
        if (type === 2) {
          if (!this.detailForm.fillingDate) {
            return this.$message.warning('提报日期未选择，请选择！');
          }
          // 判断审批人
          const initFlow = this.$refs.flow.$data.users;
          if (!initFlow.length) {
            return this.$message.warning(
              '审批流程没有审批人节点，请完善审批流程！'
            );
          }
          const isTrue = initFlow.some((item) => {
            if (item.users === null || item.users.length === 0) {
              return true;
            }
          });
          if (isTrue) {
            return this.$message.warning('请选择审批人！');
          }
          // 判断必填字段
          const { flag, msg } = this.validate();
          if (!flag) {
            return this.$message.warning(msg);
          }
          this.$confirm('是否确定发起审批？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.postData(type);
            })
            .catch(() => {});
        }
      },
      // 提交数据
      async postData(type) {
        try {
          this.disabled = true;
          if (type === 1) {
            this.loadingSave = true;
          } else {
            this.loadingSubmit = true;
          }
          // 遍历去除listData中每行数据的attachList
          this.listData.map((item) => delete item.attachList);
          const formData = {
            processDefinitionId: this.processId,
            usersNodeinfos: this.$refs.flow.$data.users,
            pluginForm: {
              ...this.detailForm,
              fillingDate: formatTime(this.detailForm.fillingDate),
              resultList: this.listData
            }
          };
          const apiObj = {
            1: postKpiFillSave,
            2: postKpiFillSubmit
          };
          const res = await apiObj[type](formData);
          const data = res.data;
          if (data.code === 200) {
            this.$message.success(data.msg);
            this.$emit('close');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.disabled = false;
          this.loadingSave = false;
          this.loadingSubmit = false;
        }
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;

    .icon-full {
      position: absolute;
      right: 0;
      bottom: 0;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
