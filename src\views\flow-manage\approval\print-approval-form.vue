<!--
 * @description:
 * @param:
 * @author: Fei
 * @return:
 * @Date: 2021-02-02 14:23:44
-->
<template>
  <el-dialog
    title="打印表单"
    :visible.sync="show"
    class="print-approval-form-dialog"
    width="1000px"
    height="500px"
    :before-close="handleClose"
  >
    <div class="pdf-context-box">
      <pdf
        v-for="(i, index) in numPages"
        :key="index"
        @page-loaded="currentPage = $event"
        @loaded="loadPdfHandler"
        :page="i"
        :src="url"
      ></pdf>
    </div>
    <div ref="printContent" class="pdf-context-box-print">
      <pdf
        v-for="(i, index) in numPages"
        ref="printPdf"
        :key="index"
        :src="url"
        :page="i"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返 回</el-button>
      <el-button @click="downFile" type="primary">下 载</el-button>
      <el-button @click="print" type="primary">打 印</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import pdf from 'vue-pdf';
  import html2canvas from 'html2canvas';
  import printJS from 'print-js';

  export default {
    components: { pdf },

    props: {
      url: {
        type: String
      },
      show: {
        type: Boolean
      }
    },
    data() {
      return {
        src: null,
        currentPage: 0,
        numPages: null
      };
    },
    created() {
      // console.log("pdf.numPages1111111", this.url);

      this.src = pdf.createLoadingTask(this.url);

      this.src.promise.then((pdf) => {
        // console.log("pdf.numPages", pdf.numPages);

        this.numPages = pdf.numPages;
      });
    },
    methods: {
      // 打印文件
      print() {
        // this.$refs.printPdf.print();
        html2canvas(this.$refs.printContent, {
          backgroundColor: null,
          useCORS: true,
          windowHeight: document.body.scrollHeight
        }).then((canvas) => {
          const url = canvas.toDataURL();
          printJS({
            printable: url,
            type: 'image',
            documentTitle: ''
          });
          // console.log(url)
        });
      },
      // 关闭弹窗
      handleClose() {
        this.$emit('close');
      },
      // 下载文件
      downFile() {
        window.open(this.url, '_blank');
      },
      // pdf加载时
      loadPdfHandler() {
        this.currentPage = 1; // 加载的时候先加载第一页
      }
    }
  };
</script>

<style lang="scss">
  .pdf-context-box {
    height: calc(100vh - 340px);
    overflow: auto;
  }
  .pdf-context-box-print {
    position: fixed;
    left: 1000px;
    top: 20000px;
    width: 1200px;
  }
  .print-approval-form-dialog {
    margin: auto;
    height: calc(100vh - 100px);
    .el-dialog {
      margin-top: 0 !important;
    }
  }
</style>
