<template>
  <el-table
    :data="tableData"
    v-loading="loading"
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column
      align="center"
      type="selection"
      width="55"
      :selectable="(row) => row.status === 1"
    />
    <el-table-column align="center" type="index" label="#" width="60" />
    <el-table-column align="center" prop="code" width="136" label="方案编号" />
    <el-table-column
      align="center"
      min-width="120"
      prop="name"
      label="方案名称"
      show-overflow-tooltip
    />
    <el-table-column align="center" label="是否生效" min-width="100">
      <template slot-scope="{ row }">
        {{ row.status | filterStatus }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      label="考核维度"
      prop="dimensionName"
      width="120"
    >
    </el-table-column>
    <el-table-column fixed="right" align="center" label="操作" width="300">
      <template slot-scope="{ row }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-view"
          @click="tableEmit('detail', row)"
          >查看</el-button
        >
        <el-button
          type="text"
          size="mini"
          icon="el-icon-edit"
          @click="tableEmit('edit', row)"
          >编辑</el-button
        >
        <el-popconfirm
          style="margin-left: 8px"
          title="确定 删除 此数据吗？"
          @confirm="() => $emit('del', [row.code])"
        >
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            class="button-del"
            slot="reference"
            >删除</el-button
          >
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-info',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      tableData: {
        type: Array,
        default: function () {
          return [];
        }
      }
    },
    filters: {
      filterStatus(type) {
        switch (type) {
          case 0:
            return '无效';
          case 1:
            return '生效';
          default:
            return '--';
        }
      }
    },
    methods: {
      // 多选框
      handleSelectionChange(val) {
        this.$emit('select', val);
      },
      // 表格事件
      tableEmit(type, { code }) {
        const titleObj = {
          edit: '编辑',
          detail: '查看'
        };
        const params = {
          code,
          showFlag: true,
          title: titleObj[type]
        };
        this.$emit('tableEmit', type, params);
      }
    }
  };
</script>

<style lang="scss">
  .button-del {
    color: #f56c6c;
    &:hover {
      color: #f78989;
    }
  }
  .text_ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
