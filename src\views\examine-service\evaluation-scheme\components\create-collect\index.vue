<template>
  <el-dialog
    width="720px"
    :visible="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    title="生成评价收集单"
    @close="handleCancel"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="mini"
      label-suffix=":"
      label-width="90px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="方案编号" prop="code">
            <div class="form_div">
              <el-input :value="originData.code" disabled></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方案名称" prop="name">
            <div class="form_div">
              <el-input :value="originData.name" disabled></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考核维度" prop="dimension">
            <div class="form_div">
              <el-input :value="originData.dimensionName" disabled></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考核周期" prop="periodItemName">
            <div class="form_div">
              <el-input
                class="disable_cursor"
                placeholder="请选择考核周期"
                :value="form.periodItemName"
                @click.native="handleSelectAssessPeriod"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止日期" prop="deadline">
            <div class="form_div">
              <el-date-picker
                v-model="form.deadline"
                value-format="yyyy-MM-dd hh:mm:ss"
                type="date"
                :disabled="!form.periodItemId"
                :placeholder="
                  form.periodItemId ? '请选择截止日期' : '请先选择考核周期'
                "
                :picker-options="pickerOptions"
                style="width: 100%"
              ></el-date-picker>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="comment">
            <el-input
              type="textarea"
              placeholder="请输入备注"
              maxlength="200"
              show-word-limit
              v-model="form.comment"
              :rows="3"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" :loading="btnLoading" @click="handleCancel"
        >返回</el-button
      >
      <el-button
        size="small"
        type="primary"
        :loading="btnLoading"
        @click="handleSubmit"
        class="confirm"
        >确定</el-button
      >
    </div>

    <period-select
      v-if="periodDialogShow"
      :selectPeriodId="form.periodItemId"
      @dialogSave="handleDialogSave"
      @dialogClose="periodDialogShow = false"
    />
  </el-dialog>
</template>

<script>
  import { PeriodSelect } from '@/views/examine-service/components/period-select';
  import { postCollectSave } from '@/api/examine/evaluation-scheme';
  import { collectRules } from '../rules';

  export default {
    name: 'collect-single',
    components: {
      PeriodSelect
    },
    props: {
      originData: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        rules: collectRules,
        form: {
          periodItemId: undefined,
          periodItemName: undefined,
          deadline: undefined,
          comment: undefined,
          isOverwrite: 0
        },
        // 截止日期必须在选中考核周期结束日期后
        pickerOptions: {
          disabledDate: null
        },
        periodDialogShow: false,
        // 提交按钮加载中
        btnLoading: false
      };
    },
    methods: {
      // 显示考核周期弹窗
      handleSelectAssessPeriod() {
        this.periodDialogShow = true;
      },
      // 接收选中的考核周期
      handleDialogSave(data) {
        this.periodDialogShow = false;
        this.form.periodItemId = data.id;
        this.form.periodItemName = data.itemName;
        if (data.deadline) {
          // 如果截止日期有默认值, 将默认值格式化为日期+时间
          this.form.deadline = data.deadline + ' 00:00:00';
        } else {
          this.form.deadline = undefined;
        }
        // 截止日期禁用
        this.pickerOptions.disabledDate = (time) => {
          return time.getTime() < new Date(data.endTime).getTime();
        };
      },
      // 返回
      handleCancel() {
        this.form = {
          periodItemId: undefined,
          periodItemName: undefined,
          deadline: undefined,
          comment: undefined,
          isOverwrite: 0
        };
        this.$emit('dialogClose');
      },
      // 提交
      handleSubmit() {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return;
          try {
            // 点击提交时将按钮状态改为加载中, 等后台返回结果后取消
            this.btnLoading = true;
            const {
              code: schemeCode,
              type: schemeType,
              dimension
            } = this.originData;
            const data = {
              schemeCode,
              schemeType,
              dimension,
              ...this.form
            };
            const res = await postCollectSave(data);
            const { code, msg } = res.data;
            // 根据返回code进行处理
            switch (code) {
              // 成功
              case 200:
                this.$message.success('生成评价收集单成功');
                this.handleCancel();
                this.$emit('refresh');
                break;
              // 存在可覆盖的评价单
              case 10002:
                this.$confirm(msg, '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
                  .then(() => {
                    this.form.isOverwrite = 1;
                    this.handleSubmit();
                  })
                  .catch(() => {
                    this.form.isOverwrite = 0;
                  });
                break;
              // 存在不可覆盖的评价单
              case 10003:
                this.$alert(msg, '提示', {
                  confirmButtonText: '确定'
                });
                break;

              default:
                break;
            }
          } catch (e) {
            console.error(e);
          } finally {
            this.btnLoading = false;
          }
        });
      }
    }
  };
</script>
<style lang="scss" scoped>
  .disable_cursor {
    ::v-deep .el-input__inner {
      color: #606266;
      background: #fff;
      cursor: pointer;
    }
  }

  .form_div {
    height: 34px;
  }
</style>
