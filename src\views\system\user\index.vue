<template>
  <el-row>
    <el-col :span="5">
      <basic-container :autoHeight="true">
        <!-- 左侧树 -->
        <avue-tree
          :option="treeOption"
          :data="treeData"
          @node-click="nodeClick"
        />
      </basic-container>
    </el-col>
    <el-col :span="19">
      <basic-container :autoHeight="true">
        <!-- 右侧搜索 -->
        <search v-show="showSearch" @search="handleSearch" />
        <el-row>
          <el-col :span="20">
            <!-- 列表操作按钮 -->
            <operate
              :ids="ids"
              :search="queryParams"
              :selectionList="selectionList"
              @dispatch="dispatch"
            />
          </el-col>
          <!-- 列表右侧操作 -->
          <yk-right-tool
            :show-search.sync="showSearch"
            :columns="columns"
            @queryTable="onLoad(page)"
          ></yk-right-tool>
        </el-row>
        <table-info
          v-loading="loading"
          :source="tableData"
          :columns="columns"
          @selection="(val) => (selectionList = val)"
          @dispatch="dispatch"
        />
        <yk-pagination
          small
          v-show="page.total > 0"
          :total="page.total"
          :page.sync="page.currentPage"
          :limit.sync="page.pageSize"
          @pagination="
            ({ page, limit }) => onLoad({ currentPage: page, pageSize: limit })
          "
        />
      </basic-container>
    </el-col>
    <!-- 新增/编辑/详情 -->
    <drawer
      :drawerType="drawerType"
      :drawerVisible="drawerVisible"
      :userId="userId"
      @close="handleDrawerClose"
      @refresh="onLoad(page)"
    />
    <!-- 角色配置 -->
    <role-set
      :ids="ids"
      :roleBoxVisible="roleBox"
      :roleTreeObj="roleTreeObj"
      :roleGrantList="roleGrantList"
      @dispatch="dispatch"
      @close="roleBox = false"
    />
    <!-- 平台配置 -->
    <platform :platformBoxVisible="platformBox" @close="platformBox = false" />
    <!-- 导入 -->
    <yk-import-excel
      title="用户数据导入"
      action="/szyk-user/import-user"
      templateUrl="/szyk-user/export-template"
      :importVisible="importVisible"
      @close="importVisible = false"
      @refresh="onLoad(page)"
    />
  </el-row>
</template>

<script>
  import { getList } from '@/api/system/user';
  import { getRoleTree } from '@/api/system/role';
  import { getDeptLazyTree } from '@/api/system/dept';
  import Search from './search';
  import Operate from './operate';
  import TableInfo from './table-info';
  import RoleSet from './components/role-set';
  import Platform from './components/platform';
  import Drawer from './drawer.vue';

  const columns = [
    { key: 0, label: `登录账号`, visible: true },
    { key: 1, label: `所属租户`, visible: true },
    { key: 2, label: `用户姓名`, visible: true },
    { key: 3, label: `所属角色`, visible: true },
    { key: 4, label: `所属部门`, visible: true },
    { key: 5, label: `用户平台`, visible: true }
  ];

  export default {
    name: 'userList',
    components: {
      Search,
      Operate,
      TableInfo,
      RoleSet,
      Platform,
      Drawer
    },
    data() {
      return {
        // 左侧机构树
        treeDeptId: '',
        treeData: [],
        treeOption: {
          nodeKey: 'id',
          lazy: true,
          treeLoad: function (node, resolve) {
            const parentId = node.level === 0 ? 0 : node.data.id;
            getDeptLazyTree(parentId).then((res) => {
              resolve(
                res.data.data.map((item) => {
                  return {
                    ...item,
                    leaf: !item.hasChildren
                  };
                })
              );
            });
          },
          addBtn: false,
          menu: false,
          size: 'small',
          props: {
            labelText: '标题',
            label: 'title',
            value: 'value',
            children: 'children'
          }
        },
        // 右侧搜索
        showSearch: true,
        queryParams: {},
        // 右侧用户列表
        columns,
        loading: false,
        tableData: [],
        selectionList: [],
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        // 角色配置
        roleBox: false,
        roleGrantList: [],
        roleTreeObj: [],
        // 平台配置
        platformBox: false,
        // 导入
        importVisible: false,
        // 抽屉
        userId: '',
        drawerType: '',
        drawerVisible: false
      };
    },
    methods: {
      nodeClick(data) {
        this.treeDeptId = data.id;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      handleSearch(params) {
        this.queryParams = params;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      selectionClear() {
        this.selectionList = [];
      },
      // 角色配置
      handleGrant() {
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.roleTreeObj = [];
        if (this.selectionList.length === 1) {
          this.roleTreeObj = this.selectionList[0].roleId.split(',');
        }
        getRoleTree().then((res) => {
          this.roleGrantList = res.data.data;
          this.roleBox = true;
        });
      },
      // 表格操作
      dispatch(type, data) {
        if (['add', 'edit', 'view'].includes(type)) {
          this.drawerType = type;
          this.drawerVisible = true;
          if (type !== 'add') {
            this.userId = data.id;
          }
        } else if (type === 'refresh') {
          this.onLoad(this.page);
        } else if (type === 'grant') {
          this.handleGrant();
        } else if (type === 'platform') {
          this.platformBox = true;
        } else if (type === 'import') {
          this.importVisible = true;
        }
      },
      // 请求列表数据
      async onLoad(page) {
        try {
          this.loading = true;
          const res = await getList(
            page.currentPage,
            page.pageSize,
            this.queryParams,
            this.treeDeptId
          );
          const data = res.data.data;
          this.page.total = data.total;
          this.tableData = data.records;
          this.selectionClear();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 抽屉关闭
      handleDrawerClose() {
        this.drawerVisible = false;
        this.drawerType = '';
        this.formData = {};
        this.userId = '';
      }
    },
    computed: {
      ids() {
        let ids = [];
        this.selectionList.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(',');
      }
    }
  };
</script>
