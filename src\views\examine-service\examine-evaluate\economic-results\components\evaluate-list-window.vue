<template>
  <el-dialog title="设置目标" append-to-body :visible="visited" @close="close">
    <el-form inline size="small">
      <el-form-item label="指标名称：" style="margin-right: 30px">{{
        this.temp.indexName || '---'
      }}</el-form-item>
      <el-form-item label="单位：">{{ this.temp.unit || '---' }}</el-form-item>
    </el-form>
    <el-table :data="tableData" size="mini" border>
      <el-table-column prop="b1" align="center" />
      <el-table-column label="年度目标" align="center">
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.b2"
            placeholder="请输入"
            size="mini"
            controls-position="right"
            style="width: 100%"
            :precision="2"
            :step="0.01"
          />
        </template>
      </el-table-column>
      <el-table-column label="截止本考核周期目标" align="center">
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.b3"
            placeholder="请输入"
            size="mini"
            controls-position="right"
            style="width: 100%"
            :precision="2"
            :step="0.01"
          />
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer">
      <el-button size="small" @click="close">返 回</el-button>
      <el-button size="small" type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    name: 'evaluate-list-window',
    props: {
      visited: {
        type: Boolean,
        default: false
      },
      temp: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        tableData: [
          { b1: '考核目标', b2: 0, b3: 0 },
          { b1: '奋斗目标', b2: 0, b3: 0 },
          { b1: '力争目标', b2: 0, b3: 0 }
        ]
      };
    },
    watch: {
      visited(bool) {
        if (bool && this.temp.target) {
          this.tableData = this.tableData.map((item) => {
            if (item.b1 === '考核目标') {
              item.b2 = this.temp.target.yearBasicTarget;
              item.b3 = this.temp.target.currentBasicTarget;
            }
            if (item.b1 === '奋斗目标') {
              item.b2 = this.temp.target.yearFightTarget;
              item.b3 = this.temp.target.currentFightTarget;
            }
            if (item.b1 === '力争目标') {
              item.b2 = this.temp.target.yearBestTarget;
              item.b3 = this.temp.target.currentBestTarget;
            }
            return item;
          });
        } else {
          this.tableData = [
            { b1: '考核目标', b2: 0, b3: 0 },
            { b1: '奋斗目标', b2: 0, b3: 0 },
            { b1: '力争目标', b2: 0, b3: 0 }
          ];
        }
      }
    },
    methods: {
      // 关闭
      close() {
        this.$emit('close');
      },
      // 完成
      save() {
        const data = Object.assign({}, this.temp.target, {
          yearBasicTarget: this.tableData[0].b2,
          currentBasicTarget: this.tableData[0].b3,
          yearFightTarget: this.tableData[1].b2,
          currentFightTarget: this.tableData[1].b3,
          yearBestTarget: this.tableData[2].b2,
          currentBestTarget: this.tableData[2].b3
        });
        this.$emit('save', data, this.temp.id);
      }
    }
  };
</script>
