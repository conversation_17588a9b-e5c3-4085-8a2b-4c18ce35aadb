<template>
  <h-panel :title="title" class="apply-log">
    <i
      slot="title-append"
      @click="downList"
      class="el-icon-arrow-down"
      :class="{ 'is-reverse': isReverse }"
    />
    <el-timeline v-show="!isReverse">
      <template v-for="(activity, index) in activities">
        <el-timeline-item
          v-if="activity.type !== 2 || users.length"
          :key="index"
          :icon="activity.icon"
          :color="activity.color"
          :class="{
            blue: pending.includes(index),
            green: success.includes(index),
            error: error.includes(index)
          }"
        >
          <div class="log-content">
            <div>
              <span class="log-start">
                {{ activity.processName }}
                <span>{{ approveText(activity) }}</span>
              </span>
              <span>
                <span
                  v-for="(user, i) of activity.users"
                  :key="i"
                  @click="getUserInfo(user)"
                  class="user-info"
                >
                  <img v-if="user.avatar" :src="user.avatar" />
                  <img v-else v-oss src="/launch/default-photo.png" />
                  {{ user.username }}
                </span>
              </span>
            </div>
            <div class="log-end">
              <span>{{ activity.timestamp }}</span>
            </div>
          </div>
          <div
            v-if="isCommon(activity)"
            class="footer"
            :class="{
              'is-deleted': activity.nodeType === 7 && !activity.commentFlag
            }"
          >
            <div v-if="isAddSign(activity)">
              {{ addSignText(activity) }}: {{ activity.addSignNodeUserInfo }}
            </div>
            <template v-if="activity.comment">
              {{ commentHeader(activity.nodeType) }}：{{ activity.comment }}
            </template>
          </div>
          <h-upload
            :value="activity.opinionFiles"
            multiple
            show-file-list
            :disabled="
              disabled || (activity.nodeType === 7 && !activity.commentFlag)
            "
            :no-tip="false"
            :is-deleted="activity.nodeType === 7 && !activity.commentFlag"
          >
          </h-upload>
          <el-button
            v-if="
              activity.nodeType === 7 && activity.commentFlag && activity.isMine
            "
            @click="deleteComment(activity.commentId)"
            type="text"
            size="small"
            class="delete-btn"
          >
            删除
          </el-button>
        </el-timeline-item>
      </template>
    </el-timeline>
    <user-info-dialog
      @close="userInfoVisible = false"
      @goToChatPage="goToChatPage"
      :visible="userInfoVisible"
      :user-info-id="userInfoId"
      :org-id="orgId"
    ></user-info-dialog>
  </h-panel>
</template>
<script>
  import { mapGetters } from 'vuex';
  import { commonDetail, deleteComment } from '@/api/flow/process';
  import userInfoDialog from './user-info-dialog.vue';
  const FLAG_STATUS = ['time', 'yes', 'no', 'recall', '', 'yes', '', ''];
  const STATUS_NAME = [
    '审批中',
    '通过',
    '拒绝',
    '撤销',
    '已停用',
    '自动通过',
    '删除',
    '待审批'
  ];
  const TYPE_NAME = {
    1: '发起审批',
    3: '抄送',
    5: '加签',
    6: '转交'
  };
  export default {
    components: { userInfoDialog },
    props: {
      processInstanceId: {
        type: String,
        default: ''
      },
      title: {
        type: String,
        default: '审批日志'
      },
      // 控制审批收起功能
      isPickUp: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        activities: [],
        disabled: true,
        isReverse: false,
        pending: [],
        success: [],
        error: [],
        userInfoVisible: false,
        userInfoId: '',
        orgId: ''
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      isCommon() {
        return (activity) => activity.comment || activity.addSignNodeUserInfo;
      },
      isAddSign() {
        return (activity) =>
          [5, 6].includes(activity.nodeType) && activity.addSignNodeUserInfo;
      },
      approveText() {
        return (activity) =>
          activity.users.length === 1
            ? ''
            : activity.approveType === 1
            ? '需所有人审批通过'
            : activity.approveType === 2
            ? '1人通过即可'
            : '';
      },
      addSignText() {
        return (activity) => (activity.nodeType === 5 ? '添加审批人' : '转交');
      }
    },
    watch: {
      processInstanceId: {
        handler() {
          this.getHistory();
        },
        immediate: true
      }
    },
    created() {
      this.isReverse = this.isPickUp;
    },
    methods: {
      commentHeader(activity) {
        switch (activity) {
          case 1:
            return '销假原因';
          case 5:
            return '原因';
          case 6:
            return '原因';
          case 7:
            return '评论内容';
          default:
            return '审批意见';
        }
      },
      getUserInfo({ userId, orgId }) {
        this.userInfoId = userId;
        this.orgId = orgId;
        this.userInfoVisible = true;
      },
      goToChatPage(userInfo) {
        this.$store.dispatch('chat/setChatUserInfo', userInfo);
        this.$router.push({ name: 'im' });
      },
      getHistory() {
        if (!this.processInstanceId) {
          return;
        }
        commonDetail(this.processInstanceId).then((res) => {
          const nodes = res.data.data;
          this.activities = nodes.map((h, i) => {
            // 判断当前为抄送人节点且其他节点审批完成  2021.7.28增加评论节点
            const copyToPeople =
              (i !== 0 &&
                [1, 5].includes(nodes[i - 1].status) &&
                h.nodeType === 3) ||
              [5, 6, 7].includes(h.nodeType);

            const icon = `iconfont icon-process_${
              h.nodeType === 1
                ? 'create'
                : copyToPeople
                ? 'yes'
                : FLAG_STATUS[h.status]
            }`;

            h.status === 0 && i !== 0 && this.pending.push(i - 1);
            [1, 5].includes(h.status) && i !== 0 && this.success.push(i - 1);
            [2].includes(h.status) && i !== 0 && this.error.push(i - 1);
            copyToPeople && this.success.push(i - 1);

            let isMine =
              h.users.length && h.users[0].userId === this.userInfo.user_id;
            let processName =
              h.nodeType === 7
                ? `${isMine ? '我' : h.users[0].username}${
                    h.commentFlag ? '添加' : '删除'
                  }了评论`
                : TYPE_NAME[h.nodeType] || STATUS_NAME[h.status];

            return {
              // 流程状态
              status: h.status,
              // 时间
              timestamp: h.approveDate,
              // 节点人
              users: h.users,
              // 节点状态小图标
              icon,
              nodeType: h.nodeType,
              // 流程状态名
              processName,
              // 审批意见 销假原因
              comment: h.opinion,
              // 加签人
              addSignNodeUserInfo: h.addSignNodeUserInfo,
              // 文件
              // opinionFiles: h.opinionFiles ? JSON.parse(h.opinionFiles).map((l) => l.fileUrl) : []
              opinionFiles: h.opinionFiles ? JSON.parse(h.opinionFiles) : [],
              // 1会签 2或签
              approveType: h.approveType,
              commentFlag: h.commentFlag,
              commentId: h.commentId,
              isMine
            };
          });
        });
      },
      downList() {
        this.isReverse = !this.isReverse;
      },
      deleteComment(id) {
        this.$confirm('确定删除此评论？', '提示').then(() => {
          const loading = this.$loading();
          deleteComment(id)
            .then(() => {
              loading.close();
              this.$message.success('操作成功');
              this.$emit('refresh');
            })
            .catch(() => {
              loading.close();
            });
        });
      }
    }
  };
</script>
<style lang="scss">
  .apply-log {
    display: flex;
    flex-direction: column;

    .el-icon-arrow-down {
      cursor: pointer;
    }

    .is-reverse {
      transform: rotate(180deg);
      transition: transform 0.3s;
    }

    .h-upload-content {
      display: none;
    }

    .panel-main {
      width: 100%;
      padding: 0 !important;
      padding-bottom: 4px !important;

      .el-timeline {
        padding-top: 16px;
      }

      .footer {
        word-wrap: break-word;
        word-break: normal;
      }
    }

    .el-timeline-item__node {
      padding: 1px 0;
      background: #fff;

      i {
        width: 16px;
        height: 16px;
        background: #cfcfcf;
        border-radius: 50%;
      }

      .icon-process_create,
      .icon-process_yes {
        color: #0bbd87;
        background: #fff;
      }

      .icon-process_time {
        color: #3b7cff;
        background: #fff;
      }

      .icon-process_no {
        color: #ff5151;
        background: #fff;
      }

      .icon-process_recall {
        color: #cfcfcf;
        background: #fff;
      }
    }

    .el-timeline-item__node--normal {
      width: 16px;
      height: 16px;
      margin-left: -2px;
    }

    .h-upload {
      margin-top: 4px;
    }

    .el-timeline-item__tail {
      border-color: #cfcfcf;
    }

    .blue .el-timeline-item__tail {
      border-color: #3b7cff !important;
    }

    .green .el-timeline-item__tail {
      border-color: #15bc84 !important;
    }

    .error .el-timeline-item__tail {
      border-color: #ff5151 !important;
    }

    .el-timeline-item {
      padding-bottom: 40px;

      &:last-child {
        padding-bottom: 0;
      }
    }

    .is-bottom {
      display: none;
    }

    .h-panel {
      width: 100%;
    }

    .log-content {
      display: flex;
      justify-content: space-between;

      .log-start {
        display: flex;
        color: #333;
        font-weight: 550;
        line-height: 22px;

        span {
          margin-top: 0 !important;
          margin-left: 8px;
          color: #999;
        }
      }

      .log-end {
        align-items: flex-end;
        color: #999;
        line-height: 22px;
      }

      div {
        display: flex;
        flex-direction: column;

        span:last-child {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          max-width: 600px;
          margin-top: 6px;

          span {
            display: flex;
            align-items: center;
            margin-right: 16px;
            margin-bottom: 16px;
          }
        }
      }

      .user-info {
        cursor: pointer;
      }

      img {
        width: 30px;
        height: 30px;
        margin-right: 8px;
        margin-left: 16px;
        border-radius: 50%;

        &:first-child {
          margin-left: 0;
        }
      }
    }

    .footer {
      margin-top: 12px;
      padding: 16px;
      color: #333;
      line-height: 22px;
      text-align: left;
      background: #f7f8fa;
      border-radius: 4px;

      &.is-deleted {
        text-decoration: line-through;
      }
    }

    .delete-btn {
      float: right;
    }
  }
</style>
