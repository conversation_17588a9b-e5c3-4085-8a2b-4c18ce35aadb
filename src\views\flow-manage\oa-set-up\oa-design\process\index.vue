<script>
  import FlowCard from './flow-card/index.vue';
  import PropPanel from './prop-panel/index.vue';
  import { NodeUtils, getMockData } from './flow-card/util.js';
  import { deepClone } from '@/util/util.js';

  export default {
    name: 'process',
    props: ['tabName', 'conf', 'nodeFields'],
    data() {
      let data = getMockData();
      if (typeof this.conf === 'object' && this.conf !== null) {
        Object.assign(data, this.conf);
      }
      return {
        data, // 流程图数据
        updateId: 0, // key值 用于模拟$forceUpdate
        activeData: null, // 被激活的流程卡片数据，用于属性面板编辑
        verifyMode: false,
        fieldStatus: null
      };
    },
    watch: {
      conf(val) {
        if (typeof val === 'object' && val !== null) {
          this.data = val;
        }
      }
    },
    methods: {
      // 给父级组件提供的获取流程数据得方法
      getData() {
        this.verifyMode = true;
        if (NodeUtils.checkAllNode(this.data)) {
          return Promise.resolve({ formData: this.data });
        } else {
          return Promise.reject({ target: this.tabName });
        }
      },
      /**
       * 接收所有FlowCard事件触发
       * @param { Object } data - 含有event(事件名称)/args(参数)两个属性
       */
      eventReciver({ event, args }) {
        if (event === 'edit') {
          this.activeData = args[0]; // 打开属性面板
          let node = this.nodeFields.find(
            ({ nodeId }) => nodeId === args[0].nodeId
          );
          this.fieldStatus = node ? node.fieldStatus : null;
          return;
        }
        // console.log(event);
        // 本实例只监听了第一层数据（startNode）变动
        // 为了实时更新  采用$forceUpdate刷新 但是由于某些条件下触发失效（未排除清除原因）
        // 使用key + 监听父组件updateId方式强制刷新
        let newNode = NodeUtils[event](...args);
        if (event === 'addApprovalNode' && newNode.type === 'approver') {
          this.$emit('add-approve-node', newNode.nodeId);
        } else if (event === 'deleteNode' && args[0].type === 'approver') {
          this.$emit('delete-approve-node', args[0].nodeId);
        }
        this.$emit('start-node-change', this.data);
        this.forceUpdate();
      },
      updateErrorNumber() {
        this.$emit('change-error-number', true);
      },

      forceUpdate() {
        this.updateId = this.updateId + 1;
      },
      /**
       * 属性面板提交事件
       * @param { Object } value - 被编辑的节点的properties属性对象
       */
      onPropEditConfirm(value) {
        this.activeData = Object.assign(this.activeData, value);
        this.onClosePanel();
        this.forceUpdate();
      },
      /**
       * 属性面板取消事件
       */
      onClosePanel() {
        this.activeData = null;
      },

      // 传formIds 查询指定组件 未传时  判断所有组件
      isFilledPCon(formIds) {
        let res = false;
        const loopChild = (parent, callback) =>
          // eslint-disable-next-line no-use-before-define
          parent.childNode && loop(parent.childNode, callback);
        const loop = (data, callback) => {
          if (res || !data) {
            return;
          } // 查找到就退出
          if (Array.isArray(data.conditionNodes)) {
            const uesd = data.conditionNodes.some((c) => {
              const cons = c.properties.conditions || [];
              return Array.isArray(formIds)
                ? cons.some((item) => formIds.includes(item.formId)) // 查询特定组件
                : cons.length > 0; // 只要有节点设置了条件 说明就有组件作为条件被使用
            });
            uesd
              ? callback()
              : data.conditionNodes.forEach((t) => loopChild(t, callback));
          }
          loopChild(data, callback);
        };
        loop(this.data, () => (res = true));
        return res;
      },
      onUpdateFieldStatus(fieldStatus) {
        let index = this.nodeFields.findIndex(
          ({ nodeId }) => nodeId === this.activeData.nodeId
        );
        if (index > -1) {
          let list = deepClone(this.nodeFields);
          list[index].fieldStatus = fieldStatus;
          this.$emit('update:nodeFields', list);
        }
      }
    },
    // eslint-disable-next-line no-unused-vars
    render: function (h) {
      NodeUtils.globalID = NodeUtils.getMaxNodeId(this.data);
      return (
        <div class="flow-container">
          <FlowCard
            verifyMode={this.verifyMode}
            key={this.updateId}
            data={this.data}
            onEmits={this.eventReciver}
            onUpdateErrorNumber={this.updateErrorNumber}
            style={{ transform: `scale(1)` }}
          />
          <PropPanel
            value={this.activeData}
            processData={this.data}
            formJson={this.formJson}
            onConfirm={this.onPropEditConfirm}
            onCancel={this.onClosePanel}
            fieldStatus={this.fieldStatus}
            onStatus={this.onUpdateFieldStatus}
          />
        </div>
      );
    }
  };
</script>

<style scoped lang="scss">
  .flow-container {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 20px;
    padding-bottom: 80px;
    overflow: auto;
    text-align: center;
    background: #f0f2f5;
  }

  .scale-slider {
    position: fixed;
    right: 0;
    z-index: 99;

    .btn {
      display: inline-block;
      margin-right: 10px;
      margin-left: 10px;
      padding: 4px;
      background: #fff;
      border: 1px solid #cacaca;
      border-radius: 3px;
      cursor: pointer;
    }
  }
</style>
