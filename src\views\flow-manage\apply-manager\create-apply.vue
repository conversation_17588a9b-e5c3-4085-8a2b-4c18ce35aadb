<template>
  <el-dialog
    v-loading="dialogLoading"
    :visible.sync="createApplyVisible"
    append-to-body
    :before-close="closeDialog"
    width="640px"
    class="create-apply"
    title="创建审批"
  >
    <section>
      <el-button @click="goApply" type="primary" size="small"
        >自定义审批</el-button
      >
      <p class="apply-tip">使用审批模板</p>
      <div class="apply-list">
        <div
          v-for="li of list"
          :key="li.id"
          @click="createApply(li.id, 'copy')"
          class="apply-template"
        >
          <img v-oss :src="li.icon || '/oa/icon_diy_1.png'" />
          <div>
            <header>
              {{ li.processName }}
            </header>
            <p v-if="li.remark" class="apply-tip">{{ li.remark }}</p>
          </div>
        </div>
      </div>
    </section>
  </el-dialog>
</template>
<script>
  import { listCommon } from '@/api/flow/process';
  export default {
    props: {
      createApplyVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        dialogLoading: false,
        list: []
      };
    },
    watch: {
      createApplyVisible() {
        if (this.createApplyVisible) {
          listCommon().then((res) => {
            if (res && res.data && res.data.success) {
              this.list = res.data.data;
            }
          });
        }
      }
    },
    methods: {
      goApply() {
        this.$router.push({ name: 'oaSetUp' });
      },
      closeDialog() {
        this.$emit('update:createApplyVisible', false);
      },
      createApply(id, type) {
        this.$router.push({ name: 'oaSetUp', query: { id, type } });
      }
    }
  };
</script>
<style lang="scss">
  .create-apply {
    .el-dialog__body {
      padding: 16px !important;
      section {
        button {
          margin-left: 8px;
        }
        .apply-list {
          display: flex;
          flex-wrap: wrap;
          max-height: 280px;
          overflow-y: auto;
        }
      }
      p {
        width: 100%;
      }
      .apply-tip {
        margin-bottom: 8px;
        margin-left: 8px;
        color: #999;
      }
      .apply-template {
        width: calc(50% - 58px);
        display: flex;
        background: #f7f8fa;
        margin: 0 8px 16px;
        border-radius: 4px;
        padding: 20px;
        cursor: pointer;
        .apply-tip {
          margin-top: 10px;
        }
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 12px;
        }
        header {
          font-size: 16px;
          line-height: 16px;
          color: #333;
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        p {
          flex: 1;
          margin: 0;
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        div {
          width: calc(100% - 40px);
          display: flex;
          flex-direction: column;
          justify-content: center;
        }
        &:hover {
          background: #f0f2f5;
        }
      }
    }
  }
</style>
