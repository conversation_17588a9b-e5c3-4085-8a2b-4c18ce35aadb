<template>
  <el-dialog
    :title="title"
    :visible.sync="chooseVisible"
    width="544px"
    append-to-body
    custom-class="choose"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeName" @tab-click="changeTabs">
      <el-tab-pane label="成员" name="people"></el-tab-pane>
      <el-tab-pane label="按岗位" name="position"></el-tab-pane>
      <!-- <el-tab-pane label="按职务" name="job"></el-tab-pane> -->
    </el-tabs>
    <div v-loading="loading" class="content">
      <select-staff
        :checked-list.sync="searchList"
        :radio="activeName"
        :tree-data="treeData"
      ></select-staff>
    </div>
    <div class="footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button @click="save" type="primary">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { deepClone } from '@/util/util.js';
  import selectStaff from './select-staff';
  import { userTree } from '@/api/system/post';
  import { jobUserTree } from '@/api/system/company';
  import { getStaffTreeList } from '@/api/system/dept';
  export default {
    components: { selectStaff },
    props: {
      chooseVisible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '选择成员'
      },
      ids: {
        type: Array,
        default: () => []
      },
      radio: {
        type: String,
        default: 'people'
      }
    },
    data() {
      return {
        activeName: 'people',
        searchList: [],
        treeData: [],
        loading: false
      };
    },
    watch: {
      chooseVisible: {
        handler(val) {
          if (val) {
            this.searchList = this.ids || [];
            this.getData();
          } else {
            this.activeName = 'people';
          }
        },
        immediate: true
      }
    },
    created() {
      this.getData();
    },
    methods: {
      async getData() {
        this.loading = true;
        const url = {
          people: getStaffTreeList,
          position: userTree,
          job: jobUserTree
        };
        url[this.activeName]()
          .then((data) => {
            if (data && data.data && data.data.success) {
              this.treeData = data.data.data;
            } else if (data && data.data && !data.data.success) {
              this.$message.error(data.data.message);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      changeTabs(tab) {
        this.activeName = tab.name;
        this.getData();
      },
      save() {
        if (this.searchList.length > 20) {
          this.$message.error('人数过多,请重新选择,最多可选择20人');
          return;
        }
        const list = deepClone(this.searchList);
        this.$emit('choose-save', list);
        this.handleClose();
      },
      handleClose() {
        this.$emit('update:chooseVisible', false);
      }
    }
  };
</script>
<style lang="scss">
  .el-dialog__wrapper .choose .el-dialog__header {
    padding-bottom: 24px;
    border-bottom: 1px solid #d9d9d9;

    .el-dialog__title {
      color: #121620;
      line-height: 16px;
    }

    .el-dialog__headerbtn {
      right: 24px;
      font-weight: bolder;
    }
  }

  .choose {
    .el-tabs--top {
      padding-right: 24px;
      padding-left: 24px;

      .el-tabs__header {
        margin-top: 15px;
        margin-bottom: 0;
      }
    }

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      padding: 0 !important;

      .header-tip {
        margin-bottom: 4px;
        padding-bottom: 16px;
        padding-left: 24px;
        border-bottom: 1px solid #d9d9d9;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88px;

      button {
        width: 140px;
        height: 40px;
      }
    }
  }
</style>
