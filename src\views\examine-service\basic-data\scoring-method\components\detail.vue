<template>
  <div class="wrapper">
    <div class="nav_header">
      <h3>基本信息</h3>
    </div>
    <el-form label-width="120px" label-suffix="：" size="small">
      <el-row :gutter="15">
        <el-col :span="8">
          <el-form-item label="打分方式编号" style="margin-bottom: 0">
            {{ form.code || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="打分方式名称" style="margin-bottom: 0">
            {{ form.name || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型" style="margin-bottom: 0">
            {{ form.type | filterDafenType }}
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="赋分等级" style="margin-bottom: 0">
            {{ form.scoreGrade || '--' }}
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="精度" style="margin-bottom: 0">
            {{ form.accuracy }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分制" style="margin-bottom: 0">
            {{ form.scoreSystem | filterScoreSystem }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生效" style="margin-bottom: 0">
            {{ form.status | filterDafenStatus }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <div v-if="this.form.type !== 2">
      <div class="nav_header">
        <h3>等级</h3>
      </div>
      <content-table :detail="true" :source="source" />
    </div> -->
    <div class="footer_button">
      <el-button type="primary" size="small" @click="back">返回</el-button>
    </div>
  </div>
</template>

<script>
  import ContentTable from './content-table';
  import { getDetail } from '@/api/examine/scoring-method';
  export default {
    name: 'detail',
    props: {
      id: {
        type: String,
        default: ''
      }
    },
    components: {
      ContentTable
    },
    data() {
      return {
        source: [],
        form: {}
      };
    },
    filters: {
      filterScoreSystem(type) {
        switch (type) {
          case '1':
            return '百分制';
          case '2':
            return '十分制';
          case '3':
            return '五分制';
          case '4':
            return '基本分';
          case '5':
            return '任意';
          default:
            return '---';
        }
      }
    },
    mounted() {
      this.getInfo();
    },
    methods: {
      async getInfo() {
        try {
          const res = await getDetail(this.id);
          const data = res.data.data;
          this.form = data;
          this.source = data.scoreItemList;
        } catch (e) {
          console.error(e);
        }
      },
      back() {
        this.$emit('close', false);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 10px 30px;

    .nav_header {
      position: relative;
      margin-bottom: 20px;
      border-bottom: 1px solid #dfdfdf;

      h3 {
        margin: 10px 0;
        padding-left: 12px;
        font-size: 17px;
        border-left: 5px solid #1e9fff;
      }
    }

    .footer_button {
      margin: 30px 0 0;
      text-align: center;
    }
  }
</style>
