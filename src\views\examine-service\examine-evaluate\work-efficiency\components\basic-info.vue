<template>
  <div class="info_form_mb5">
    <el-form label-width="110px" label-suffix="：" size="small">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="单号">
            {{ form.code || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="考核周期">
            {{ form.periodItemName || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="截止日期">
            {{ form.deadline || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="接收人">
            {{ form.assessorName || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联评价方案">
            {{ form.schemeCode || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据状态">
            {{ form.status | changeStatus }}
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="isDetail">
          <el-form-item label="评价日期">
            {{ form.evaluationDate | formatDate }}
          </el-form-item>
        </el-col>
        <el-col :span="8" v-else>
          <el-form-item label="评价日期">
            <el-date-picker
              type="datetime"
              v-model="cFillingDate"
              value-format="yyyy-MM-dd"
              format="yyyy 年 MM 月 dd 日"
              placeholder="选择评价日期"
              style="width: 200px"
              :editable="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <div class="text-style">
              {{ form.comment || '---' }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'basic-info',
    props: {
      form: {
        type: Object,
        default() {
          return {};
        }
      },
      isDetail: {
        type: Boolean,
        default: false
      },
      fillingDate: {
        type: String,
        require: true
      }
    },
    model: {
      prop: 'fillingDate',
      event: 'change'
    },
    computed: {
      cFillingDate: {
        get() {
          return this.fillingDate;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    },
    methods: {
      handleChange() {
        this.$emit('syncDate', this.value1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .text-style {
    padding-top: 5px;
    color: #5a5a5a;
    line-height: 20px;
  }
</style>
