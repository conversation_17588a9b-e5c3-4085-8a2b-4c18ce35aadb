import request from '@/router/axios';
// 左侧树 - 查询
export const getTreeData = () => {
  return request({
    url: '/api/examine/scheme-tag/tag-tree',
    method: 'get'
  });
};
// 左侧树 - 新增
export const postTreeAdd = (data) => {
  return request({
    url: '/api/examine/scheme-tag/save',
    method: 'post',
    data
  });
};
// 左侧树 - 编辑
export const postTreeEdit = (data) => {
  return request({
    url: '/api/examine/scheme-tag/update',
    method: 'post',
    data
  });
};
// 左侧树 - 删除
export const getTreeDelete = (id) => {
  return request({
    url: `/api/examine/scheme-tag/remove?id=${id}`,
    method: 'get'
  });
};

// 右侧列表 - 列表数据查询
export const getListData = (params) => {
  return request({
    url: `/api/examine/scheme/list`,
    method: 'get',
    params
  });
};

// 右侧列表 - 新增方案
export const postSchemeAdd = (data) => {
  return request({
    url: '/api/examine/scheme/save',
    method: 'post',
    data
  });
};
// 右侧列表 - 方案详情
export const getSchemeDetail = (code) => {
  return request({
    url: `/api/examine/scheme/detail?code=${code}`,
    method: 'get'
  });
};
// 右侧列表 - 编辑方案
export const postSchemeEdit = (data) => {
  return request({
    url: '/api/examine/scheme/update',
    method: 'post',
    data
  });
};

// 批量删除方案
export const postSchemeDel = (data) => {
  return request({
    url: '/api/examine/scheme/remove',
    method: 'post',
    data
  });
};

// 获取考核周期列表
export const getPeriodList = (params) => {
  return request({
    url: `/api/examine/period/item-list`,
    method: 'get',
    params
  });
};

// 根据机构和岗位获取人员列表
export const getUserByDeptAndPost = (data) => {
  return request({
    url: '/api/szyk-user/get-user-of-dept-post',
    method: 'post',
    data
  });
};

// 生成评价单
export const postCollectSave = (data) => {
  return request({
    url: '/api/examine/evaluation-form/save',
    method: 'post',
    data
  });
};
