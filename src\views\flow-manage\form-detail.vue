<template>
  <div>
    <el-form v-model="form" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="项目名称：">{{
            form.proName || '---'
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始时间：">{{
            form.startTime || '---'
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间：">{{
            form.endTime || '---'
          }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：">{{ form.remark || '---' }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    props: {
      form: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {};
    },
    methods: {}
  };
</script>
