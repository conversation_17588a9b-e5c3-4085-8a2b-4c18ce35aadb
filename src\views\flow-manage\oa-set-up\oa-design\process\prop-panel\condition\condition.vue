<template>
  <el-drawer
    :before-close="cancel"
    :visible.sync="visible"
    append-to-body
    size="528px"
    custom-class="oa-approver-condition"
    show-close
    :wrapper-closable="false"
  >
    <div class="oa-approver-overflow">
      <div class="oa-approver-drawer">
        <div class="oa-approver-drawer-title">条件</div>
        <el-form
          ref="form"
          class="oa-approver-drawer-form"
          size="small"
          label-position="top"
        >
          <div v-for="(item, index) in properties.conditions" :key="index">
            <div class="oa-approvel-condition">
              <div class="oa-approvel-condition-top">
                <div>条件组</div>
                <i
                  @click="deleteConditionGroup(index)"
                  class="el-icon-delete"
                ></i>
              </div>
              <setting-condition
                @get-conditions="(data) => getConditions(data, index)"
                :condition-list="item"
              ></setting-condition>
            </div>
            <div
              v-if="index !== properties.conditions.length - 1"
              class="oa-condition-or-hr"
            >
              或
            </div>
          </div>
          <div @click="addConditonGroup" class="oa-add-button">
            <i class="el-icon-plus"></i>
            <span>添加条件组</span>
          </div>
          <!-- </el-form-item> -->
          <div class="oa-approvel-save-button">
            <el-button @click="cancel" size="small">返回</el-button>
            <el-button @click="onSubmit" size="small" type="primary"
              >保存</el-button
            >
          </div>
        </el-form>
      </div>
    </div>
  </el-drawer>
</template>
<script>
  import settingCondition from './setting-condition';
  import { deepClone } from '@/util/util.js';
  import { getRuleList } from '@/api/desk/flow';
  const getAllNode = (list) => {
    let temp = [];
    const getNode = (array) => {
      array.forEach((item) => {
        temp.push(item);
        if (item.children && item.children.length) {
          getNode(item.children);
        }
      });
    };
    getNode(list);
    return temp;
  };
  export default {
    components: {
      settingCondition
    },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      value: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        properties: {
          conditions: [
            [
              {
                type: '',
                name: '',
                form: {
                  condition: '',
                  relation: '',
                  value: ''
                }
              }
            ]
          ]
        },
        json: [],
        typeList: [],
        ruleList: []
      };
    },
    computed: {
      formDesignList() {
        return this.$store.state.oaSetUp.formDesignList;
      }
    },
    watch: {
      visible: {
        handler() {
          if (this.visible) {
            if (
              this.value &&
              this.value.properties &&
              this.value.properties.conditions &&
              this.value.properties.conditions.length > 0
            ) {
              this.properties = deepClone(this.value.properties);
            } else {
              this.properties = {
                conditions: [
                  [
                    {
                      type: '',
                      name: '',
                      form: {
                        condition: '',
                        relation: '',
                        value: ''
                      }
                    }
                  ]
                ]
              };
            }
          } else {
            this.properties = {
              conditions: [
                [
                  {
                    type: '',
                    name: '',
                    form: {
                      condition: '',
                      relation: '',
                      value: ''
                    }
                  }
                ]
              ]
            };
          }
        },
        immediate: true
      },
      formDesignList(val) {
        this.json = val;
        this.typeFilter();
      }
    },
    created() {
      // this.getRule(); // 20221025
      this.json = this.formDesignList;
      this.typeFilter();
    },
    methods: {
      addConditonGroup() {
        this.properties.conditions.push([
          {
            type: '',
            name: '',
            form: {
              condition: '',
              relation: '',
              value: ''
            }
          }
        ]);
      },
      cancel() {
        this.$emit('update:visible', false);
      },
      getConditions(data, index) {
        this.properties.conditions[index] = data;
        this.$forceUpdate();
      },
      deleteConditionGroup(index) {
        this.properties.conditions.splice(index, 1);
      },
      typeFilter() {
        let tList = this.json.filter((l) => {
          return (
            ([
              'checkbox',
              'radio',
              'inputMoney',
              'inputNumber',
              'computed'
            ].includes(l.type) &&
              l.valueJson.required) ||
            ['rest', 'work', 'out', 'trip'].includes(l.type)
          );
        });
        let arr = [];
        let rList = this.ruleList;
        tList.forEach((item) => {
          let types = ['rest1', 'rest4', 'work4', 'out3', 'trip8'];
          if (['rest', 'work', 'out', 'trip'].includes(item.type)) {
            let list = getAllNode(item.children);
            let oneObj = list.filter((i) => types.includes(i.id));
            if (oneObj) {
              // this.getRule();
              oneObj.forEach(({ type, id, valueJson }) => {
                arr.push({
                  type,
                  id,
                  name: valueJson.name || '',
                  valueList: id === 'rest1' ? rList : ''
                });
              });
            }
          } else {
            arr.push({
              type: item.type || '',
              id: item.id || '',
              name: item.valueJson.name || '',
              valueList: item.valueJson.options || []
            });
          }
        });
        arr.unshift({
          type: 'selectPerson',
          id: 'startUser',
          name: '发起人'
        });
        this.typeList = [...arr];
      },
      getRule() {
        getRuleList().then((res) => {
          let { data } = res.data;
          let arr = [];
          data.map((item) => {
            arr.push({ value: item.ruleName });
          });
          this.ruleList = arr;
        });
      },
      validate() {
        let result = true;
        if (
          this.properties.conditions.length &&
          this.properties.conditions[0].length
        ) {
          this.properties.conditions.some((item) => {
            item.every((res) => {
              if (res.type) {
                if (
                  res.type &&
                  res.form.condition &&
                  res.form.relation &&
                  res.form.value.length
                ) {
                  return true;
                } else {
                  result = false;
                  return false;
                }
              } else {
                result = false;
                return false;
              }
            });
          });
        } else {
          result = false;
          return false;
        }
        return result;
      },
      onSubmit() {
        if (!this.validate()) {
          this.$message.error('请将信息设置完整');
          return;
        }
        let contentIntro = [];
        this.properties.conditions.forEach((item) => {
          item.map((i) => {
            if (i.form.condition === 'startUser') {
              let arrUser = [];
              if (i.form.value) {
                i.form.value.map((l) => {
                  l.valueList.map((m) => {
                    arrUser.push(m.name);
                  });
                });
              }
              contentIntro.push('发起人为' + arrUser.join('/'));
            } else {
              let arr = this.typeList.filter(
                (res) => res.id === i.form.condition
              );
              if (arr[0].name !== i.name) {
                i.name = arr[0].name;
              }
              let str =
                arr[0].name +
                (i.form.relation
                  ? i.form.relation === 'radioEq'
                    ? '是'
                    : i.form.relation === 'multipleAll'
                    ? '同时选中'
                    : i.form.relation === 'multipleAny'
                    ? '选中任意'
                    : i.form.relation
                  : '为') +
                (Array.isArray(i.form.value)
                  ? i.form.type === 'checkbox'
                    ? i.form.value.join('/')
                    : i.form.value
                  : i.form.value);
              contentIntro.push(str);
            }
          });
        });
        this.$emit('add-list', {
          type: 'condition',
          content: `条件：${contentIntro.join(',')}`,
          properties: this.properties
        });
        this.cancel();
      }
    }
  };
</script>
<style lang="scss">
  .oa-approver-condition {
    .el-drawer__header {
      position: absolute;
      box-sizing: border-box;
      width: 100%;
    }

    .el-drawer__body {
      height: 100%;
    }
  }
</style>
<style lang="scss" scoped>
  .oa-approver-condition {
    position: relative;

    .oa-approver-overflow {
      height: calc(100% - 65px);
      overflow: auto;

      .oa-approver-drawer {
        padding: 23px 24px;
        text-align: left;

        .oa-approver-drawer-title {
          margin-bottom: 39px;
          color: #333;
          font-weight: 550;
          font-size: 18px;
          line-height: 18px;
        }

        .oa-approver-drawer-form {
          .oa-approvel-condition {
            margin: 6px 0 12px;
            overflow: hidden;
            border: 1px solid #d9d9d9;
            border-radius: 4px;

            .oa-approvel-condition-top {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 16px 12px;
              color: #333;
              font-weight: 400;
              font-size: 14px;
              line-height: 14px;
              background: #fafafa;
              border-bottom: 1px solid #d9d9d9;

              i {
                color: #8e8e8e;
                font-size: 16px;
                line-height: 16px;

                &:hover {
                  color: #ff4648;
                  cursor: pointer;
                }
              }
            }

            .oa-approvel-condition-button {
              display: flex;
              align-items: center;
              margin-bottom: 24px;
              margin-left: 24px;
              color: #409eff;
              font-weight: 400;
              font-size: 14px;
              line-height: 14px;
              cursor: pointer;

              i {
                margin-right: 2px;
                font-size: 16px;
              }
            }
          }

          // 添加审批人add
          .oa-add-button {
            display: flex;
            align-items: center;
            margin-top: 16px;
            margin-bottom: 30px;
            color: #409eff;
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            cursor: pointer;

            i {
              margin-right: 2px;
              font-size: 16px;
            }
          }

          .oa-condition-or-hr {
            margin: 15px 0;
            color: #333;
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
          }
        }

        .oa-approvel-save-button {
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          width: 100%;
          height: 64px;
          padding: 16px 24px;
          text-align: right;
          background: #fff;
          box-shadow: 0 -1px 0 0 #0015291f;

          button {
            width: 80px;
            height: 32px;
          }
        }
      }
    }
  }
</style>
