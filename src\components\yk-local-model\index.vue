<template>
  <div class="screen" v-show="open">
    <el-row :gutter="15">
      <el-col :span="20">
        <div class="title_h2">{{ title }}</div>
      </el-col>
      <el-col :span="4" style="text-align: right">
        <el-button type="primary" size="mini" @click="back">关闭</el-button>
      </el-col>
    </el-row>
    <div v-if="open">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'showExcel',
    props: {
      open: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: ''
      }
    },
    methods: {
      // 关闭
      back() {
        this.$emit('close', false);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .screen ::v-deep {
    width: 100%;

    .title_h2 {
      margin: 0;
      font-weight: bold;
      font-size: 20px;
      letter-spacing: 2px;
    }
  }
</style>
