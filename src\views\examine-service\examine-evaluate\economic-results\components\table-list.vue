<template>
  <el-table :data="list" border style="width: 100%" size="small">
    <el-table-column type="index" label="序号" width="50" align="center" />
    <el-table-column prop="code" label="评价单编号" align="center">
    </el-table-column>
    <el-table-column prop="assessorNames" label="接收人" align="center">
    </el-table-column>
    <el-table-column
      prop="periodItemName"
      width="150"
      label="考核周期"
      align="center"
    >
    </el-table-column>
    <el-table-column width="120" label="截止日期" align="center">
      <template slot-scope="scope">
        {{ scope.row.deadline | formatDate }}
      </template>
    </el-table-column>
    <el-table-column prop="status" width="120" label="单据状态" align="center">
      <template slot-scope="scope">
        {{ scope.row.status | changeStatus }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="150" align="center">
      <template slot-scope="scope">
        <el-button
          type="text"
          size="small"
          icon="el-icon-view"
          @click="$emit('dispatch', 'detail', scope.row)"
          >查看</el-button
        >
        <el-button
          v-if="scope.row.status == '2'"
          type="text"
          size="small"
          icon="el-icon-edit"
          @click="$emit('dispatch', 'edit', scope.row)"
          >评价</el-button
        >
        <el-button
          v-if="scope.row.status == '1'"
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'evaluate', scope.row)"
          >评价</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-list',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      }
    }
  };
</script>
