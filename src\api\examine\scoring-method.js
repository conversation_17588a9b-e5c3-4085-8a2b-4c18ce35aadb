import request from '@/router/axios';

// 打分方式 分页查询
export function getList(params) {
  return request({
    url: '/api/examine/score-type/page',
    method: 'get',
    params
  });
}

// 打分方式 详情
export function getDetail(id) {
  return request({
    url: `/api/examine/score-type/detail/${id}`,
    method: 'get'
  });
}

// 打分方式 新增
export function postSave(data) {
  return request({
    url: `/api/examine/score-type/save`,
    method: 'post',
    data
  });
}

// 打分方式 编辑
export function postUpdate(data) {
  return request({
    url: `/api/examine/score-type/update`,
    method: 'post',
    data
  });
}

// 打分方式 删除
export function postDelete(data) {
  return request({
    url: `/api/examine/score-type/remove`,
    method: 'post',
    data
  });
}
