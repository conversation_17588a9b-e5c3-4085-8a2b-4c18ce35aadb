<template>
  <div class="wrapper">
    <div class="nav_header">
      <h3>基本信息</h3>
    </div>
    <el-form
      label-width="120px"
      label-suffix="："
      size="small"
      ref="form"
      :model="form"
      :rules="rules"
    >
      <el-row :gutter="15">
        <el-col :span="8" v-if="id.length">
          <el-form-item label="打分方式编号">
            <el-input
              v-model.trim="form.code"
              disabled
              placeholder="请输入打分方式编号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="打分方式名称" prop="name">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入打分方式名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="选择类型"
              clearable
              filterable
              @change="typeChange"
            >
              <el-option
                v-for="item in serviceDicts.type.score_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="赋分等级" prop="scoreGrade">
            <el-input-number
              v-model.number="form.scoreGrade"
              controls-position="right"
              :precision="0"
              :step="1"
              :min="2"
              :disabled="type_status"
              placeholder="请输入"
              style="width: 180px"
              @change="handleLevel"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="精度" prop="accuracy">
            <el-input-number
              v-model.number="form.accuracy"
              controls-position="right"
              :precision="0"
              :step="1"
              :min="0"
              placeholder="请输入"
              style="width: 180px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分制" prop="scoreSystem">
            <el-select
              v-model="form.scoreSystem"
              placeholder="选择分制"
              clearable
              filterable
              @change="scoreSystemFn"
            >
              <el-option
                v-for="item in serviceDicts.type.score_system"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.value == 4 && form.type == 1"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生效" prop="status">
            <el-checkbox v-model="form.status"></el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <div v-show="!type_status">
      <div class="nav_header">
        <h3>等级</h3>
      </div>
      <div style="max-width: 1200px">
        <content-table
          ref="contentTable"
          :detail="false"
          :loading="loading"
          :score-max="scoreMax"
        />
      </div>
    </div> -->
    <div class="footer_button">
      <el-button size="small" @click="back">返回</el-button>
      <el-button type="primary" size="small" @click="save">保存</el-button>
    </div>
  </div>
</template>

<script>
  import ContentTable from './content-table';
  import {
    getDetail,
    postSave,
    postUpdate
  } from '@/api/examine/scoring-method';
  import { mapMutations, mapState } from 'vuex';
  import { cloneDeep } from 'lodash';
  import { numBoolChange } from '@/util/util';

  const rules = {
    name: [{ required: true, message: '请输入打分方式名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    accuracy: [{ required: true, message: '请输入精度', trigger: 'blur' }],
    scoreSystem: [{ required: true, message: '请输入分制', trigger: 'blur' }]
  };

  export default {
    name: 'FormList',
    serviceDicts: ['score_type', 'score_system'],
    components: {
      ContentTable
    },
    props: {
      id: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        form: {
          code: '',
          name: '',
          type: undefined,
          scoreGrade: undefined,
          accuracy: 0,
          scoreSystem: undefined,
          status: false
        },
        rules,
        type_status: true,
        loading: true,
        scoreMax: 0
      };
    },
    computed: {
      ...mapState({
        source: (state) => state.scoringMethod.source
      })
    },
    watch: {
      'form.scoreSystem'(val) {
        this.loading = !val;
      }
    },
    mounted() {
      this.init();
    },
    methods: {
      ...mapMutations([
        'SOURCE_INCREASE',
        'SOURCE_DECREASE',
        'SOURCE_CLEAR_VALUE',
        'SET_SOURCE',
        'SOURCE_CLEAR'
      ]),
      // 返回
      back() {
        this.$emit('close', false);
      },
      // 保存
      save() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            const isOk = this.source.some((item) => {
              if (item.itemName === '' || item.itemScore === null) {
                return true;
              }
            });
            if (isOk) {
              this.$message({
                type: 'warning',
                message: '请完善，等级明细内的名称或分值数据!'
              });
              return false;
            }
            const form = Object.assign({}, this.form, {
              status: numBoolChange(this.form.status),
              scoreItemList: this.source
            });
            try {
              if (this.id) {
                // 编辑
                const res = await postUpdate(form);
                const code = res.data.code;
                if (code === 200) {
                  this.$message({
                    type: 'success',
                    message: '打分方式数据编辑成功！'
                  });
                  this.$emit('update');
                  this.back();
                }
              } else {
                // 新增
                const res = await postSave(form);
                const code = res.data.code;
                if (code === 200) {
                  this.$message({
                    type: 'success',
                    message: '打分方式新增成功！'
                  });
                  this.$emit('update');
                  this.back();
                }
              }
            } catch (e) {
              console.error(e);
            }
          } else {
            return false;
          }
        });
      },
      // 赋分等级
      handleLevel(num = 0) {
        const len = this.source.length;
        if (num > len) {
          const value = num - len;
          for (let i = 0; i < value; i++) {
            this.SOURCE_INCREASE({
              itemName: '',
              itemScore: '',
              isDefault: 0,
              ratioOperator: undefined,
              ratio: 0
            });
          }
        }
        if (num < len) {
          const value = len - num;
          for (let i = 0; i < value; i++) {
            this.SOURCE_DECREASE();
          }
        }
      },
      // 类型切换
      typeChange(type) {
        this.form.scoreGrade = undefined;
        if (type == 1) {
          this.type_status = false;
          // 类型：赋分法，不可以选分制：基本分
          if (this.form.scoreSystem == 4) {
            this.form.scoreSystem = undefined;
          }
        } else {
          this.type_status = true;
          this.SOURCE_CLEAR();
        }
      },
      // 分制改变
      scoreSystemFn(val) {
        let maxNum = 0;
        switch (val) {
          case '1': // 百分制
            maxNum = 100;
            break;
          case '5': // 任意
            maxNum = 0;
            break;
          default:
            maxNum = 0;
            break;
        }
        this.scoreMax = maxNum;
        this.SOURCE_CLEAR_VALUE();
      },
      // 初始化
      init() {
        if (this.id) {
          this.detail();
        }
      },
      // 详情
      async detail() {
        try {
          const res = await getDetail(this.id);
          const data = res.data.data;
          this.form = cloneDeep(data);
          this.form.status = numBoolChange(data.status);

          this.typeChange(this.form.type);
          this.scoreSystemFn(this.form.scoreSystem);
          this.form.scoreGrade = data.scoreGrade || undefined;
          this.SET_SOURCE(data.scoreItemList);
        } catch (e) {
          console.error(e);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 10px 30px;

    .nav_header {
      position: relative;
      margin-bottom: 20px;
      border-bottom: 1px solid #dfdfdf;

      h3 {
        margin: 10px 0;
        padding-left: 12px;
        font-size: 17px;
        border-left: 5px solid #1e9fff;
      }
    }

    .footer_button {
      margin: 30px 0 0;
      text-align: center;
    }
  }
</style>
