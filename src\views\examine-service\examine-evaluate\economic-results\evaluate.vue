<template>
  <div class="wrapper" v-loading="loading">
    <!--  基础信息  -->
    <el-collapse style="border: 0" v-model="collapse">
      <el-collapse-item name="one">
        <template slot="title">
          <h2 class="title">基础信息</h2>
        </template>
        <basic-info :form="basic" />
      </el-collapse-item>
    </el-collapse>
    <section>
      <div class="title_wrapper">
        <h2 class="title">评价信息</h2>
        <el-tooltip effect="dark" content="全屏展示" placement="top-start">
          <i class="el-icon-full-screen icon-full" @click="visited = true"></i>
        </el-tooltip>
      </div>
      <!--   详情   -->
      <evaluate-list-detail v-if="isDetail" :list="listDetail" />
      <!--   评价   -->
      <evaluate-list
        v-else
        :key="1"
        :max-height="300"
        :list="listExamine"
        :period-num="basic.periodNumber"
        @sync="handleExamine"
      />
      <div class="end-btn">
        <el-button
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </section>
    <!--   全屏操作   -->
    <full-screen-table v-model="visited" :title="title">
      <!--  详情   -->
      <evaluate-list-detail
        v-if="isDetail"
        full
        :max-height="0"
        :list="listDetail"
      />
      <!--  评价   -->
      <evaluate-list
        v-else
        full
        :key="2"
        :max-height="0"
        :list="listExamine"
        :period-num="basic.periodNumber"
        @sync="handleExamine"
      />
      <div class="end-btn">
        <el-button
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="() => (visited = false)"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </full-screen-table>
  </div>
</template>

<script>
  import { getDetail, postSubmit } from '@/api/examine/economic-results';
  import { BasicInfo, EvaluateList, EvaluateListDetail } from './components';
  import { FullScreenTable } from '../../components/full-screen';

  export default {
    name: 'evaluate',
    components: {
      BasicInfo,
      EvaluateList,
      EvaluateListDetail,
      FullScreenTable
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false
      },
      code: {
        type: String,
        default: ''
      }
    },
    computed: {
      title() {
        return this.isDetail ? '查看详情' : '评价详情';
      }
    },
    data() {
      return {
        collapse: 'one',
        visited: false,
        listDetail: [],
        listExamine: [],
        basic: {},
        loadingSave: false,
        loadingSubmit: false,
        disabled: false,
        loading: false
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      async init() {
        try {
          this.loading = true;
          const res = await getDetail({
            evaluationCode: this.code
          });
          const data = res.data.data;
          this.basic = data;
          if (this.isDetail) {
            this.listDetail = data.economyResultList || [];
          } else {
            this.listExamine =
              data.economyResultList.map((item) => {
                item.accumulatedCompletion =
                  Number(item.accumulatedCompletion) || null;
                return item;
              }) || [];
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 保存&提交
      async save(type) {
        console.log(this.listExamine);
        const data = Object.assign({}, this.basic, {
          action: type,
          economyResultList: this.listExamine
        });
        // 保存
        if (type === 'save') {
          try {
            this.disabled = true;
            this.loadingSave = true;
            await postSubmit(data);
            this.$message.success('数据保存成功！');
            this.$emit('refresh');
          } catch (e) {
            console.error(e);
          } finally {
            this.disabled = false;
            this.loadingSave = false;
          }
        }
        // 提交
        if (type === 'submit') {
          const status = this.listExamine.some((item) => {
            if (
              typeof item.accumulatedCompletion !== 'number' ||
              !item.completionSituation
            ) {
              return true;
            }
          });
          if (status) {
            return this.$message.warning('存在有评价内容未填写，请核对！');
          }
          try {
            this.disabled = true;
            this.loadingSubmit = true;
            await postSubmit(data);
            this.$message.success('数据提交成功！');
            this.$emit('refresh');
          } catch (e) {
            console.error(e);
          } finally {
            this.disabled = false;
            this.loadingSubmit = false;
          }
        }
      },
      // 同步数据
      handleExamine(list) {
        this.listExamine = list;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;

    .icon-full {
      position: absolute;
      right: 0;
      bottom: 0;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
