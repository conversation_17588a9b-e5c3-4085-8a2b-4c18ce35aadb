<template>
  <common-comp
    @click.native="relationApply(value.id)"
    :value="value.name"
    :class="{ 'bc-apply-component-relation-apply': value.id }"
  />
</template>

<script>
  import CommonComp from './common-comp';

  export default {
    name: 'RelationApplyComp',
    components: { CommonComp },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    methods: {
      relationApply() {
        if (this.value.id) {
          // window.open(`#/flow-manage/apply/?id=${this.value.id}`, '__blank');
          this.$router.push(`/flow-manage/apply/?id=${this.value.id}`);
        }
      }
    }
  };
</script>

<style lang="scss">
  @import '@/styles/element-ui';

  .bc-apply-component-relation-apply {
    color: #3c7cff;
    cursor: pointer;
  }
</style>
