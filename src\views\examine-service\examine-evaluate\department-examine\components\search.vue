<template>
  <div class="info_form_mb5">
    <el-form
      ref="form"
      inline
      label-suffix="："
      label-width="100px"
      size="small"
      :model="form"
    >
      <el-row :gutter="12">
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="评价单编号" prop="code">
            <el-input
              v-model="form.code"
              placeholder="请输入评价单编号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="接收人" prop="assessorName">
            <el-input
              v-model.trim="form.assessorName"
              placeholder="请输入接收人"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="考核周期" prop="periodItemId">
            <el-input
              class="disable_cursor"
              placeholder="请选择考核周期"
              :value="periodItemName"
              @click.native="periodDialogShow = true"
              style="width: 200px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="单据状态" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择单据状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in serviceDicts.type.evaluation_form_status"
                :key="dict.value"
                :label="dict.label"
                :value="+dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="截止日期" prop="deadline">
            <el-date-picker
              type="date"
              placeholder="请选择截止日期"
              v-model="form.deadline"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="small"
            @click="search"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh-right" size="small" @click="reset"
            >重置</el-button
          >
        </el-col>
      </el-row>
    </el-form>
    <period-select
      v-if="periodDialogShow"
      :selectPeriodId="form.periodItemId"
      @dialogSave="handleDialogSave"
      @dialogClose="periodDialogShow = false"
    />
  </div>
</template>

<script>
  import { PeriodSelect } from '@/views/examine-service/components/period-select';
  import { scheme_type } from '@/constant/service';
  export default {
    name: 'search',
    serviceDicts: ['evaluation_form_status', 'period_type'],
    components: { PeriodSelect },
    data() {
      return {
        // 考核周期属性
        periodDialogShow: false,
        periodItemName: undefined,
        form: {
          code: undefined,
          assessorName: undefined,
          periodItemId: undefined,
          status: undefined,
          deadline: undefined,
          schemeType: scheme_type.depart
        }
      };
    },
    mounted() {
      this.search();
    },
    methods: {
      // 接收选中的考核周期
      handleDialogSave(data) {
        this.periodDialogShow = false;
        this.form.periodItemId = data.id;
        this.periodItemName = data.itemName;
      },
      search() {
        this.$emit('query', this.form);
      },
      reset() {
        this.periodItemName = undefined;
        this.$refs.form.resetFields();
        this.search();
      }
    }
  };
</script>
