<template>
  <div class="bc-form-build-dateranage">
    <el-col :span="12">
      <el-form-item
        :prop="`${prop}.startTime`"
        :label="`${data.nameOne}：`"
        :rules="startRules"
      >
        <date-comp v-model="startTime" :data="data"></date-comp>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item
        :prop="`${prop}.endTime`"
        :label="`${data.nameTwo}：`"
        :rules="endRules"
      >
        <date-comp v-model="endTime" :data="data"></date-comp>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item
        :prop="`${prop}.interval`"
        :label="`${data.nameThree}(${data.dateType === 3 ? '小时' : '天'})：`"
        :rules="intervalRules"
      >
        <div class="interval">
          {{ value.interval || '自动计算' }}
        </div>
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
  import { str2Date } from '@/util/date';
  import { DATE_AND_HALF_DAY } from '@/const/validator';
  import dateComp from './date-comp';

  const empty = {
    startTime: '',
    endTime: '',
    interval: ''
  };

  export default {
    name: 'DaterangeComp',
    components: { dateComp },
    props: {
      value: {
        type: Object,
        default() {
          return empty;
        }
      },
      prop: {
        type: String,
        default: ''
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        intervalRules: [{ validator: this.validateInterval }]
      };
    },
    computed: {
      startTime: {
        get() {
          return this.value.startTime;
        },
        set(val) {
          let value = { ...this.value };
          value.startTime = val || '';
          this.$emit('input', value);
          this.getInterval();
        }
      },
      endTime: {
        get() {
          return this.value.endTime;
        },
        set(val) {
          let value = { ...this.value };
          value.endTime = val || '';
          this.$emit('input', value);
          this.getInterval();
        }
      },
      startRules() {
        return this.getRules('Start');
      },
      endRules() {
        return this.getRules('End');
      }
    },
    created() {
      // 不加则会报错并校验失败
      if (this.value === empty) {
        this.$emit('input', {
          startTime: '',
          endTime: '',
          interval: ''
        });
      }
    },
    methods: {
      getRules(type) {
        let rules = [];
        if (this.data.required) {
          rules.push({ required: true, message: '请选择' });
        }
        if (this.data.dateType === 2) {
          rules.push(DATE_AND_HALF_DAY);
        }
        rules.push({ validator: this[`validate${type}`] });
        return rules;
      },
      getInterval() {
        this.$nextTick(() => {
          let { startTime = '', endTime = '' } = this.value;
          let interval = '';
          if (startTime && endTime) {
            let unit = 86400000;
            let step = 1;
            if (this.data.dateType === 2) {
              startTime = startTime
                .replace('上午', '00:00:00')
                .replace('下午', '12:00:00');
              endTime = endTime
                .replace('上午', '00:00:00')
                .replace('下午', '12:00:00');
              step = 0.5;
            } else if (this.data.dateType === 3) {
              unit = 3600000;
              step = 0;
            }
            let startDate = str2Date(startTime);
            let endDate = str2Date(endTime);
            interval =
              Math.floor(
                ((endDate.getTime() - startDate.getTime()) / unit + step) * 100
              ) / 100;
            if (interval < 0) {
              interval = 0;
            }
          }
          let value = { ...this.value };
          value.interval = interval + '';
          this.$emit('input', value);
        });
      },
      validateStart(rule, value, callback) {
        if (!this.validate()) {
          callback(
            new Error(`${this.data.nameOne}不能晚于${this.data.nameTwo}`)
          );
          return;
        }
        callback();
      },
      validateEnd(rule, value, callback) {
        if (!this.validate()) {
          callback(
            new Error(`${this.data.nameTwo}不能早于${this.data.nameOne}`)
          );
          return;
        }
        callback();
      },
      validateInterval(rule, value, callback) {
        callback(
          this.data.required && value === 0
            ? new Error(`${this.data.nameThree}必须大于0`)
            : undefined
        );
      },
      validate() {
        let { startTime = '', endTime = '' } = this.value;
        if (!startTime || !endTime) {
          return true;
        }
        if (this.data.dateType === 2) {
          startTime = startTime
            .replace('上午', '00:00:00')
            .replace('下午', '12:00:00');
          endTime = endTime
            .replace('上午', '00:00:00')
            .replace('下午', '12:00:00');
        }
        let startDate = str2Date(startTime);
        let endDate = str2Date(endTime);
        return startDate.getTime() <= endDate.getTime();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .bc-form-build-dateranage {
    .interval {
      line-height: 32.4px;
    }
  }
</style>
