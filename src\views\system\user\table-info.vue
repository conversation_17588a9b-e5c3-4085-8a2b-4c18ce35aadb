<template>
  <el-table
    :data="list"
    style="width: 100%"
    size="small"
    border
    @selection-change="(val) => $emit('selection', val)"
  >
    <el-table-column align="center" type="selection" width="55" />
    <el-table-column align="center" type="index" label="#" />
    <el-table-column v-if="columns[0].visible" prop="account" label="登录账号">
    </el-table-column>
    <el-table-column
      v-if="columns[1].visible"
      prop="tenantName"
      label="所属租户"
      show-overflow-tooltip
    >
      <template slot-scope="{ row }">
        <el-tag class="tag_ellipsis">{{ row.tenantName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column v-if="columns[2].visible" prop="realName" label="用户姓名">
    </el-table-column>
    <el-table-column
      v-if="columns[3].visible"
      prop="roleName"
      label="所属角色"
      show-overflow-tooltip
    >
      <template slot-scope="{ row }">
        <el-tag class="tag_ellipsis">{{ row.roleName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[4].visible"
      prop="deptName"
      label="所属部门"
      show-overflow-tooltip
    >
      <template slot-scope="{ row }">
        <el-tag class="tag_ellipsis">{{ row.deptName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[5].visible"
      prop="userTypeName"
      label="用户平台"
    >
      <template slot-scope="{ row }">
        <el-tag>{{ row.userTypeName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column align="center" label="操作" fixed="right" width="200">
      <template slot-scope="scope">
        <el-button
          size="small"
          type="text"
          icon="el-icon-view"
          v-if="permission.user_view"
          @click="$emit('dispatch', 'view', scope.row)"
          >查看
        </el-button>
        <el-button
          size="small"
          type="text"
          icon="el-icon-edit"
          v-if="permission.user_edit"
          @click="$emit('dispatch', 'edit', scope.row)"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          size="small"
          v-if="permission.user_delete"
          @click="rowDel(scope.row)"
          >删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { remove } from '@/api/system/user';
  import { mapGetters } from 'vuex';

  export default {
    name: 'UserTableInfo',
    props: {
      columns: {
        type: Array,
        default() {
          return [];
        }
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    watch: {
      source: {
        handler(arr) {
          this.list = cloneDeep(arr);
        },
        deep: true
      }
    },
    data() {
      return {
        list: [],
        visited: false
      };
    },
    methods: {
      // 删除
      rowDel({ id }) {
        this.$confirm('确定删除选择的数据吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await remove(id);
          if (res.data.code === 200) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.$emit('dispatch', 'refresh');
          }
        });
      }
    },
    computed: {
      ...mapGetters(['permission'])
    }
  };
</script>

<style lang="scss" scoped>
  .tag_ellipsis {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
