<template>
  <el-form
    ref="form"
    :inline="true"
    :model="form"
    size="small"
    label-suffix="："
  >
    <el-form-item label="打分方式编码" prop="code">
      <el-input
        v-model.trim="form.code"
        placeholder="请输入打分方式编码"
      ></el-input>
    </el-form-item>
    <el-form-item label="打分方式名称" prop="name">
      <el-input v-model.trim="form.name" placeholder="请选择打分方式名称" />
    </el-form-item>
    <el-form-item label="类型" prop="type">
      <el-select v-model="form.type" placeholder="请选择类型">
        <el-option
          v-for="item in serviceDicts.type.score_type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="$emit('query', form)"
        >查询</el-button
      >
    </el-form-item>
    <el-form-item>
      <el-button icon="el-icon-delete" @click="onReset">清空</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
  export default {
    name: 'search',
    serviceDicts: ['score_type'],
    data() {
      return {
        form: {
          code: '',
          name: undefined,
          type: undefined
        }
      };
    },
    mounted() {
      this.$emit('query', this.form);
    },
    methods: {
      onReset() {
        this.$refs.form.resetFields();
        this.$emit('query', this.form);
      }
    }
  };
</script>
