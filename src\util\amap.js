export function loadAMap() {
  return new Promise((resolve, reject) => {
    // 跳过eslint验证
    /* eslint-disable */ 
    if (typeof AMap !== 'undefined') {
      resolve(AMap);
      return true;
    }
    window.onCallback = function () {
      resolve(AMap);
    };
    window._AMapSecurityConfig = {
      securityJsCode: '8b6472a13686a80fba44d0c4f3407231'
      // 例如 securityJsCode:'您申请的安全密钥',
      // 引入地图 JSAPI 脚本之前增加设置 JSAPI 安全密钥的脚本标签，并将您的安全密钥「您申请的安全密钥」替换为您的安全密钥；（注意这个设置必须是在  JSAPI 的脚本加载之前进行设置，否则设置无效。）
    };
    let script = document.createElement('script');
    script.type = 'text/javascript';
    /* eslint-disable-next-line */
    script.src = `https://webapi.amap.com/maps?v=1.4.20&key=779144a1e160e21303ae47cc0531479b&callback=onCallback&plugin=AMap.Geocoder`;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

export function loadAMapUI() {
  return new Promise((resolve, reject) => {
    let AMapUI = window.AMapUI; // 避免报错添加的
    if (typeof AMapUI !== 'undefined') {
      resolve(AMapUI);
      return true;
    }
    let script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = 'https://webapi.amap.com/ui/1.0/main.js';
    script.onload = function () {
      resolve(AMapUI);
    };
    script.onerror = reject;
    document.head.appendChild(script);
  });
}
