import request from '@/router/axios';

/**
 * 新增员工接口
 */
export const addStaff = (row) => {
  return request({
    url: '/api/attila-system/employee',
    method: 'post',
    data: row
  });
};
/**
 * 员工信息个人信息查询
 */
export const staffInfo = (employeeId) => {
  return request({
    url: '/api/attila-system/user-dept/personalInfo',
    method: 'get',
    params: {
      employeeId
    }
  });
};
/**
 * 员工信息个人信息提交
 */
export const staffUpdate = (row) => {
  return request({
    url: '/api/attila-system/user-dept/personalInfo',
    method: 'post',
    data: row
  });
};
/**
 * 员工信息新增履历
 */
export const addRecord = (employeeId, row) => {
  return request({
    url:
      '/api/attila-system/user-dept-addition/record?employeeId=' + employeeId,
    method: 'post',
    data: row
  });
};
/**
 * 员工信息查询履历
 */
export const getRecord = (employeeId, types) => {
  return request({
    url: '/api/attila-system/user-dept-addition/record',
    method: 'get',
    params: {
      employeeId,
      types
    }
  });
};
/**
 * 员工信息上传附件
 */
export const addPhoto = (employeeId, row) => {
  return request({
    url: '/api/attila-system/user-dept/appendFile?employeeId=' + employeeId,
    method: 'post',
    data: row
  });
};
/**
 * 员工信息获取附件
 */
export const getPhoto = (employeeId) => {
  return request({
    url: '/api/attila-system/user-dept/appendFile',
    method: 'get',
    params: {
      employeeId
    }
  });
};

/**
 * 员工信息-查询工资社保
 * @param {String} employeeId 员工ID
 */
export const getPayInfo = (employeeId) => {
  return request({
    url: '/api/attila-system/user-dept/payInfo',
    method: 'get',
    params: { employeeId }
  });
};

/**
 * 员工信息-保存工资社保
 * @param {Object} data 参数
 */
export const savePayInfo = (data) => {
  return request({
    url: '/api/attila-system/user-dept/payInfo',
    method: 'put',
    data
  });
};

/**
 * 查询用户入职时间
 */
export const getEntryTime = () => {
  return request({
    url: '/api/attila-system/user-dept/select-entryTime',
    method: 'get'
  });
};

/**
 * 计算员工转正日期
 * @param {String} entryTime 入职时间
 * @param {number} probation 试用期
 */
export const getRegularTime = (entryTime, probation) => {
  return request({
    url: '/api/attila-system/user-dept/select-regularTime',
    method: 'get',
    params: { entryTime, probation }
  });
};
