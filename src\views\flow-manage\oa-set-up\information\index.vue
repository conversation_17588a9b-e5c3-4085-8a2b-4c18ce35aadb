<template>
  <div class="oaSetUpInformation">
    <el-form ref="form" :rules="rules" :model="form">
      <el-form-item label="图标：">
        <img v-oss :src="form.icon" />
        <span @click="addImgVisible = true" class="changeImg">修改</span>
      </el-form-item>
      <el-form-item label="审批名称：" prop="processName">
        <el-input
          v-model.trim="form.processName"
          placeholder="请输入审批名称，如：报销流程"
          maxlength="20"
          size="small"
        ></el-input>
      </el-form-item>
      <el-form-item label="说明：">
        <el-input
          v-model.trim="form.remark"
          placeholder="请输入"
          maxlength="30"
          size="small"
        ></el-input>
      </el-form-item>
      <el-form-item label="分组：" prop="groupId">
        <h-select
          v-model="form.groupId"
          size="small"
          :data-source="applyList"
          :props="prop"
          placeholder="请选择"
        ></h-select>
      </el-form-item>
      <el-form-item
        label="固定表单："
        v-hasRole="['administrator']"
        prop="isForm"
      >
        <el-radio v-model="form.isForm" :label="1">是</el-radio>
        <el-radio v-model="form.isForm" :label="0">否</el-radio>
      </el-form-item>
      <el-form-item label="发起人范围：" prop="selectRadio">
        <el-radio v-model="form.selectRadio" :label="5">全部成员</el-radio>
        <el-radio v-model="form.selectRadio" :label="2">部分成员</el-radio>
      </el-form-item>
      <el-form-item
        v-if="form.selectRadio === 2"
        label=" "
        prop="ids"
        class="selectPeople"
      >
        <div @click="selectPeople" class="people">
          <span>{{ form.startDetailNames }}</span>
          <i class="el-icon-arrow-down"></i>
        </div>
      </el-form-item>
    </el-form>
    <add-img
      @emit-src="emitSrc"
      :add-img-visible.sync="addImgVisible"
    ></add-img>
    <select-originator
      @rangeSave="rangeSave"
      :range-visible.sync="visible"
      :ids="form.ids"
      :radio="`${form.startScopeType === 5 ? 1 : form.startScopeType}`"
      :is-radio="false"
      title="发起人范围"
    ></select-originator>
  </div>
</template>
<script>
  import addImg from './add-img';
  import selectOriginator from '@/components/select-tree/select-originator';
  import { groupList } from '@/api/flow/process';

  export default {
    components: { addImg, selectOriginator },
    props: {
      informationDetails: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        visible: false,
        addImgVisible: false,
        peopleLabel: '',
        rules: {
          processName: [
            { required: true, message: '请填写审批名称', trigger: 'blur' }
          ],
          groupId: [{ required: true, message: '请选择分组', trigger: 'blur' }],
          isForm: [
            { required: true, message: '请选择是否固定表单', trigger: 'blur' }
          ],
          selectRadio: [{ required: true }],
          ids: [
            {
              required: true,
              message: '请选择发起人范围',
              trigger: 'blur',
              validator(rule, val, callback) {
                if (!val.length) {
                  callback(new Error('请选择发起人范围'));
                } else {
                  callback();
                }
              }
            }
          ]
        },
        form: {
          icon: '/oa/icon_diy_0.png',
          processName: '',
          remark: '',
          groupId: '',
          isForm: 0,
          startScopeType: 5,
          selectRadio: 5,
          ids: [],
          startDetailNames: ''
        },
        applyList: [],
        prop: {
          value: 'id',
          label: 'groupName'
        }
      };
    },
    watch: {
      informationDetails() {
        this.form = {
          icon: this.informationDetails.icon,
          processName: this.informationDetails.processName,
          remark: this.informationDetails.remark,
          groupId: this.informationDetails.groupId,
          isForm:
            this.informationDetails.isForm !== null
              ? this.informationDetails.isForm
              : 0,
          selectRadio: this.informationDetails.startScopeType === 5 ? 5 : 2,
          startScopeType: this.informationDetails.startScopeType,
          ids: this.informationDetails.ids,
          startDetailNames: this.informationDetails.startDetailNames
        };
      },
      'form.selectRadio'() {
        if (this.form.ids.length && this.form.selectRadio === 2) {
          return;
        }
        if (this.form.selectRadio === 2) {
          this.visible = true;
        } else {
          this.peopleLabel = '';
          this.form.ids = [];
          this.form.startScopeType = 5;
          this.form.startDetailNames = '';
        }
      },
      form: {
        handler() {
          if (this.$store.state.oaSetUp.isValidate) {
            this.$nextTick(() => {
              this.$emit('update-error-number', true);
            });
          }
        },
        deep: true
      }
    },
    created() {
      groupList().then((res) => {
        if (res && res.data && res.data.success) {
          this.applyList = res.data.data;
        }
      });
    },
    methods: {
      emitSrc(src) {
        src && (this.form.icon = src);
      },
      selectPeople() {
        this.visible = true;
      },
      rangeSave(v) {
        const { activeTab, list } = v;
        const label =
          activeTab === '4'
            ? 'postName'
            : activeTab === '3'
            ? 'title'
            : activeTab === '2'
            ? 'jobName'
            : 'label';
        this.form.selectRadio = 2;
        this.form.startScopeType = activeTab;
        this.form.startDetailNames = list.map((l) => l[label]).join(' / ');
        this.form.ids = list.map((l) => l.id);
      },
      submit() {
        return this.form;
      },
      validate() {
        let num = 0;
        this.$refs.form.validate((v, group) => {
          num = Object.keys(group).length;
        });
        return num;
      }
    }
  };
</script>
<style lang="scss">
  .oaSetUpInformation {
    display: flex;
    justify-content: center;
    height: calc(100% - 180px) !important;
    height: auto;
    padding-top: 141px;
    background: #fff;

    // margin: 24px;
    border-radius: 2px;

    .el-form {
      width: 674px;

      .el-form-item {
        display: flex;
      }

      .is-error .people {
        border-color: #ff5151 !important;
      }

      .selectPeople .el-form-item__label::before {
        content: '';
      }

      .el-form-item__content {
        display: flex;
        flex: 1;
        align-items: center;
        line-height: 40px;

        .el-radio {
          margin-right: 40px;
        }

        img {
          width: 40px;
          height: 40px;
        }

        .changeImg {
          margin-left: 8px;
          color: #409eff;
          cursor: pointer;
        }

        .people {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 545px;
          height: 32px;
          padding: 0 10px 0 15px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          cursor: pointer;

          span {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          i {
            color: #cfcfcf;
            font-size: 14px;
          }
        }
      }

      .el-form-item__label {
        width: 130px;
      }
    }
  }
</style>
