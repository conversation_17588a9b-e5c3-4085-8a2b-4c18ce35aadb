import request from '@/router/axios';

export const getModelWpsUrl = (category) => {
  return request({
    url: '/api/attila-resource/wps/get-model-wps-url',
    method: 'get',
    params: { category }
  });
};
export const getModelUrl = (id, editType) => {
  return request({
    url: '/api/attila-resource/wps/get-model-url',
    method: 'get',
    params: { id, editType }
  });
};
// 获取预览地址
export const getWpsViewUrl = (downloadUrl) => {
  return request({
    url: '/api/attila-resource/wps/get-wps-url-of-view',
    method: 'post',
    data: { downloadUrl }
  });
};
