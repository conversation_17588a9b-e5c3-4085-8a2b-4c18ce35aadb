<template>
  <div class="post_list">
    <ul>
      <li
        v-for="item in list"
        :key="item.id"
        :class="item.select ? 'active' : ''"
      >
        <div class="post_style" @click="() => handleClick(item)">
          <span>{{ item.postName }}</span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  import { getList } from '@/api/system/post';

  export default {
    name: 'postCom',
    data() {
      return {
        list: []
      };
    },
    props: {
      postIds: {
        type: Array,
        default() {
          return [];
        }
      },
      single: {
        type: Boolean,
        default: false
      }
    },
    mounted() {
      this.getPostListData();
    },
    methods: {
      // 获取岗位数据
      async getPostListData() {
        try {
          const res = await getList(1, 100);
          const data = res.data.data;
          this.list = data.records.map((item) => {
            item.select = false;
            return item;
          });
          // 编辑
          if (this.postIds.length) {
            const temp = [];
            this.list.map((item) => {
              this.postIds.forEach((id) => {
                if (item.id === id) {
                  item.select = true;
                  temp.push(item);
                }
              });
              return item;
            });
            this.$emit('emitPost', temp, true);
          }
        } catch (e) {
          console.error(e);
        }
      },
      handleClick(post) {
        const temp = [];
        if (this.single) {
          this.list.map((item) => {
            item.select = false;
            if (item.id === post.id) {
              item.select = !item.select;
            }
            if (item.select) {
              temp.push(item);
            }
            return item;
          });
        } else {
          this.list.map((item) => {
            if (item.id === post.id) {
              item.select = !item.select;
            }
            if (item.select) {
              temp.push(item);
            }
            return item;
          });
        }
        this.$emit('emitPost', temp);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .post_list {
    ul,
    li {
      margin: 0;
      padding: 0;
      list-style: none;

      .active {
        background-color: #ebebeb;
      }
    }
  }

  .post_style {
    box-sizing: border-box;
    padding: 8px 15px 8px 10px;
    border-bottom: 1px solid #dfdfdf;
    cursor: pointer;

    &:hover {
      background-color: #ebebeb;
    }
  }
</style>
