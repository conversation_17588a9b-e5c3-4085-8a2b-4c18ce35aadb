.approval {
  &.h-block .h-block-title + .h-block-content {
    padding: 12px 0 32px;
  }

  .write-form {
    width: 120px;
    margin-bottom: 18px;

    .iconfont {
      display: flex;
      justify-content: center;
      font-size: 14px;
    }
  }

  .abstract {
    width: 100%;

    & > div {
      max-width: 230px;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;

      .search-keyword {
        color: #fe6b06;
      }
    }
  }

  .formName {
    overflow: hidden;
    color: #3b7cff;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  .cell {
    display: flex;
    align-items: center;
  }

  .icon-status_dot {
    margin-right: 8px;
    font-size: 12px;
    transform: scale(0.5, 0.5);
  }

  .blue {
    color: #3b7cff;
  }

  .green {
    color: #52c41a;
  }

  .red {
    color: #ff5151;
  }

  .gray {
    color: #cfcfcf;
  }

  .orange {
    color: #feb506;
  }
}

.newApproval {
  .h-block-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    ul {
      padding-left: 0;
      list-style: none;
    }

    li {
      &:not(:first-child) {
        margin-top: 56px;
      }
    }

    .header {
      width: 100%;
      margin-bottom: 8px;
      padding-bottom: 15px;
      color: #333;
      font-weight: 600;
      font-size: 16px;
      line-height: 16px;
      border-bottom: 1px solid #e9e9e9;
    }

    .content-container {
      display: grid;
      grid-column-gap: 16px;
      grid-template-columns: repeat(2, 332px);

      .content {
        display: flex;
        box-sizing: border-box;
        margin-top: 16px;
        padding: 20px;
        background-color: #f7f8fa;
        cursor: pointer;

        &:hover {
          background-color: #f0f2f5;
        }

        img {
          width: 40px;
          height: 40px;
          margin-right: 13px;
        }

        h4 {
          width: 200px;
          margin: 0 0 10px;
          overflow: hidden;
          color: #333;
          font-size: 16px;
          line-height: 18px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        span {
          width: 200px;
          overflow: hidden;
          color: #999;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        div {
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}

@media screen and (min-width: 1200px) {
  .newApproval {
    .h-block-content {
      .content-container {
        grid-template-columns: repeat(3, 332px);
      }
    }
  }
}

.download-attila {
  .el-dialog__body {
    padding: 32px 0 41px !important;

    .info {
      font-size: 12px;
    }

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;

      .code-app {
        color: #333;

        img {
          margin-right: 4px;
          vertical-align: bottom;
        }
      }
    }

    #qrCodeUrl {
      margin-top: 32px;
      margin-bottom: 26px;
    }
  }
}

.approval-apply {
  .primary {
    color: #fff;
    background-color: #ff740a;

    &:hover {
      color: #fff;
      background-color: #ff9f31;
    }
  }

  .cancle {
    // margin-right: 10px;
    background-color: #fff;
  }

  .refuse {
    color: #fff;
    background-color: #ff5151;

    &:hover {
      background: #ff6f6f;
    }
  }

  .agree {
    color: #fff;
    background-color: #15bc84;

    &:hover {
      background: #16d696;
    }
  }

  .warningStr {
    margin-left: 144px;
    color: #ff5151;
  }

  .h-block-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    margin-bottom: 20px;

    & > div {
      display: flex;
      flex-wrap: wrap;
      width: 848px;
    }
  }

  .detailLink {
    display: flex;
    justify-content: center;
    margin-top: 19px;
    margin-bottom: 40px;

    span {
      color: #fe6b06;
      cursor: pointer;
    }
  }

  .apply-info {
    position: relative;
    display: block !important;

    .apply-status {
      position: absolute;
      right: 80px;
      z-index: 99999;
      width: 120px;
      height: 102px;
    }

    .share-button {
      position: absolute;
      top: 0;
      right: 0;
      color: #999;
      font-size: 12px;
      cursor: pointer;

      i {
        margin-right: 4px;
      }
    }

    .content {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      & > div {
        &:nth-of-type(2) {
          white-space: normal;
        }
      }
    }
  }

  header {
    width: 100%;
    margin-bottom: 19px;
    padding-bottom: 15px;
    color: #333;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    border-bottom: 1px solid #e9e9e9;
  }

  .content {
    display: flex;
    box-sizing: border-box;
    width: 50%;
    min-height: 64px;
    padding: 21px 0;

    div {
      line-height: 22px;
    }

    & > div:first-child {
      width: 144px;
      overflow: hidden;
      white-space: nowrap;
      text-align: right;
      text-overflow: ellipsis;

      &::after {
        content: '：';
      }
    }

    .noTip::after {
      content: '' !important;
    }
  }

  .attach {
    display: flex;
    box-sizing: border-box;
    width: 100%;
    min-height: 64px;
    padding: 21px 0;

    & > div:first-child {
      justify-content: flex-end;
      width: 144px;
      text-align: right;
    }

    .fileUrl {
      flex: 1;
    }
  }

  .footer-btn {
    display: flex;
    justify-content: center;
    margin-left: -10px;
    padding-top: 24px;
    padding-bottom: 30px;

    button {
      width: 140px;
      margin-left: 10px;
    }
  }
}

.more-btn {
  padding: 2px 0 !important;

  .btn-cell {
    height: 32px;
    color: #333;
    font-size: 14px;
    line-height: 32px;
    cursor: pointer;

    &:hover {
      color: #409eff;
    }

    i {
      padding-right: 12px;
      padding-left: 16px;
    }
  }
}

.apply-confirm {
  .el-button {
    width: 140px;
  }

  .h-upload {
    margin-top: 12px;
  }

  .el-button--small {
    width: 100px;
  }

  .fileTip {
    margin-left: 8px;
    color: #cfcfcf;
  }

  textarea {
    height: 132px;
    padding: 6px 12px;
  }

  .apply-confirm-context-box {
    max-height: 434px;
    overflow: auto;
  }
}
