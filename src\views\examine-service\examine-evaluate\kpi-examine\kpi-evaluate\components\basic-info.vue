<template>
  <div class="info_form_mb5">
    <el-form label-width="110px" label-suffix="：" size="small">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="评价单单号">
            {{ form.code || '--' }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="考核周期">
            {{ form.periodItemName || '--' }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="截止日期">
            {{ form.deadline || '--' }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="接收人">
            {{ form.assessorName || '--' }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联评价方案">
            {{ form.schemeCode || '--' }}</el-form-item
          >
        </el-col>
        <el-col v-if="isDetail" :span="8">
          <el-form-item label="关联绩效单">
            {{ form.correlatedResultCode || '--' }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据状态">
            {{ form.status | changeStatus }}</el-form-item
          >
        </el-col>
        <el-col :span="8" v-if="isDetail">
          <el-form-item label="评价日期">
            {{ form.evaluationDate | formatDate }}</el-form-item
          >
        </el-col>
        <el-col :span="8" v-else>
          <el-form-item label="评价日期">
            <el-date-picker
              type="date"
              v-model="cEvaluationDate"
              value-format="yyyy-MM-dd"
              placeholder="选择评价日期"
              style="width: 200px"
              :editable="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="isDetail ? 24 : 16">
          <el-form-item label="备注"> {{ form.comment || '--' }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'basic-info',
    props: {
      isDetail: {
        type: Boolean,
        default: false
      },
      form: {
        type: Object,
        default() {
          return {};
        }
      },
      evaluationDate: {
        type: String,
        require: true
      }
    },
    model: {
      prop: 'evaluationDate',
      event: 'change'
    },
    data() {
      return {
        a: undefined
      };
    },
    computed: {
      cEvaluationDate: {
        get() {
          return this.evaluationDate;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-form-item {
    margin-bottom: 0 !important;
  }
</style>
