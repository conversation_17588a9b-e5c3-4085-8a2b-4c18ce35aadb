export default [
  // 云盾内容 勿删
  {
    name: '云盾',
    children: [
      {
        name: '上会申请',
        type: 'yd-shsq',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'decisionName',
            valueJson: {
              name: '业务名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'date',
            id: 'conferenceTime',
            valueJson: {
              name: '会议时间',
              placeholder: '请选择',
              required: false,
              dateType: 3
            }
          },
          {
            type: 'input',
            id: 'conferenceVenue',
            valueJson: {
              name: '会议地点',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientName',
            valueJson: {
              name: '客户名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          }
        ]
      },
      {
        name: '盘点管理',
        type: 'yd-pdgl',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'storeName',
            valueJson: {
              name: '仓库名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'textarea',
            id: 'reason',
            valueJson: {
              name: '盘点原因',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          },
          {
            type: 'input',
            id: 'gainLoss',
            valueJson: {
              name: '盈亏状态',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'differQuantity',
            valueJson: {
              name: '差异量',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          }
        ]
      },
      {
        name: '合同',
        type: 'yd-ht',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractTypeName',
            valueJson: {
              name: '合同类型',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractNum',
            valueJson: {
              name: '合同编号',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'decisionName',
            valueJson: {
              name: '业务决策',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientName',
            valueJson: {
              name: '物流公司/供应商/客户',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'date',
            id: 'signDate',
            valueJson: {
              name: '签订日期',
              placeholder: '请选择',
              required: false,
              dateType: 1
            }
          },
          {
            type: 'date',
            id: 'performanceDate',
            valueJson: {
              name: '履行期限',
              placeholder: '请选择',
              required: false,
              dateType: 1
            }
          },
          {
            type: 'inputNumber',
            id: 'remitDate',
            valueJson: {
              name: '汇款时限',
              placeholder: '请输入',
              unit: '天',
              required: false,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'isStandard',
            valueJson: {
              name: '是否为标准合同',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'settlePlace',
            valueJson: {
              name: '纠纷解决地',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleName',
            valueJson: {
              name: '责任人',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleOrgName',
            valueJson: {
              name: '责任部门',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'totalAmount',
            valueJson: {
              name: '合同总额',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'input',
            id: 'tradeWayName',
            valueJson: {
              name: '结算方式',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          }
        ]
      },
      {
        name: '非标准合同',
        type: 'yd-fbzht',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractTypeName',
            valueJson: {
              name: '合同类型',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractNum',
            valueJson: {
              name: '合同编号',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'decisionName',
            valueJson: {
              name: '业务决策',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientName',
            valueJson: {
              name: '物流公司/供应商/客户',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'date',
            id: 'signDate',
            valueJson: {
              name: '签订日期',
              placeholder: '请选择',
              required: false,
              dateType: 1
            }
          },
          {
            type: 'date',
            id: 'performanceDate',
            valueJson: {
              name: '履行期限',
              placeholder: '请选择',
              required: false,
              dateType: 1
            }
          },
          {
            type: 'inputNumber',
            id: 'remitDate',
            valueJson: {
              name: '汇款时限',
              placeholder: '请输入',
              unit: '天',
              required: false,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'isStandard',
            valueJson: {
              name: '是否为标准合同',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'settlePlace',
            valueJson: {
              name: '纠纷解决地',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleName',
            valueJson: {
              name: '责任人',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleOrgName',
            valueJson: {
              name: '责任部门',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'totalAmount',
            valueJson: {
              name: '合同总额',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'input',
            id: 'tradeWayName',
            valueJson: {
              name: '结算方式',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          }
        ]
      },
      {
        name: '业务决策',
        type: 'yd-ywjc',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'decisionName',
            valueJson: {
              name: '决策名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'supplierName',
            valueJson: {
              name: '供应商',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientName',
            valueJson: {
              name: '客户名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'transporterName',
            valueJson: {
              name: '运输单位',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'cooperativeModeName',
            valueJson: {
              name: '合作模式',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'cooperativeTime',
            valueJson: {
              name: '合作时间',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleName',
            valueJson: {
              name: '责任人',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleOrgName',
            valueJson: {
              name: '责任部门',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'advanceAmount',
            valueJson: {
              name: '授信额度-预付',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'creditAmount',
            valueJson: {
              name: '授信额度-赊销',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'totalQuantity',
            valueJson: {
              name: '总合作量',
              placeholder: '请输入',
              unit: '吨',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'monthlyAverage',
            valueJson: {
              name: '月均量',
              placeholder: '请输入',
              unit: '吨',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'profit',
            valueJson: {
              name: '利润空间',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'capitalUse',
            valueJson: {
              name: '资金占用',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'cycleTime',
            valueJson: {
              name: '周转期',
              placeholder: '请输入',
              unit: '天',
              required: false,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          }
        ]
      },
      {
        name: '授信',
        type: 'yd-sx',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'score',
            valueJson: {
              name: '评定得分',
              placeholder: '请输入',
              unit: '',
              required: false,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'input',
            id: 'grade',
            valueJson: {
              name: '信用等级',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'advanceAmount',
            valueJson: {
              name: '授信额度（预付账款）',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'creditAmount',
            valueJson: {
              name: '授信额度（赊销账款）',
              placeholder: '请输入',
              unit: '吨',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'timeLimit',
            valueJson: {
              name: '授信期限',
              placeholder: '请输入',
              unit: '天',
              required: false,
              decimal: false,
              decimalLength: 0
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          }
        ]
      },
      {
        name: '收款',
        type: 'yd-sk',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'receiptTypeName',
            valueJson: {
              name: '收款类型',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'decisionName',
            valueJson: {
              name: '业务决策',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractNum',
            valueJson: {
              name: '合同编号',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientName',
            valueJson: {
              name: '企业名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'receiptMethodName',
            valueJson: {
              name: '收款方式',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'totalAmount',
            valueJson: {
              name: '收款金额',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'input',
            id: 'clientAccount',
            valueJson: {
              name: '收款账户',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientBank',
            valueJson: {
              name: '开户行',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleName',
            valueJson: {
              name: '责任人',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleOrgName',
            valueJson: {
              name: '责任部门',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          }
        ]
      },
      {
        name: '付款',
        type: 'yd-fk',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'paymentTypeName',
            valueJson: {
              name: '付款类型',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'decisionName',
            valueJson: {
              name: '业务决策',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractNum',
            valueJson: {
              name: '合同编号',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientName',
            valueJson: {
              name: '企业名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'paymentMethodName',
            valueJson: {
              name: '付款方式',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'totalAmount',
            valueJson: {
              name: '付款总额',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'date',
            id: 'paymentDate',
            valueJson: {
              name: '付款日期',
              placeholder: '请选择',
              required: false,
              dateType: 1
            }
          },
          {
            type: 'input',
            id: 'filingStatus',
            valueJson: {
              name: '合同归档',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'payAccount',
            valueJson: {
              name: '付款账户',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'payBank',
            valueJson: {
              name: '付款开户行',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'receiptAccount',
            valueJson: {
              name: '收款账户',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'receiptBank',
            valueJson: {
              name: '收款开户行',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleName',
            valueJson: {
              name: '责任人',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleOrgName',
            valueJson: {
              name: '责任部门',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'textarea',
            id: 'paymentCause',
            valueJson: {
              name: '付款事由',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          }
        ]
      },
      {
        name: '发票',
        type: 'yd-fp',
        children: [
          {
            type: 'input',
            id: 'id',
            valueJson: {
              name: '业务ID',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'invoiceTypeName',
            valueJson: {
              name: '发票类型',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'contractNum',
            valueJson: {
              name: '合同编号',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'clientName',
            valueJson: {
              name: '企业名称',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'inputNumber',
            id: 'amount',
            valueJson: {
              name: '开票金额',
              placeholder: '请输入',
              unit: '元',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'quantity',
            valueJson: {
              name: '开票吨数',
              placeholder: '请输入',
              unit: '吨',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'inputNumber',
            id: 'unitPrice',
            valueJson: {
              name: '开票单价',
              placeholder: '请输入',
              unit: '元/吨',
              required: false,
              decimal: true,
              decimalLength: 2
            }
          },
          {
            type: 'input',
            id: 'rate',
            valueJson: {
              name: '税率',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleName',
            valueJson: {
              name: '责任人',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'input',
            id: 'responsibleOrgName',
            valueJson: {
              name: '责任部门',
              placeholder: '请输入',
              maxLength: 30,
              required: false,
              scanCode: false
            }
          },
          {
            type: 'file',
            id: 'fileUrl',
            valueJson: {
              name: '附件',
              required: false
            }
          },
          {
            type: 'textarea',
            id: 'remark',
            valueJson: {
              name: '备注',
              placeholder: '请输入',
              maxLength: 500,
              required: false
            }
          }
        ]
      }
    ]
  }
];
