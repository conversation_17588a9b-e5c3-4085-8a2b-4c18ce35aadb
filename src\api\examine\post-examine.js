import request from '@/router/axios';

// 分页查询
export function getPageList(params) {
  return request({
    url: '/api/examine/evaluation-form/page',
    method: 'get',
    params
  });
}

// 岗位业绩评价单详情
export function getDetail(params) {
  return request({
    url: '/api/examine/evaluation-form/post-performance-detail',
    method: 'get',
    params
  });
}

// 保存岗位业绩评价
export function postSave(data) {
  return request({
    url: '/api/examine/evaluation-form/post-performance-save',
    method: 'post',
    data
  });
}

// 提交岗位业绩评价
export function postSubmit(data) {
  return request({
    url: '/api/examine/evaluation-form/post-performance-submit',
    method: 'post',
    data
  });
}

// 删除行
export function deleteItem(params) {
  return request({
    url: '/api/examine/evaluation-form/post-performance-delete-user',
    method: 'get',
    params
  });
}

// 岗位业绩增加被评价人
export function addItem(data) {
  return request({
    url: '/api/examine/evaluation-form/post-performance-add-user',
    method: 'post',
    data
  });
}

// 根据字典key获取对应最新发布流程定义id
export function getProcessId(params) {
  return request({
    url: '/api/pc/process-definition/getIdByKey',
    method: 'get',
    params
  });
}

// 审批（同意-拒绝）岗位业绩评价单
export function postApprove(data) {
  return request({
    url: '/api/examine/evaluation-form/post-performance-approve',
    method: 'post',
    data
  });
}

// 撤销岗位业绩评价的审批流程
export function getRevocation(params) {
  return request({
    url: '/api/examine/evaluation-form/post-performance-revocation',
    method: 'get',
    params
  });
}
