<template>
  <el-form
    ref="form"
    :model="hValue"
    :rules="formRules"
    :label-width="labelWidth"
    :disabled="disabled"
    :validate-on-rule-change="false"
    size="small"
    class="bc-form-build"
  >
    <el-row :gutter="50">
      <template v-for="item in componentList">
        <input-money-comp
          v-if="item.type === 'inputMoney'"
          :key="item.id"
          v-model="hValue[item.id]"
          :prop="item.id"
          :data="item.valueJson"
        />
        <daterange-comp
          v-else-if="item.type === 'daterange'"
          :key="item.id"
          v-model="hValue[item.id]"
          :prop="item.id"
          :data="item.valueJson"
        />
        <region-comp
          v-else-if="item.type === 'region'"
          :key="item.id"
          v-model="hValue[item.id]"
          :prop="item.id"
          :data="item.valueJson"
        />
        <component
          :is="`${item.type}Comp`"
          v-else-if="groupTypes.includes(item.type)"
          ref="group"
          :key="item.id"
          v-model="hValue"
          :data="item.valueJson || {}"
          :component-list.sync="item.children"
          :disabled="disabled"
          :accept="accept"
        />
        <el-col
          v-else
          :key="item.id"
          :span="rowTypes.includes(item.type) ? 24 : 12"
        >
          <el-form-item
            :prop="item.id"
            :label="getComponentLabel(item)"
            :class="{ 'image-item': item.type === 'image' }"
          >
            <form-comp
              v-if="item.type === 'form'"
              ref="formComp"
              v-model="hValue[item.id]"
              :data="item.valueJson"
              :disabled="disabled"
              :component-list="item.children"
              :accept="accept"
              :total-value="value"
            />
            <file-comp
              v-else-if="item.type === 'file'"
              v-model="hValue[item.id]"
              v-loading="loading[item.id] || false"
              :data="item.valueJson || {}"
              :form="hValue"
              :disabled="disabled"
              :props="selectProps"
              :accept="accept"
            >
              <span slot="button" :disabled="disabled">选择附件</span>
            </file-comp>
            <component
              :is="`${item.type}Comp`"
              v-else
              v-model="hValue[item.id]"
              v-loading="loading[item.id] || false"
              :data="item.valueJson || {}"
              :form="hValue"
              :disabled="disabled"
              :props="selectProps"
              :total-value="totalValue"
            />
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>

<script>
  import {
    ROW_TYPES,
    NO_RULE_TYPES,
    INPUT_RULE_TYPES,
    GROUP_TYPES
  } from './const';
  import {
    ID_CARD_NO_IMPORT,
    PHONE,
    TELEPHONE,
    MOBILE_PHONE,
    NO_ALL_SPACE,
    DATE_AND_HALF_DAY
  } from '@/const/validator';

  import inputComp from './items/input-comp';
  import textareaComp from './items/textarea-comp';
  import tipsComp from './items/tip-comp';
  import inputNumberComp from './items/input-number-comp';
  import inputMoneyComp from './items/input-money-comp';
  import computedComp from './items/computed-comp';
  import radioComp from './items/radio-comp';
  import checkboxComp from './items/checkbox-comp';
  import dateComp from './items/date-comp';
  import daterangeComp from './items/daterange-comp';
  import imageComp from './items/image-comp';
  import fileComp from './items/file-comp';
  import idcardComp from './items/idcard-comp';
  import phoneComp from './items/phone-comp';
  import peopleComp from './items/people-comp';
  import deptComp from './items/dept-comp';
  import areaComp from './items/area-comp';
  import regionComp from './items/region-comp';
  import relationApplyComp from './items/relation-apply-comp';
  import formComp from './items/form-comp';
  import textComp from './items/text-comp';
  import descComp from './items/desc-comp';
  import restComp from './groups/rest-comp';
  import replacecardComp from './groups/replacecard-comp';
  import workComp from './groups/work-comp';
  import outComp from './groups/out-comp';
  import tripComp from './groups/trip-comp';
  import turnFormalComp from './groups/turn-formal-comp';
  import leaveComp from './groups/leave-comp';
  import htToXdComp from './groups/ht-to-xd-comp';
  import htToHtComp from './groups/ht-to-xd-comp';
  import htToGpComp from './groups/ht-to-xd-comp';
  import htToJmgp1Comp from './groups/ht-to-xd-comp';
  import htToJmgp2Comp from './groups/ht-to-xd-comp';
  import htToJmbm1Comp from './groups/ht-to-xd-comp';
  import htToJmbm2Comp from './groups/ht-to-xd-comp';

  export default {
    name: 'CFormBuild',
    components: {
      inputComp,
      textareaComp,
      tipsComp,
      inputNumberComp,
      inputMoneyComp,
      computedComp,
      radioComp,
      checkboxComp,
      dateComp,
      daterangeComp,
      imageComp,
      fileComp,
      idcardComp,
      phoneComp,
      peopleComp,
      deptComp,
      areaComp,
      regionComp,
      relationApplyComp,
      formComp,
      textComp,
      descComp,
      restComp,
      replacecardComp,
      workComp,
      outComp,
      tripComp,
      turnFormalComp,
      leaveComp,
      htToXdComp,
      htToHtComp,
      htToGpComp,
      htToJmgp1Comp,
      htToJmgp2Comp,
      htToJmbm1Comp,
      htToJmbm2Comp
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      labelWidth: {
        type: String,
        default: '160px'
      },
      disabled: {
        type: Boolean,
        default: false
      },
      rules: Object,
      selectProps: Object,
      loading: {
        type: Object,
        default() {
          return {};
        }
      },
      accept: String,
      totalValue: Object
    },
    data() {
      return {
        rowTypes: ROW_TYPES,
        noRuleTypes: NO_RULE_TYPES,
        inputRuleTypes: INPUT_RULE_TYPES,
        groupTypes: GROUP_TYPES,
        formRules: {}
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      componentList() {
        this.init();
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        this.formRules = this.getRules(this.componentList);
      },
      getRules(list) {
        let rules = {};
        list.forEach((item) => {
          if (this.noRuleTypes.includes(item.type)) {
            return;
          }
          let rule = [];
          if (item.valueJson.required) {
            if (this.inputRuleTypes.includes(item.type)) {
              rule = [
                { required: true, message: '请输入', trigget: 'blur' },
                NO_ALL_SPACE
              ];
            } else if (['image', 'file'].includes(item.type)) {
              rule.push({ type: 'array', required: true, message: '请上传' });
            } else if ('computed' === item.type) {
              rule.push({
                required: true,
                validator: this.validateComputed,
                trigget: 'blur'
              });
            } else {
              rule.push({ required: true, message: '请选择' });
            }
          }
          if ('date' === item.type && item.valueJson.dateType === 2) {
            rule.push(DATE_AND_HALF_DAY);
          } else if ('idcard' === item.type) {
            rule.push(ID_CARD_NO_IMPORT);
          } else if ('phone' === item.type) {
            switch (item.valueJson.phoneType) {
              case 1:
                rule.push(PHONE);
                break;
              case 2:
                rule.push(TELEPHONE);
                break;
              case 3:
                rule.push(MOBILE_PHONE);
                break;
            }
          }
          if (item.type === 'region') {
            rules[`${item.id}.provinceCode`] = rule;
            rules[`${item.id}.street`] = [
              { required: true, message: '请输入', trigget: 'blur' },
              NO_ALL_SPACE
            ];
          } else if (item.type === 'inputMoney') {
            rules[`${item.id}.value`] = rule;
          } else {
            rules[item.id] = rule;
          }
        });
        if (this.rules) {
          rules = {
            ...rules,
            ...this.rules
          };
        }
        return rules;
      },
      getComponentLabel(data) {
        if (['tips', 'desc'].includes(data.type)) {
          return ' ';
        }
        let { name, unit } = data.valueJson;
        if (['inputNumber', 'text'].includes(data.type) && unit) {
          return `${name}(${unit})：`;
        }
        return `${name}：`;
      },
      validateComputed(rule, value, callback) {
        callback(
          isNaN(parseInt(value, 10))
            ? new Error('编辑的计算公式为空或不符合计算法则，无法计算')
            : ''
        );
      },
      validate() {
        return new Promise((resolve, reject) => {
          let list = [];
          if (this.$refs.formComp) {
            list = list.concat(this.$refs.formComp);
          }
          if (this.$refs.group) {
            list = list.concat(this.$refs.group);
          }
          Promise.all([
            this.validateForm(),
            ...Array.from(list, (item) => item.validate())
          ])
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      },
      validateForm() {
        return new Promise((resolve, reject) => {
          this.$refs.form.validate((valid) => {
            valid ? resolve() : reject();
          });
        });
      },
      validateField(props, callback) {
        this.$refs.form.validateField(props, callback);
      },
      clearValidate(props) {
        this.$refs.form.clearValidate(props);
      }
    }
  };
</script>

<style lang="scss">
  @import '@/styles/element-ui';

  .bc-form-build {
    &.el-form {
      .el-form-item {
        &.image-item {
          .el-form-item__content {
            line-height: 1;
          }
        }
      }
    }

    .bc-form-build {
      margin: 0 25px;

      .el-row {
        position: unset;
      }
    }
  }
</style>
