// 获取流程定义ID, 字典编号
export const service_code = 'biz_process_definition';

//  获取流程定义ID，字典类型
export const service_process = {
  kpi_evaluate: 'kpi_filling_process_definition', // kpi指标
  post_evaluate: 'post_performance_process_definition', // 岗位业绩
  economy_kip_result: 'economy_kpi_result_process_definition', // 经济效益+kip
  depart_result: 'depart_performance_result_process_definition', // 部门业绩
  post_result: 'post_performance_result_process_definition' // 岗位业绩
};

/**
 * scheme_type：方案类型
 *   economic： 经济效益指标
 *   kpi: kpi指标
 *   depart: 部门建设
 *   post: 岗位业绩
 *   workEfficiency: 工作效能
 * */
export const scheme_type = {
  economic: 1,
  kpi: 2,
  depart: 3,
  post: 4,
  workEfficiency: 5
};

// 绩效结果单类型
/**
 * scheme_result_type：绩效结果单类型
 *   econ_kpi： 经济效益指标 & kpi指标 & 工作效能
 *   depart: 部门业绩结果
 *   post: 岗位业绩结果
 * */
export const scheme_result_type = {
  econ_kpi: 1,
  depart: 2,
  post: 3
};
