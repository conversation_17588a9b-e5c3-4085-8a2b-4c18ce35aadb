<template>
  <el-table
    border
    :data="list"
    size="small"
    style="width: 100%"
    v-loading="loading"
    element-loading-text="请选择适合分制"
  >
    <el-table-column type="index" label="序号" align="center" width="60">
    </el-table-column>
    <el-table-column prop="itemName" label="名称" align="center">
      <template slot-scope="scope">
        <div v-if="detail">{{ scope.row.itemName }}</div>
        <div v-else>
          <el-input
            type="text"
            size="mini"
            v-model="scope.row.itemName"
            placeholder="请输入名称"
            @change="syncDataFn"
          />
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="itemScore" label="分值" width="300" align="center">
      <template slot-scope="scope">
        <div v-if="detail">{{ scope.row.itemScore }}</div>
        <div v-else>
          <el-input-number
            v-if="scoreMax"
            size="mini"
            v-model="scope.row.itemScore"
            placeholder="请输入分值"
            controls-position="right"
            :min="0"
            :max="scoreMax"
            @change="syncDataFn"
            style="width: 100%"
          />
          <el-input-number
            v-else
            size="mini"
            v-model.trim="scope.row.itemScore"
            placeholder="请输入分值"
            controls-position="right"
            @change="syncDataFn"
            style="width: 100%"
          />
        </div>
      </template>
    </el-table-column>
    <el-table-column label="是否默认" width="100" align="center">
      <template slot-scope="scope">
        <div v-if="detail">{{ scope.row.isDefault ? '是' : '否' }}</div>
        <div v-else>
          <el-switch
            v-model="scope.row.isDefault"
            @change="(bool) => isDefault(scope.$index, bool)"
            :active-value="1"
            :inactive-value="0"
          />
        </div>
      </template>
    </el-table-column>
    <el-table-column label="占比" align="center" width="280px">
      <template slot-scope="scope">
        <div v-if="detail">
          {{ scope.row.ratioOperator | ratioFhFilter
          }}{{ scope.row.ratio | ratioFilter }}
        </div>
        <div v-else>
          <el-select
            v-model="scope.row.ratioOperator"
            placeholder="请选择"
            size="mini"
            style="width: 100px"
            @change="syncDataFn"
          >
            <el-option
              v-for="item in serviceDicts.type.ratio_operator"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            size="mini"
            type="text"
            v-model="scope.row.ratio"
            placeholder="请输入占比"
            style="width: 150px"
            @change="(val) => formatNum(val, scope.row)"
          >
            <template slot="append">%</template>
          </el-input>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { mapState, mapMutations } from 'vuex';
  export default {
    name: 'content-table',
    serviceDicts: ['ratio_operator'],
    props: {
      source: {
        type: Array,
        default() {
          return [];
        }
      },
      detail: {
        type: Boolean,
        default: false
      },
      loading: {
        type: Boolean,
        default: false
      },
      scoreMax: {
        type: Number,
        default: 0
      }
    },
    computed: {
      ...mapState({
        toSource: (state) => state.scoringMethod.source
      })
    },
    data() {
      return {
        list: [],
        itemScoreMin: 0,
        itemScoreMax: 100
      };
    },
    watch: {
      source: {
        handler(arr) {
          this.list = cloneDeep(arr);
        },
        deep: true
      },
      toSource: {
        handler(arr) {
          if (!this.detail) {
            this.list = cloneDeep(arr);
          }
        },
        deep: true
      }
    },
    filters: {
      ratioFhFilter(type) {
        switch (type) {
          case '1':
            return '大于';
          case '2':
            return '大于等于';
          case '3':
            return '等于';
          case '4':
            return '小于等于';
          case '5':
            return '小于';
          default:
            return '';
        }
      },
      ratioFilter(num) {
        if (num) {
          return num + '%';
        }
      }
    },
    methods: {
      ...mapMutations(['SET_SOURCE']),
      syncDataFn() {
        this.SET_SOURCE(this.list);
      },
      isDefault(index, bool) {
        this.list.map((item, idx) => {
          item.isDefault = 0;
          if (idx === index) {
            item.isDefault = bool;
          }
          return item;
        });
        this.SET_SOURCE(this.list);
      },
      formatNum(val, row) {
        let _temp = Number(val);
        console.log(_temp);
        if (isNaN(_temp)) {
          row.ratio = 0;
        } else if (_temp < 0) {
          row.ratio = 0;
        } else if (_temp > 100) {
          row.ratio = 100;
        } else {
          row.ratio = _temp;
        }
        this.SET_SOURCE(this.list);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .handleClick {
    cursor: pointer;
  }
</style>
