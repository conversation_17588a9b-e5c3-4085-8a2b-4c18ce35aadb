<template>
  <el-drawer
    :before-close="cancel"
    :visible.sync="visible"
    append-to-body
    size="528px"
    custom-class="oa-approver"
    :show-close="false"
    :wrapper-closable="false"
  >
    <div v-if="!value.isCC" class="oa-approver-overflow">
      <div class="oa-approver-drawer">
        <el-tabs v-model="tab" class="oa-approver-drawer-tabs">
          <el-tab-pane label="设置审批人" name="1">
            <el-form
              ref="form"
              class="oa-approver-drawer-form"
              size="small"
              label-position="top"
            >
              <el-form-item label="审批人设置">
                <!-- 审批人 -->
                <template v-if="isRefrush">
                  <div
                    v-for="(item, index) in properties.approverPerson"
                    :key="index"
                  >
                    <setting-approvel-person
                      @get-one-person="getOnePerson"
                      @delete-approvel="deleteApprovelPerson"
                      :data="item"
                      :isNullPorperties="properties.isNull"
                      :data-length="properties.approverPerson.length"
                      :index="index"
                    ></setting-approvel-person>
                  </div>
                </template>
                <div v-show="isShowAdd" class="oa-add-button">
                  <div @click="addApprovelPerson" class="oa-add-button-content">
                    <i class="el-icon-plus"></i>
                    <span>添加审批人</span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item
                v-show="isShowType"
                label="审批方式（多人审批时采用）"
              >
                <div class="oa-approvel-type">
                  <h-radio-group
                    v-model="properties.approverType"
                    :data-source="approverTypeList"
                  ></h-radio-group>
                </div>
              </el-form-item>
              <el-form-item v-show="isShowNull" label="审批为空时">
                <div class="oa-approvel-null">
                  <h-radio-group
                    v-model="properties.isNull.type"
                    @change="changeNullRadio"
                    :data-source="approverNullList"
                  ></h-radio-group>
                </div>
              </el-form-item>
              <el-form-item
                v-if="isShowNull && properties.isNull.type === 2"
                label="指定成员"
              >
                <div class="oa-null-staff-list">
                  <i @click="chooseStaff('staff')" class="el-icon-plus"></i>
                  <el-tag
                    v-if="isNullStaff.id"
                    @close="handleDelete(isNullStaff.id)"
                    type="info"
                    size="medium"
                    class="oa-null-staff-tag"
                    closable
                  >
                    成员：{{ isNullStaff.name }}
                  </el-tag>
                </div>
              </el-form-item>
              <div class="oa-approvel-save-button">
                <el-button @click="cancel" size="small">返回</el-button>
                <el-button @click="onSubmit" size="small" type="primary"
                  >保存</el-button
                >
              </div>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="表单操作权限" name="2">
            <el-form size="small" label-width="160px" label-position="left">
              <el-form-item
                v-for="(value, key) in status"
                :key="key"
                :label="getFieldLabel(key)"
              >
                <div
                  slot="label"
                  :title="getFieldLabel(key)"
                  class="field-label"
                >
                  {{ getFieldLabel(key) }}
                </div>
                <h-radio-group
                  v-model="status[key]"
                  :data-source="getFieldStatusList(key)"
                />
              </el-form-item>
            </el-form>
            <div class="oa-approvel-save-button">
              <el-button @click="cancel" size="small">返回</el-button>
              <el-button @click="onFieldSubmit" size="small" type="primary">
                保存
              </el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <section v-else>
      <div class="oa-approver-overflow">
        <div class="oa-approver-drawer">
          <div class="oa-approver-drawer-title">抄送</div>
          <el-form
            ref="form2"
            class="oa-approver-drawer-form"
            size="small"
            label-position="top"
          >
            <el-form-item label="添加抄送人">
              <div class="oa-null-staff-list">
                <i @click="chooseCopyStaff" class="el-icon-plus"></i>
                <el-tooltip
                  v-for="(tag, i) in copyStaffList"
                  :key="i"
                  class="item"
                  effect="dark"
                  :content="tag.orgName"
                  placement="top"
                  :disabled="!tag.orgId || tag.orgId === orgId"
                >
                  <el-tag
                    @close="handleDeleteCopyStaff(i)"
                    type="info"
                    size="medium"
                    class="oa-null-staff-tag"
                    closable
                  >
                    <i
                      v-if="tag.orgId && tag.orgId !== orgId"
                      class="el-icon-link"
                    />
                    {{
                      tag.type === 'dept'
                        ? '部门：'
                        : tag.type === 'position'
                        ? '岗位：'
                        : tag.type === 'job'
                        ? '职务：'
                        : '成员：'
                    }}{{
                      tag.label || tag.title || tag.postName || tag.jobName
                    }}
                  </el-tag>
                </el-tooltip>
              </div>
            </el-form-item>
            <div class="oa-approvel-save-button">
              <el-button @click="cancel" size="small">返回</el-button>
              <el-button @click="onCopySubmit" size="small" type="primary"
                >保存</el-button
              >
            </div>
          </el-form>
        </div>
      </div>
    </section>
    <select-staff-tree
      @rangeSave="getStaffData"
      :range-visible.sync="dialogVisible"
      title="选择成员"
      radio="1"
      :ids="staffList"
    ></select-staff-tree>
    <multiple-staff-with-link-org
      v-if="chooseCopyVisible"
      @range-save="getCheckList"
      :range-visible.sync="chooseCopyVisible"
      :max-select-length="20 - copyStaffList.length"
    ></multiple-staff-with-link-org>
  </el-drawer>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { deepClone } from '@/util/util.js';
  import arrDesgin from '@/util/approvel-desgin';
  import multipleStaffWithLinkOrg from '../components/multiple-staff-with-link-org';
  import settingApprovelPerson from './setting-approvel';
  import selectStaffTree from '@/components/select-tree/select-people';

  const APPROVER_PERSON = {
    type: 1,
    dataId: '',
    scopeIds: [],
    startLevelType: '',
    level: '',
    scopes: '2',
    scopesType: '1'
  };

  export default {
    components: {
      selectStaffTree,
      settingApprovelPerson,
      multipleStaffWithLinkOrg
    },
    props: {
      name: {
        type: String,
        default: ''
      },
      visible: {
        type: Boolean,
        default: false
      },
      value: {
        type: Object,
        default: () => {}
      },
      fieldStatus: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        appprovelTitle: '审批',
        properties: {
          approverPerson: [{ ...APPROVER_PERSON }],
          approverType: 1, // 审批方式
          isNull: {
            type: 1, // 审批为空时
            dataId: {}
          }
        },
        initValues: {},
        isNullStaff: {},
        isNullAdmin: '',
        approverTypeList: [
          { label: '会签（需所有审批人同意）', value: 1 },
          { label: '或签（一人同意或拒绝即可）', value: 2 }
        ],
        approverNullList: [
          { label: '自动通过', value: 1 },
          { label: '指定人员', value: 2 }
          // { label: "转交给审批管理员", value: 3 },
        ],
        staffList: [],
        dialogVisible: false,
        chooseCopyVisible: false,
        isShowAdd: true,
        // nullType: "",
        isShowType: true,
        isRefrush: true,
        copyStaffList: [],
        isShowNull: true,
        tab: '1',
        status: {}
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      orgId() {
        return this.userInfo.activeCompanyId;
      },
      formDesignList() {
        return this.$store.state.oaSetUp.formDesignList;
      }
    },
    watch: {
      visible: {
        handler() {
          if (this.visible) {
            // 抄送人
            if (this.value.isCC === true) {
              if (this.value.properties.values) {
                this.copyStaffList = this.value.properties.values;
              }
            } else {
              this.appprovelTitle = this.value.properties.title;
              if (
                this.value.properties.approverPerson &&
                this.value.properties.approverPerson.length
              ) {
                this.properties = deepClone(this.value.properties);
                if (this.value.properties.isNull.type === 2) {
                  this.isNullStaff = this.value.properties.isNull.dataId;
                }
              } else {
                this.properties = {
                  approverPerson: [{ ...APPROVER_PERSON }],
                  approverType: 1, // 审批方式
                  isNull: {
                    type: 1, // 审批为空时
                    dataId: {}
                  }
                };
                this.$forceUpdate();
              }
            }
          } else {
            this.properties = {
              approverPerson: [{ ...APPROVER_PERSON }],
              approverType: 1, // 审批方式
              isNull: {
                type: 1, // 审批为空时
                dataId: {}
              }
            };
          }
        },
        immediate: true
      },
      properties: {
        handler(val) {
          let arr = val.approverPerson.map(({ type }) => type);
          let array = Array.from(new Set(arr));
          this.isShowNull = array.length !== 1 || array[0] !== 1;
        },
        immediate: true,
        deep: true
      }
    },
    created() {
      this.status = deepClone(this.fieldStatus);
    },
    methods: {
      cancel() {
        this.$emit('update:visible', false);
        this.$nextTick(() => {
          this.tab = 1;
        });
      },
      // 添加审批人
      addApprovelPerson() {
        this.properties.approverPerson.push({ ...APPROVER_PERSON });
      },
      // 获取审批人数据
      getOnePerson(data) {
        this.isShowAdd = data.val.type !== 8 || data.index !== 0;
        let arr = [];
        this.properties.approverPerson.forEach((item) => {
          if (!arr.includes(item.type)) {
            arr.push(item.type);
          }
        });
        this.isShowNull = arr.length !== 1 || data.val.type !== 1;
        // 判断审批方式是否显示
        if (this.properties.approverPerson.length === 1) {
          if ([1, 5, 6, 7, 9].includes(data.val.type)) {
            this.isShowType = true;
          } else {
            let sum = 0;
            sum += data.val.dataId.length || (data.val.level ? 1 : '');
            this.isShowType = sum > 1;
          }
        } else {
          let sum = 0;
          this.properties.approverPerson.map((item) => {
            if ([1, 5, 6, 7, 9].includes(item.type)) {
              sum += 10;
            }
            sum += item.dataId.length || (item.level ? 1 : '');
          });
          this.isShowType = sum > 1;
        }
        this.properties.approverPerson[data.index] = data.val;
      },
      // 删除某个审批人
      deleteApprovelPerson(index) {
        this.properties.approverPerson.splice(index, 1);
        this.isRefrush = false;
        this.$nextTick(() => {
          this.isRefrush = true;
        });
      },
      // 审批为空时选择成员
      chooseStaff() {
        if (Object.keys(this.isNullStaff).length > 0) {
          let arr = [];
          arr.push(this.isNullStaff.id);
          this.staffList = deepClone(arr);
        } else {
          this.staffList = [];
        }
        this.dialogVisible = true;
      },
      // 添加抄送人
      chooseCopyStaff() {
        if (this.copyStaffList.length >= 20) {
          this.$message.warning('抄送人已达数量上限');
          return;
        }
        this.chooseCopyVisible = true;
      },
      getCheckList(data) {
        // 根据id、type、orgId查重，添加抄送人
        this.copyStaffList = this.copyStaffList.concat(
          data.filter(
            ({ id, type, orgId }) =>
              this.copyStaffList.findIndex(
                (item) =>
                  item.id === id &&
                  item.type === type &&
                  (item.orgId || this.orgId) === orgId
              ) === -1
          )
        );
      },
      // 删除抄送人
      handleDeleteCopyStaff(index) {
        this.copyStaffList.splice(index, 1);
      },
      handleDelete() {
        this.isNullStaff = {};
        this.properties.isNull.dataId = {};
      },
      changeNullRadio() {
        this.isNullAdmin = '';
        this.isNullStaff = {};
        this.properties.isNull.dataId = {};
      },
      getStaffData(data) {
        if (data.length) {
          let obj = {
            id: data[0].id,
            name: data[0].label
          };
          let arr = [];
          arr.push(data[0].id);
          this.staffList = arr;
          this.isNullStaff = obj;
          this.properties.isNull.dataId = obj;
        } else {
          this.properties.isNull.dataId = {};
          this.isNullStaff = {};
        }
      },
      validate() {
        if (
          [2, 3].includes(this.properties.isNull.type) &&
          Object.keys(this.properties.isNull.dataId).length === 0
        ) {
          return false;
        } else {
          if (this.properties.approverPerson.length) {
            let li = this.properties.approverPerson;
            let bool = true;
            for (let i = 0; i < li.length; i++) {
              let item = li[i];
              if (
                // 发起人自选判断
                (item.scopesType === '2' && !item.scopeIds.length) ||
                // 指定成员部门岗位职务判断 20221115
                ([2, 5, 6, 7].includes(item.type) && item.dataId === '') ||
                // 指定成员部门岗位职务判断
                ([2, 5, 6, 7].includes(item.type) &&
                  item.dataId.length === 0) ||
                // 部门负责人判断
                ([3, 4].includes(item.type) &&
                  (!item.startLevelType || !item.level)) ||
                // 连续多级判断
                (item.type === 8 && !item.level) ||
                // 连续多级判断
                (item.type === 9 && (!item.orgId || !item.scopeIds.length))
              ) {
                bool = false;
              }
            }
            return bool;
          } else {
            return false;
          }
        }
        // return true;
      },
      onCopySubmit() {
        if (this.copyStaffList.length) {
          let arr = ['people', 'dept', 'position', 'job'];
          const obj = [
            { type: 'informUserIds', valueList: [] },
            { type: 'informDeptIds', valueList: [] },
            { type: 'informPostIds', valueList: [] },
            { type: 'informJobIds', valueList: [] }
          ];
          this.copyStaffList.map((item) => {
            if (arr.includes(item.type)) {
              obj[arr.indexOf(item.type)].valueList.push({
                id: item.id,
                name: item.label || item.title || item.postName || item.jobName
              });
            }
          });
          const values = obj.filter((l) => l.valueList.length);
          let formJson = {};
          values.map((item) => {
            formJson[item.type] = item.valueList.map((i) => i.id).join(',');
          });
          let contentIntro = [];
          this.copyStaffList.map((item) => {
            if (item.type === 'people') {
              contentIntro.push('成员：' + item.label + ',');
            } else if (item.type === 'dept') {
              contentIntro.push('部门：' + item.title + ',');
            } else if (item.type === 'position') {
              contentIntro.push('岗位：' + item.postName + ',');
            } else if (item.type === 'job') {
              contentIntro.push('职务：' + item.jobName + ',');
            }
          });
          let str = contentIntro.join('');
          if (str.charAt(str.length - 1) === ',') {
            str = str.substr(0, str.length - 1);
          }
          const value = {
            isCC: true,
            title: '抄送',
            type: 'approver',
            content: str,
            properties: { title: '抄送', values: this.copyStaffList, ids: obj }
          };
          this.$emit('add-list', value);
        } else {
          const valueNull = {
            isCC: true,
            title: '抄送',
            type: 'approver',
            content: '请设置抄送人',
            properties: { title: '抄送' }
          };
          this.$emit('add-list', valueNull);
        }
        this.cancel();
      },
      onSubmit() {
        if (!this.validate()) {
          this.$message.error('请将信息设置完整');
          return;
        }
        let contentIntro = [];
        if (this.properties.approverPerson.length) {
          this.properties.approverPerson.forEach((item) => {
            if (item.type === 1) {
              contentIntro.push('发起人自选');
            } else if (item.type === 2) {
              let arr = [];
              item.dataId.map((item) => {
                arr.push(item.label);
              });
              let str = '成员：' + arr.join('、');
              contentIntro = [...contentIntro, str];
            } else if (item.type === 3) {
              let str = '';
              if (item.startLevelType === 1 && item.level === 1) {
                str = '最高部门负责人';
              } else if (item.startLevelType === 2 && item.level === 1) {
                str = '直接部门负责人';
              } else {
                str = `第${arrDesgin.arrNum[item.level - 1]}级部门负责人`;
              }
              contentIntro.push(str);
            } else if (item.type === 4) {
              let str = '';
              if (item.startLevelType === 1 && item.level === 1) {
                str = '最高上级';
              } else if (item.startLevelType === 2 && item.level === 1) {
                str = '直接上级';
              } else {
                str = `第${arrDesgin.arrNum[item.level - 1]}级上级`;
              }
              contentIntro.push(str);
            } else if (item.type === 5) {
              let arr = [];
              item.dataId.map((item) => {
                arr.push(item.name);
              });
              let str = '职务：' + arr.join('、');
              contentIntro = [...contentIntro, str];
            } else if (item.type === 6) {
              let arr = [];
              item.dataId.map((item) => {
                arr.push(item.name);
              });
              let str = '岗位：' + arr.join('、');
              contentIntro = [...contentIntro, str];
            } else if (item.type === 7) {
              let arr = [];
              item.dataId.map((item) => {
                arr.push(item.name);
              });
              let str = '角色：' + arr.join('、');
              contentIntro = [...contentIntro, str];
            } else if (item.type === 8) {
              contentIntro.push('连续多级负责人审批');
            } else if (item.type === 9) {
              contentIntro.push('关联组织');
            }
          });
        } else {
          this.$message.error('请选择审批人');
        }
        const value = {
          title: '审批',
          type: 'approver',
          content: contentIntro.join(','),
          properties: Object.assign(this.properties, {
            title: this.appprovelTitle
          })
        };
        this.$emit('add-list', value);
        this.cancel();
      },
      getFieldLabel(id) {
        let field = this.formDesignList.find((item) => item.id === id);
        return field.valueJson.name || field.name;
      },
      getFieldStatusList(id) {
        let field = this.formDesignList.find((item) => item.id === id);
        return [
          {
            value: '0',
            label: '可编辑',
            disabled: ['tips', 'computed'].includes(field.type)
          },
          { value: '1', label: '只读' },
          { value: '2', label: '隐藏' }
        ];
      },
      onFieldSubmit() {
        this.$emit('update-field-status', this.status);
        this.cancel();
      }
    }
  };
</script>
<style lang="scss">
  .oa-approver {
    .el-drawer__header {
      position: absolute;
      box-sizing: border-box;
      width: 100%;
    }

    .el-drawer__body {
      height: 100%;
    }

    .oa-approver-drawer {
      .oa-approver-drawer-tabs {
        .el-tabs__nav-scroll {
          padding: 0 24px;
        }

        .el-tabs__content {
          position: unset;
          padding: 23px 24px 0;
        }
      }

      .oa-approver-drawer-form {
        .el-form-item {
          .el-form-item__label {
            color: #333;
            font-weight: 500;
            font-size: 14px;
            line-height: 14px !important;
          }
        }

        .oa-approvel-person-content {
          .h-radio-group {
            .el-radio {
              width: 33%;
              margin: 8px 0;
            }
          }
        }

        .oa-approvel-type {
          .h-radio-group {
            .el-radio {
              display: block;
              margin: 24px 0;
            }
          }
        }

        .oa-dept-level {
          .oa-dept-level-content {
            .h-radio-group {
              .el-radio {
                display: block;
                margin: 16px 0;
              }
            }
          }
        }

        .oa-approvel-null {
          margin-top: 13px;

          .oa-approvel-null-admin {
            margin-top: 12px;
          }

          .h-radio-group {
            width: 100%;

            .el-radio {
              width: 50%;
              margin-right: 0;
            }
          }
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  .oa-approver {
    position: relative;

    .oa-approver-overflow {
      height: calc(100% - 65px);
      overflow: auto;

      .oa-approver-drawer {
        padding: 23px 24px;
        text-align: left;

        .oa-approver-drawer-tabs {
          width: calc(100% + 48px);
          margin-top: -23px;
          margin-left: -24px;

          .field-label {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .oa-approver-drawer-title {
          margin-bottom: 39px;
          color: #333;
          font-weight: 550;
          font-size: 18px;
          line-height: 18px;
        }

        .oa-approver-drawer-form {
          .oa-null-staff-list {
            .el-icon-plus {
              width: 56px;
              height: 26px;
              margin-right: 12px;
              line-height: 26px;
              text-align: center;
              border: 1px dashed #999;
              border-radius: 16px;
              cursor: pointer;

              &:hover {
                color: #409eff;
                border-color: #409eff;
              }
            }

            .oa-null-staff-tag {
              margin: 6px 12px 6px 0;
              margin-right: 12px;
              color: #333;
              background: #f5f5f5;
              border-color: #d9d9d9;
              border-radius: 16px;

              .el-icon-link {
                color: #3b7cff;
                font-size: 14px;
              }
            }
          }

          // 添加审批人add
          .oa-add-button {
            display: flex;
            align-items: center;
            margin-top: 16px;
            margin-bottom: 30px;
            color: #409eff;
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;

            .oa-add-button-content {
              cursor: pointer;
            }

            i {
              margin-right: 2px;
              font-size: 16px;
            }
          }
        }

        .oa-approvel-save-button {
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          width: 100%;
          height: 64px;
          padding: 16px 24px;
          text-align: right;
          background: #fff;
          box-shadow: 0 -1px 0 0 #e0e3e5;

          button {
            width: 80px;
            height: 32px;
          }
        }
      }
    }
  }
</style>
