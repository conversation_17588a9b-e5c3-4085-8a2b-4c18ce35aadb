<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
      :span-method="spanMethod"
    >
      <el-table-column
        fixed
        prop="serialNumber"
        label="序号"
        width="50"
        align="center"
      >
      </el-table-column>
      <el-table-column
        fixed
        label="被评价机构"
        width="120"
        align="center"
        prop="deptName"
      >
        <template slot-scope="scope">
          {{ scope.row.deptName || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        fixed
        label="提报进度"
        width="100"
        align="center"
        prop="fillingProgress"
      >
        <template slot-scope="scope">
          {{ scope.row.fillingProgress | fillStatusProgress }}
        </template>
      </el-table-column>
      <el-table-column
        fixed
        label="指标名称"
        width="150"
        align="center"
        prop="indexName"
      >
        <template slot-scope="scope">
          {{ scope.row.indexName || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        label="基本分值"
        width="80"
        align="center"
        prop="basicScore"
      >
        <template slot-scope="scope">
          {{ scope.row.basicScore || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        label="考核目标及计分标准（点击查看）"
        width="180"
        align="center"
        prop="standard"
      >
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleInput1(scope.row)">
            {{ scope.row.standard || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="完成情况（点击查看）"
        align="center"
        prop="completionSituation"
        width="100"
      >
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleInput3(scope.row)">
            {{ scope.row.completionSituation || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="累计完成（点击查看）"
        align="center"
        prop="accumulatedCompletion"
      >
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleInput2(scope.row)">
            {{ scope.row.accumulatedCompletion || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="考核结果（点击查看）" align="center" width="140">
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput4(scope.row)"
          >
            {{ scope.row.assessmentResult || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="证明材料" align="center" width="240">
        <template slot-scope="{ row }">
          <show-file
            v-if="row.attachList && row.attachList.length"
            :list="row.attachList"
          ></show-file>
          <span v-else>暂无</span>
        </template>
      </el-table-column>
      <el-table-column
        label="年度累计考核加减分"
        align="center"
        prop="accumulatedPmScore"
      >
        <template slot-scope="scope">
          {{ scope.row.accumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="年度累计考核加减分（合计）"
        align="center"
        prop="sum1"
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.sum1 | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="截止上周期累计考核加减分"
        align="center"
        prop="lastMonthAccumulatedPmScore"
      >
        <template slot-scope="scope">
          {{ scope.row.lastMonthAccumulatedPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="截止上周期累计考核加减分（合计）"
        align="center"
        width="100"
        prop="sum2"
      >
        <template slot-scope="scope">
          {{ scope.row.sum2 | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="本周期考核加减分"
        align="center"
        prop="currentPmScore"
      >
        <template slot-scope="scope">
          {{ scope.row.currentPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="本周期考核加减分（合计）"
        align="center"
        prop="sum3"
      >
        <template slot-scope="scope">
          {{ scope.row.sum3 | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="本周期考核得分"
        align="center"
        prop="currentScore"
      >
        <template slot-scope="scope">
          {{ scope.row.currentScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="本周期考核得分（合计）"
        align="center"
        prop="sum4"
      >
        <template slot-scope="scope">
          {{ scope.row.sum4 | scoreFilter }}
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      width="600px"
      append-to-body
      :title="value"
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        maxlength="1000"
        show-word-limit
        disabled
        :rows="12"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { decimalPointFn } from '@/util/util';
  import { mapState } from 'vuex';
  import { cloneDeep } from 'lodash';
  import { getSpanNumber } from '@/util/examine';
  import { ShowFile } from '@/components/yk-upload-file';

  export default {
    name: 'evaluate-list-detail',
    components: { ShowFile },
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      },
      periodNum: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        inputVisited: false,
        spanArr: [],
        position: 0,
        tempInput: '',
        value: '',
        tempList: []
      };
    },
    watch: {
      list: {
        handler(val) {
          this.tempList = cloneDeep(val);
          this.handleSum();
        },
        deep: true,
        immediate: true
      }
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      spanMethod({ rowIndex, columnIndex }) {
        let spanAry = ['index', 'deptId', 'fillingProgress'];
        if (
          columnIndex < spanAry.length ||
          columnIndex === 11 ||
          columnIndex === 13 ||
          columnIndex === 15 ||
          columnIndex === 17
        ) {
          //合并相同的名字
          let itemSpan = getSpanNumber(this.list, 'deptId');
          return {
            rowspan: itemSpan[rowIndex],
            colspan: 1
          };
        }
      },
      handleInput1(row) {
        this.inputVisited = true;
        this.tempInput = row.standard;
        this.value = '考核目标及计分标准';
      },
      handleInput2(row) {
        this.inputVisited = true;
        this.tempInput = row.accumulatedCompletion;
        this.value = '累计完成';
      },
      handleInput3(row) {
        this.inputVisited = true;
        this.tempInput = row.completionSituation;
        this.value = '完成情况';
      },
      handleInput4(row) {
        this.inputVisited = true;
        this.tempInput = row.assessmentResult;
        this.value = '考核结果';
      },
      // 完成情况弹窗关闭
      inputClose() {
        this.inputVisited = false;
      },
      handleSum() {
        this.tempList.map((item) => {
          item.lastMonthAccumulatedPmScore = Number(
            item.lastMonthAccumulatedPmScore
          );
          item.accumulatedPmScore = Number(item.accumulatedPmScore || 0);
          item.currentPmScore = Number(item.currentPmScore || 0);
          item.currentScore = Number(item.currentScore || 0);
          item.sum1 = 0;
          item.sum2 = 0;
          item.sum3 = 0;
          item.sum4 = 0;
          return item;
        });
        let sum1 = {},
          sum2 = {},
          sum3 = {},
          sum4 = {};
        this.tempList.forEach((item) => {
          if (sum1[item.deptId] === undefined) {
            sum1[item.deptId] = item.accumulatedPmScore;
          } else {
            sum1[item.deptId] = sum1[item.deptId] + item.accumulatedPmScore;
          }

          if (sum2[item.deptId] === undefined) {
            sum2[item.deptId] = item.lastMonthAccumulatedPmScore;
          } else {
            sum2[item.deptId] =
              sum2[item.deptId] + item.lastMonthAccumulatedPmScore;
          }

          if (sum3[item.deptId] === undefined) {
            sum3[item.deptId] = item.currentPmScore;
          } else {
            sum3[item.deptId] = sum3[item.deptId] + item.currentPmScore;
          }

          if (sum4[item.deptId] === undefined) {
            sum4[item.deptId] = item.currentScore;
          } else {
            sum4[item.deptId] = sum4[item.deptId] + item.currentScore;
          }
        });

        this.tempList.map((item) => {
          item.sum1 = decimalPointFn(sum1[item.deptId]);
          item.sum2 = decimalPointFn(sum2[item.deptId]);
          item.sum3 = decimalPointFn(sum3[item.deptId]);
          item.sum4 = decimalPointFn(sum4[item.deptId]);
          return item;
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
    cursor: pointer;
  }
</style>
