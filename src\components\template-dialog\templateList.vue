<template>
  <div
    v-loading="temLoading"
    class="template-content"
    :class="{ 'review-btn': !isEdit }"
  >
    <template
      v-for="({ categoryName, category, modelCount, modelList }, i) of temList"
    >
      <div :key="category" class="header">
        <span>{{ categoryName }}({{ modelCount }})</span>
        <span @click="searchMore(category, i)" class="more">更多</span>
      </div>
      <template v-if="modelList.length">
        <div
          v-for="row of modelList"
          :key="row.id"
          class="list"
          :class="{
            selectCell: selectRadio === row.id
          }"
        >
          <el-radio v-model="selectRadio" @change="changeRadio" :label="row.id">
            <img v-oss src="/document/file-Word-icon.png" />
            <span class="file-name">{{ row.modelName }}</span
            >.{{ row.modelExt }}</el-radio
          >
          <div class="list-btn" :class="{ btnSty: row.modelType === 2 }">
            <el-button @click="getViewUrlFun(row, false)" type="text"
              >预览</el-button
            >
            <el-button
              v-if="isEdit && row.modelType === 1"
              @click="getViewUrlFun(row, true)"
              type="text"
              >编辑</el-button
            >
            <el-button
              v-if="isEdit && row.modelType === 1"
              @click="deleteList(row, i, category)"
              type="text"
              >删除</el-button
            >
          </div>
        </div>
      </template>
      <div v-else :key="`${category}-empty`" class="empty-box">
        <img v-oss src="/common/noData.png" class="empty-img" />
      </div>
    </template>
  </div>
</template>
<script>
  import { getModelUrl } from '@/api/resource/wps';
  import { listMore, removeModel } from '@/api/resource/contractModel';
  import { getToken } from '@/util/auth';
  import { deepClone } from '@/util/util.js';

  export default {
    props: {
      list: {
        type: Array,
        default: () => []
      },
      type: {
        type: String,
        default: '1'
      },
      isEdit: {
        type: Boolean,
        default: true
      },
      radioId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        temList: [],
        temLoading: false,
        selectRadio: '',
        moreDataArr: []
      };
    },
    watch: {
      list: {
        handler() {
          this.temList = deepClone(this.list);
        },
        deep: true
      },
      radioId() {
        this.selectRadio = this.radioId;
      }
    },
    methods: {
      updateMoreList() {
        this.moreDataArr.forEach(([category, i]) => {
          this.searchMore(category, i);
        });
      },
      changeRadio() {
        if (this.isEdit) {
          this.selectRadio = '';
          return;
        }
        const list = this.temList.reduce((arr, li) => {
          return [...arr, ...li.modelList];
        }, []);
        list.forEach((l) => {
          if (l.id === this.selectRadio) {
            this.$emit('update-radio', { id: this.selectRadio, selectObj: l });
          }
        });
      },
      getViewUrlFun(row, isEdit) {
        const { id, modelName, modelType } = row;
        const editType = isEdit ? 'write' : 'read';
        const status = isEdit ? 'edit' : 'preview';
        this.temLoading = true;

        getModelUrl(id, editType)
          .then((res) => {
            if (res && res.data && res.data.data) {
              let routeUrl = this.$router.resolve({
                name: 'documentDetailNew',
                query: {
                  url: res.data.data,
                  token: getToken(),
                  docName: modelName,
                  fileType: modelType,
                  status,
                  editEnable: modelType === 1,
                  id,
                  commonType: 1
                }
              });
              this.$router.push(routeUrl.location);
            }
          })
          .finally(() => {
            this.temLoading = false;
          });
      },
      searchMore(category, i) {
        this.temLoading = true;
        listMore(this.type, category)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.temList[i].modelList = res.data.data;
              this.moreDataArr.push([category, i]);
            }
          })
          .finally(() => {
            this.temLoading = false;
          });
      },
      async deleteList({ modelFullName, id }, i, category) {
        const isModel = await this.$confirm(
          `确认删除合同模板"${modelFullName}"?`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => true)
          .catch(() => false);

        if (isModel) {
          this.temLoading = true;
          removeModel(id)
            .then((res) => {
              if (res && res.data && res.data.success) {
                this.searchMore(category, i);
                this.$message.success('删除成功');
              }
            })
            .finally(() => {
              this.temLoading = false;
            });
        }
      }
    }
  };
</script>
<style lang="scss">
  .template-content {
    margin-top: 16px;
    padding-bottom: 24px !important;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 56px;
      margin-top: 16px;
      padding: 0 16px;
      color: #333;
      background: #fafafa;
      border-bottom: 1px solid #eaeaed;

      &:first-child {
        margin-top: 0;
      }

      .more {
        color: #409eff;
        cursor: pointer;

        .el-icon-arrow-down {
          margin-left: 12px;
          color: #9e9e9e;
        }
      }
    }

    .list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 54px;
      padding-left: 16px;
      border-bottom: 1px solid #eaeaed;

      .el-radio {
        display: flex;

        & > span {
          display: flex;
          align-items: center;

          span {
            margin-left: 8px;
          }
        }

        .el-radio__input {
          display: none;
        }

        .el-radio__label {
          padding-left: 0;
          cursor: auto;
        }
      }

      img {
        width: 22px;
        height: 26px;
      }

      .list-btn {
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;
        width: 240px;
        padding: 0 62px;

        button {
          margin: 0;
          padding: 0;
          color: #fe6b06 !important;
        }
      }

      .file-name {
        max-width: 400px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .selectCell {
      background: #fafafa;
    }

    .btnSty {
      justify-content: center !important;
    }

    .empty-box {
      display: flex;
      justify-content: center;
      width: 100%;
      padding-top: 16px;

      img {
        width: 120px;
      }
    }
  }

  .review-btn {
    border: 1px solid #d9d9d9;

    .list-btn {
      justify-content: center !important;
    }

    .el-radio__input {
      display: flex !important;
    }

    .el-radio__label {
      padding-left: 12px !important;
      cursor: pointer !important;
    }
  }
</style>
