<template>
  <el-tree
    :data="list"
    :check-strictly="true"
    :props="defaultProps"
    :default-expanded-keys="defaultExpandedKeys"
    show-checkbox
    node-key="id"
    ref="tree"
    highlight-current
    @check-change="handleCheckChange"
  ></el-tree>
</template>

<script>
  import { getDeptTree } from '@/api/system/dept';
  import website from '@/config/website';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'deptCom',
    data() {
      return {
        list: [],
        defaultProps: {
          children: 'children',
          label: 'title'
        },
        defaultExpandedKeys: []
      };
    },
    props: {
      deptIds: {
        type: Array,
        default() {
          return [];
        }
      },
      single: {
        type: Boolean,
        default: false
      }
    },
    mounted() {
      this.getDeptListData();
    },
    methods: {
      // 获取机构数据
      async getDeptListData() {
        try {
          const res = await getDeptTree(website.tenantId);
          const data = res.data.data;
          this.list = data;
          this.defaultExpandedKeys = cloneDeep(this.deptIds);
          this.$refs.tree.setCheckedKeys(this.deptIds || []);
        } catch (e) {
          console.error(e);
        }
      },
      // 选中
      handleCheckChange(data, checked) {
        if (this.single) {
          if (checked) {
            this.$refs.tree.setCheckedKeys([data.id]);
            this.$emit('emitDept', [data]);
          }
        } else {
          let res = this.$refs.tree.getCheckedNodes(false, true);
          let arr = [];
          res.forEach((item) => {
            arr.push(item);
          });
          this.$emit('emitDept', arr);
        }
      }
    }
  };
</script>
