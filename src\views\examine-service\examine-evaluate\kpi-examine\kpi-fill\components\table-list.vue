<template>
  <el-table :data="list" border style="width: 100%" size="small">
    <el-table-column type="index" label="序号" width="50" align="center" />
    <el-table-column prop="code" label="填报单号" align="center">
    </el-table-column>
    <el-table-column prop="deptName" label="被评价机构" align="center">
    </el-table-column>
    <el-table-column prop="receiveUser" label="接收人" align="center">
    </el-table-column>
    <el-table-column prop="periodItemName" label="考核周期" align="center">
    </el-table-column>
    <el-table-column prop="fillingDate" label="填报日期" align="center">
      <template slot-scope="scope">
        {{ scope.row.fillingDate || '--' }}
      </template>
    </el-table-column>
    <el-table-column prop="status" label="单据状态" align="center">
      <template slot-scope="scope">
        {{ scope.row.status | fillStatus }}
      </template>
    </el-table-column>
    <el-table-column prop="examineStatus" label="审批状态" align="center">
      <template slot-scope="scope">
        {{ scope.row.examineStatus | receiptStatus }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="220" align="center">
      <template slot-scope="scope">
        <el-button
          type="text"
          size="small"
          icon="el-icon-view"
          @click="$emit('dispatch', 'detail', scope.row)"
          >查看</el-button
        >
        <el-button
          v-if="scope.row.canFilling && scope.row.status === '0'"
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'fill', scope.row)"
          >填报</el-button
        >
        <el-button
          v-if="
            scope.row.canFilling &&
            (scope.row.status === '1' || scope.row.examineStatus === '2')
          "
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'edit', scope.row)"
          >填报</el-button
        >
        <el-button
          v-if="scope.row.status === '2' && scope.row.canApprove"
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'approve', scope.row)"
          >审批</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-list',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      }
    }
  };
</script>
