<template>
  <div v-if="visible">
    <el-dialog
      title="选择文件"
      :visible.sync="visible"
      width="1000px"
      :before-close="handleClose"
      append-to-body
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="企业文档" name="company">
          <document-list
            @search="getFileListFun"
            @addselect="addSelectDocument"
            @removeSelect="removeSelect"
            :selected="selectDocumentList"
            :page-data="companyFileListObj"
            :local-upload-file="localUploadFile"
            :loading="loading"
            :folder-info="companyFolderInfo"
            :selected-num="selectDocumentAllLength"
          />
        </el-tab-pane>
        <el-tab-pane label="群文档" name="group">
          <document-list
            @search="getGroupFileListFun"
            @addselect="addSelectDocument"
            @removeSelect="removeSelect"
            @getSelectdNum="getSelectdNum"
            :selected="selectDocumentList"
            :local-upload-file="localUploadFile"
            :page-data="groupFileListObj"
            :loading="loading"
            :folder-info="groupFolderInfo"
            :selected-num="selectDocumentAllLength"
          />
        </el-tab-pane>
      </el-tabs>
      <div
        @click="selectedDialog"
        class="selected-btn-box"
        :class="
          selectDocumentLength === 0 || !selectDocumentLength
            ? 'selected-btn-box-disable'
            : ''
        "
      >
        <div class="selected-btn-number">已选 {{ selectDocumentLength }}</div>
        <i class="el-icon-arrow-up"></i>
      </div>
      <div slot="footer">
        <el-button @click="handleClose" size="medium">返回</el-button>
        <el-button
          @click="updateSelectedList"
          :disabled="selectDocumentAllLength === 0"
          type="primary"
          size="medium"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="selectedVisible"
      title="已选文件"
      class="selected-list-dialog"
      :before-close="closeSelectedDialog"
      width="480px"
      append-to-body
    >
      <div class="selected-list-context">
        <ul>
          <li v-for="(item, key) in selectDocumentList" :key="key">
            <img
              v-oss
              class="selected-list-icon"
              :src="getDocIconFun(item.type || item.fileType)"
              alt=""
            />
            <div class="selected-list-file-name">{{ item.docName }}</div>
            <div class="selected-list-file-size">{{ item.size }}</div>
            <i @click="removeSelect(item)" class="el-icon-circle-close" />
          </li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import DocumentList from './components/document-list';
  import { getFileList, getGroupFileList } from '@/api/desk/document';
  import { getDocIcon } from '@/util/util';
  export default {
    components: { DocumentList },
    props: {
      visible: {
        type: Boolean
      },
      selectedNum: {
        type: Number
      },
      maxLength: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        activeName: 'company',
        loading: false,
        pageData: {},
        companyFileListObj: {
          breadList: []
        }, // 公司文件列表对象
        companyFolderInfo: {
          breadList: []
        },
        groupFolderInfo: {
          breadList: []
        },
        groupFileListObj: {
          breadList: []
        }, // 群文件列表对象
        selectDocumentList: {},
        selectedDocumentList: {}, // 已选择文档文件列表
        localUploadFile: {},
        selectedVisible: false // 已选文件弹窗
      };
    },
    computed: {
      // 全部已选数量
      selectDocumentAllLength() {
        return this.selectedNum + this.selectDocumentLength;
      },
      // 已选择数量的计算
      selectDocumentLength() {
        let length = 0;
        for (const key in this.selectDocumentList) {
          if (Object.hasOwnProperty.call(this.selectDocumentList, key)) {
            // const element = this.selectDocumentList[key];
            length++;
          }
        }
        return length;
      }
    },
    methods: {
      getSelectdNum() {
        return this.selectDocumentAllLength;
      },
      // 关闭已选弹窗
      closeSelectedDialog() {
        this.selectedVisible = false;
        if (this.activeName === 'company') {
          this.getFileListFun(this.searchParams);
        } else {
          this.getGroupFileListFun(this.searchParams);
        }
      },
      // 获取列表icon
      getDocIconFun(type) {
        return getDocIcon(type);
      },
      // 每次打开时初始化已选
      initSelectList(object, localFileList) {
        // this.selectDocumentList = object;
        this.selectedDocumentList = object;
        this.selectDocumentList = {};
        this.localUploadFile = localFileList;
      },
      // 点击确定按钮
      updateSelectedList() {
        if (this.maxLength && this.maxLength < this.selectDocumentAllLength) {
          this.$message.error(
            `已选择${this.selectedNum}个文件，本次最多选择${
              this.maxLength - this.selectedNum
            }个`
          );
          return;
        }
        this.$emit('updatelist', this.selectDocumentList);
        this.$emit('close');
      },
      // 已选文件弹窗
      selectedDialog() {
        if (this.selectDocumentLength > 0) {
          this.selectedVisible = true;
        }
      },
      // 更新已选择的文件列表
      selectDocumentListUpdate() {
        // this.selectDocumentList = object;
      },
      // 删除已选的文档
      removeSelect(item) {
        let { id, optionTime } = item;
        let object = JSON.parse(JSON.stringify(this.selectDocumentList));
        let key = `${id}_${optionTime}`;
        if (object[key]) {
          delete object[key];
        }
        this.selectDocumentList = object;
        // this.selectDocumentLength;
        this.$forceUpdate();
      },
      // 添加已经选择的文件
      addSelectDocument(arr) {
        let object = JSON.parse(JSON.stringify(this.selectDocumentList));
        // let returnArr = [];
        arr.map((item) => {
          object[`${item.id}_${item.optionTime}`] = item;
        });
        this.selectDocumentList = object;
        // this.$emit("updatelist", object);
      },
      handleClose() {
        this.$emit('close');
        this.selectDocumentList = {};
      },
      // 获取公司文件列表
      getFileListFun(param) {
        this.loading = true;
        let params = {};
        if (param) {
          params = param;
        } else {
          params = {
            current: 1,
            size: 10
          };
        }
        this.searchParams = params;
        getFileList(params)
          .then((res) => {
            this.loading = false;
            if (res.data.data) {
              this.companyFileListObj = res.data.data.page;
              this.companyFolderInfo = res.data.data.folderInfo;
            }
            this.multipleSelection = [];
          })
          .catch((err) => {
            console.error(err);
            this.loading = false;
          });
      },
      // 获取群文件列表
      getGroupFileListFun(param) {
        this.loading = true;
        let params = {};

        if (param) {
          params = param;
        } else {
          params = {
            current: 1,
            size: 10
          };
        }
        this.searchParams = params;

        // this.groupDocumentListParams = param;
        getGroupFileList(params)
          .then((res) => {
            this.loading = false;
            if (res.data.data) {
              this.groupFileListObj = res.data.data.page;
              this.groupFolderInfo = res.data.data.folderInfo;
            }
            // this.multipleSelection = [];
          })
          .catch((err) => {
            console.error(err);
            this.loading = false;
          });
      }
    }
  };
</script>

<style lang="scss">
  .selected-btn-box {
    position: absolute;
    bottom: 91px;
    color: #333;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;

    &:hover {
      color: #409eff;

      .el-icon-arrow-up {
        color: #409eff;
      }
    }

    .selected-btn-number {
      display: inline-block;
      padding-right: 12px;
    }

    .el-icon-arrow-up {
      color: #999;
      font-weight: 500;
    }

    &.selected-btn-box-disable {
      cursor: not-allowed;

      &:hover {
        color: #333;
      }

      .el-icon-arrow-up {
        color: #999;
      }
    }
  }

  .selected-list-dialog-title {
    display: inline-block;
    vertical-align: middle;
  }

  .selected-list-dialog-title-btn {
    float: right;
    vertical-align: middle;
    cursor: pointer;
  }

  .selected-list-dialog {
    .el-dialog {
      .el-dialog__body {
        padding: 0 !important;
      }
    }

    .selected-list-context {
      ul {
        height: 314px;
        margin: 0;
        padding: 0 0 10px;
        overflow: auto;
      }

      li {
        height: 26px;
        padding: 14px 16px;
        line-height: 26px;
        list-style: none;
        border-bottom: 1px solid rgba(0, 0, 0, 6%);

        .selected-list-icon {
          width: 22px;
        }

        .selected-list-file-name {
          display: inline-block;
          width: 255px;
          padding-right: 17px;
          padding-left: 8px;
          overflow: hidden;
          line-height: 26px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .selected-list-file-size {
          display: inline-block;
          line-height: 26px;
          vertical-align: top;
        }

        .el-icon-circle-close {
          display: inline-block;
          float: right;
          padding-top: 4px;
          color: #a3a3a3;
          font-size: 16px;
          vertical-align: top;
          cursor: pointer;

          &:hover {
            color: #757575;
          }
        }
      }

      .el-icon-circle-check {
        color: #70cd44;
        font-size: 16px;
      }

      .el-icon-circle-close {
        display: none;
        color: #a3a3a3;
        font-size: 16px;
        cursor: pointer;

        &:hover {
          color: #757575;
        }
      }
    }
  }
</style>
