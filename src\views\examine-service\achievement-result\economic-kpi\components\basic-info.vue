<template>
  <div class="info_form_mb5">
    <el-form label-width="110px" label-suffix="：" size="small">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="绩效单号">
            {{ form.code || '---' }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="考核周期">
            {{ form.periodItemName || '---' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据状态">
            {{ form.status | changeStatus }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审核状态">
            {{ form.examineStatus | changeExamineStatus }}
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="isDetail">
          <el-form-item label="提报日期">
            {{ form.submitDate | formatDate }}
          </el-form-item>
        </el-col>
        <el-col :span="8" v-else>
          <el-form-item label="提报日期">
            <el-date-picker
              type="date"
              v-model="cDate"
              value-format="yyyy-MM-dd"
              placeholder="选择提报日期"
              style="width: 200px"
              :editable="false"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'basic-info',
    props: {
      isDetail: {
        type: Boolean,
        default: false
      },
      form: {
        type: Object,
        default() {
          return {};
        }
      },
      submitDate: {
        type: String,
        require: true
      }
    },
    model: {
      prop: 'submitDate',
      event: 'change'
    },
    computed: {
      cDate: {
        get() {
          return this.submitDate;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    }
  };
</script>
