<template>
  <div class="table_wrapper">
    <el-table
      :data="list"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      size="small"
    >
      <el-table-column
        align="center"
        type="index"
        label="序号"
      ></el-table-column>
      <el-table-column align="center" prop="title" label="推送标题" />
      <el-table-column
        align="center"
        prop="content"
        label="推送内容"
        mim-width="180"
        show-overflow-tooltip
      />
      <el-table-column align="center" prop="scheduleTime" label="推送时间">
        <template slot-scope="{ row }">
          {{ row.scheduleTime || '--' }}
        </template>
      </el-table-column>
      <!-- 与后台约定, 先根据isImmediate字段判断是否已推送: 立即推送为已推送, 定时推送为未推送 -->
      <el-table-column align="center" prop="isImmediate" label="状态">
        <template slot-scope="{ row }">
          <el-tag type="warning" v-if="row.isImmediate === 1">未推送</el-tag>
          <el-tag type="success" v-else-if="row.isImmediate === 2"
            >已推送</el-tag
          >
          <el-tag v-else>--</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="$emit('dispatch', 'view', scope.row)"
            >查看
          </el-button>
          <el-button
            v-if="scope.row.isImmediate === 1 && permission.message_edit"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="$emit('dispatch', 'edit', scope.row)"
            >编辑
          </el-button>
          <el-button
            v-if="scope.row.isImmediate === 1 && permission.message_del"
            type="text"
            icon="el-icon-delete"
            size="small"
            @click="rowDel(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { postDel } from '@/api/message/message';
  import { mapGetters } from 'vuex';

  export default {
    name: 'MessagePushTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    watch: {
      source: {
        handler(arr) {
          this.list = cloneDeep(arr);
        },
        deep: true
      }
    },
    data() {
      return {
        list: [],
        visited: false
      };
    },
    methods: {
      // 删除
      rowDel({ id }) {
        this.$confirm('确定删除选择的数据吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await postDel({ id });
          if (res.data.code === 200) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.$emit('dispatch', 'refresh');
          }
        });
      },
      // 多选框
      handleSelectionChange(val) {
        const multipleSelection = val.map((item) => {
          return item.code;
        });
        this.$emit('dispatch', 'checked', multipleSelection);
      }
    },
    computed: {
      ...mapGetters(['permission'])
    }
  };
</script>

<style lang="scss" scoped></style>
