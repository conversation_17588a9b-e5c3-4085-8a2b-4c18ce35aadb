<template>
  <div>
    <el-button
      type="primary"
      icon="el-icon-plus"
      size="small"
      v-if="permission.user_add"
      @click="$emit('dispatch', 'add')"
      >新增</el-button
    >
    <el-button
      type="danger"
      size="small"
      plain
      icon="el-icon-delete"
      v-if="permission.user_delete"
      @click="handleDelete"
      >删 除
    </el-button>
    <el-button
      type="info"
      size="small"
      plain
      v-if="permission.user_role"
      icon="el-icon-user"
      @click="$emit('dispatch', 'grant')"
      >角色配置
    </el-button>
    <el-button
      type="info"
      size="small"
      plain
      v-if="permission.user_reset"
      icon="el-icon-refresh"
      @click="handleReset"
      >密码重置
    </el-button>
    <!--    <el-button-->
    <!--      type="info"-->
    <!--      size="small"-->
    <!--      plain-->
    <!--      v-if="userInfo.role_name.includes('admin')"-->
    <!--      icon="el-icon-setting"-->
    <!--      @click="$emit('dispatch', 'platform')"-->
    <!--      >平台配置-->
    <!--    </el-button>-->
    <!--    <el-button-->
    <!--      type="info"-->
    <!--      size="small"-->
    <!--      plain-->
    <!--      v-if="userInfo.role_name.includes('admin')"-->
    <!--      icon="el-icon-coordinate"-->
    <!--      @click="handleLock"-->
    <!--      >账号解封-->
    <!--    </el-button>-->
    <el-button
      type="success"
      size="small"
      plain
      v-if="permission.user_import"
      icon="el-icon-upload2"
      @click="$emit('dispatch', 'import')"
      >导入
    </el-button>
    <el-button
      type="warning"
      size="small"
      plain
      v-if="userInfo.role_name.includes('admin')"
      icon="el-icon-download"
      @click="handleExport"
      >导出
    </el-button>
    <!-- <el-button
      type="info"
      size="small"
      plain
      v-if="userInfo.role_name.includes('admin') && website.WS.enable"
      icon="el-icon-user"
      @click="handleMessage"
      >消息发送
    </el-button> -->
  </div>
</template>
<script>
  import website from '@/config/website';
  import { remove, resetPassword, unlock } from '@/api/system/user';
  import { exportBlob } from '@/api/common';
  import { downloadXls } from '@/util/util';
  import { getToken } from '@/util/auth';
  import { dateNow } from '@/util/date';
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  import { mapGetters } from 'vuex';

  export default {
    name: 'user-table-operate',
    props: {
      ids: String,
      selectionList: {
        type: Array,
        default() {
          return [];
        }
      },
      search: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        website
      };
    },
    methods: {
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定删除选择的数据吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.$emit('dispatch', 'refresh');
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          });
      },
      handleReset() {
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定将选择账号密码重置 JQ1tol@sd?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return resetPassword(this.ids);
          })
          .then(() => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          });
      },
      handleLock() {
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定将选择账号解封？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return unlock(this.ids);
          })
          .then(() => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          });
      },
      handleExport() {
        this.$confirm('是否导出用户数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          NProgress.start();
          exportBlob(
            `/api/szyk-user/export-user?${
              this.website.tokenHeader
            }=${getToken()}&account=${this.search.account}&realName=${
              this.search.realName
            }`
          ).then((res) => {
            downloadXls(res.data, `用户数据表${dateNow()}.xlsx`);
            NProgress.done();
          });
        });
      }
    },
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    }
  };
</script>
<style lang="scss" scoped></style>
