<template>
  <el-dialog
    append-to-body
    :modal="false"
    :visible.sync="visible"
    :fullscreen="true"
    @close="close"
  >
    <div slot="title">
      <span>{{ title }}</span>
    </div>
    <slot></slot>
  </el-dialog>
</template>

<script>
  export default {
    name: 'fullscreen',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: ''
      }
    },
    methods: {
      close() {
        this.$emit('close', false);
      }
    }
  };
</script>
