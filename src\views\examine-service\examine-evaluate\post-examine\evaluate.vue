<template>
  <div class="wrapper" v-loading="loading">
    <!--  基础信息  -->
    <el-collapse style="border: 0" v-model="collapse">
      <el-collapse-item name="one">
        <template slot="title">
          <h2 class="title">基础信息</h2>
        </template>
        <basic-info :form="basic" :is-detail="isDetail" @syncDate="syncDate" />
      </el-collapse-item>
    </el-collapse>
    <section>
      <el-collapse style="border: 0" v-model="collapseTable">
        <el-collapse-item name="one">
          <template slot="title">
            <div class="title_wrapper">
              <h2 class="title">评价信息</h2>
              <el-tooltip
                effect="dark"
                content="全屏展示"
                placement="top-start"
              >
                <i
                  class="el-icon-full-screen icon-full"
                  @click.stop="visited = true"
                ></i>
              </el-tooltip>
            </div>
          </template>
          <!--   详情   -->
          <evaluate-list-detail v-if="isDetail" :list="listExamine" />
          <!--   评价   -->
          <evaluate-list
            v-else
            :key="1"
            :form="basic"
            :max-height="300"
            :list="listExamine"
            :dept-id="basic.assessedDeptId"
            @sync="handleExamine"
          />
        </el-collapse-item>
      </el-collapse>
      <!--   审批环节展示   -->
      <div class="title_wrapper">
        <!--   审批详情     -->
        <div
          v-if="isDetail && !isApproval && basic.processInstanceId"
          style="padding: 10px 8px 10px 0"
        >
          <approval-flow
            :id="basic.processInstanceId"
            @repeal="handleRepeal"
            @close="$emit('close')"
          />
        </div>
        <!--   审批   -->
        <div
          v-if="isDetail && isApproval && basic.processInstanceId"
          style="padding-bottom: 20px"
        >
          <h2 class="title">审批流程</h2>
          <approval-flow
            :id="basic.processInstanceId"
            @submit="handleSubmit"
            @close="$emit('close')"
          />
        </div>
        <!--   普通展示审批环节   -->
        <div v-if="!isDetail && !isApproval">
          <h2 class="title">审批流程</h2>
          <div style="padding: 30px 70px 10px 40px">
            <init-flow v-if="processId" :id="processId" ref="initFlow" />
          </div>
        </div>
      </div>
      <!--  按钮操作区    -->
      <div class="end-btn" v-if="!isApproval">
        <el-button
          v-if="!isDetail"
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </section>
    <!--   全屏操作   -->
    <full-screen-table v-model="visited" :title="title">
      <!--  详情   -->
      <evaluate-list-detail
        v-if="isDetail"
        full
        :max-height="0"
        :list="listExamine"
      />
      <!--  评价   -->
      <evaluate-list
        v-else
        full
        :key="2"
        :form="basic"
        :max-height="0"
        :list="listExamine"
        :dept-id="basic.assessedDeptId"
        @sync="handleExamine"
      />
      <!--   操作按钮   -->
      <div class="end-btn" v-if="!isApproval">
        <el-button
          v-if="!isDetail"
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </full-screen-table>
  </div>
</template>

<script>
  // import { cloneDeep } from 'lodash';
  import { dateFormat } from '@/util/date';
  import { service_process, service_code } from '@/constant/service';
  import {
    getDetail,
    postSave,
    postSubmit,
    getProcessId,
    postApprove,
    getRevocation
  } from '@/api/examine/post-examine';
  import { BasicInfo, EvaluateList, EvaluateListDetail } from './components';
  import { FullScreenTable } from '../../components/full-screen';
  import {
    InitFlow,
    DetailFlow,
    ApprovalFlow
  } from '@/views/examine-service/components/approval';

  export default {
    name: 'evaluate',
    components: {
      ApprovalFlow,
      BasicInfo,
      EvaluateList,
      EvaluateListDetail,
      FullScreenTable,
      InitFlow,
      DetailFlow
    },
    props: {
      isDetail: {
        type: Boolean,
        required: true
      },
      isApproval: {
        type: Boolean,
        required: true
      },
      code: {
        type: String,
        default: ''
      }
    },
    computed: {
      title() {
        return this.isDetail ? '查看详情' : '评价详情';
      }
    },
    data() {
      return {
        collapse: 'one',
        collapseTable: 'one',
        visited: false,
        listExamine: [],
        basic: {},
        temp: {},
        processId: '',
        approvalId: '',
        loadingSave: false,
        loadingSubmit: false,
        disabled: false,
        loading: false
      };
    },
    async mounted() {
      await this.init();
      if (!this.isDetail && !this.isApproval) {
        await this.getProcess();
      }
    },
    methods: {
      // 获取流程定义id
      async getProcess() {
        try {
          const res = await getProcessId({
            code: service_code,
            key: service_process.post_evaluate
          });
          this.processId = res.data.data;
        } catch (e) {
          console.error(e);
        }
      },
      // 获取详情
      async init() {
        try {
          this.loading = true;
          const res = await getDetail({
            evaluationCode: this.code
          });
          const data = res.data.data;
          this.temp = data;
          this.basic = data.pluginForm;
          if (!this.basic.evaluationDate && !this.isDetail) {
            this.basic.evaluationDate = dateFormat(new Date(), 'yyyy-MM-dd');
          }
          this.listExamine = this.basic.resultList || [];
          this.listExamine.map((item) => {
            if (typeof item.score === 'string') {
              item.score = Number(item.score);
            } else if (item.score === null) {
              item.score = undefined;
            }
            return item;
          });
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 保存/提交
      async save(type) {
        const initFlow = this.$refs.initFlow.$data.users;
        const basic = Object.assign({}, this.basic, {
          resultList: this.listExamine
        });
        const data = Object.assign({}, this.temp, {
          processDefinitionId: this.processId,
          pluginForm: basic,
          usersNodeinfos: initFlow
        });
        // 保存
        if (type === 'save') {
          try {
            this.disabled = true;
            this.loadingSave = true;
            const res = await postSave(data);
            if (res.data.code === 200) {
              this.$message.success('数据保存成功！');
              this.$emit('refresh');
            }
          } catch (e) {
            console.error(e);
          } finally {
            this.disabled = false;
            this.loadingSave = false;
          }
        }
        // 提交
        if (type === 'submit') {
          if (!this.basic.evaluationDate) {
            return this.$message.warning('评价日期未选择，请选择！');
          }

          if (!initFlow.length) {
            return this.$message.warning(
              '审批流程没有审批人节点，请完善审批流程！'
            );
          }
          const isTrue = initFlow.some((item) => {
            if (item.users === null || item.users.length === 0) {
              return true;
            }
          });
          if (isTrue) {
            return this.$message.warning('请选择审批人！');
          }

          const status = this.listExamine.some((item) => {
            if (item.isMinister === 0 && typeof item.score !== 'number') {
              return true;
            }
          });
          if (status) {
            return this.$message.warning('存在未填写分值，请核对！');
          }
          // const nums = this.listExamine.map((item) => item.score);
          // if (nums.length < 10) {
          //   // 10人以下规则
          //   const set = new Set(nums);
          //   const setArr = [...set];
          //   if (setArr.length !== nums.length) {
          //     return this.$message.warning('存在岗位分数一致，请核对！');
          //   }
          //   const copyNums = cloneDeep(nums);
          //   copyNums.sort((a, b) => a - b);
          //   let isOk = false;
          //   for (let i = 1; i < copyNums.length; i++) {
          //     const value = copyNums[i] - copyNums[i - 1];
          //     if (value < 0.5) {
          //       isOk = true;
          //       break;
          //     }
          //   }
          //   if (isOk) {
          //     return this.$message.warning('存在岗位分差小于0.5分，请核对！');
          //   }
          // } else if (nums.length >= 10) {
          //   // 10人以上规则
          //   const copyNums = cloneDeep(nums);
          //   copyNums.sort((a, b) => a - b);
          //   const min = copyNums[0];
          //   const max = copyNums[copyNums.length - 1];
          //   if (max - min < 5) {
          //     return this.$message.warning('最高分与最低分差小于5分，请核对！');
          //   }
          // }
          try {
            this.disabled = true;
            this.loadingSubmit = true;
            const res = await postSubmit(data);
            if (res.data.code === 200) {
              this.$message.success('数据保存成功！');
              this.$emit('refresh');
            }
          } catch (e) {
            console.error(e);
          } finally {
            this.disabled = false;
            this.loadingSubmit = false;
          }
        }
      },
      // 同步数据
      handleExamine(list) {
        this.listExamine = list;
      },
      // 同步时间
      syncDate(date) {
        this.basic.evaluationDate = date;
      },
      // 审批
      async handleSubmit(val) {
        const data = Object.assign({}, val, {
          pluginForm: this.basic
        });
        try {
          const res = await postApprove(data);
          if (res.data.code === 200) {
            this.$message.success('审批成功！');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 撤回
      async handleRepeal() {
        try {
          const res = await getRevocation({
            processInstanceId: this.basic.processInstanceId
          });
          if (res.data.code === 200) {
            this.$message.success('评价撤回成功！');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;
    width: 100%;

    .icon-full {
      position: absolute;
      right: 10px;
      bottom: 7px;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
