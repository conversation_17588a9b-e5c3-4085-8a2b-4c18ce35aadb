<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="list"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column
        fixed
        type="index"
        label="序号"
        width="50"
        align="center"
      >
      </el-table-column>
      <el-table-column fixed label="部门(中心)" prop="deptName" align="center">
      </el-table-column>
      <el-table-column label="姓名" prop="userName" align="center">
      </el-table-column>
      <el-table-column label="岗位" prop="post" align="center">
      </el-table-column>
      <el-table-column label="工号" prop="employeeNumber" align="center">
      </el-table-column>
      <el-table-column label="基本分" prop="basicScore" align="center">
        <template slot-scope="{ row }">
          {{ row.basicScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="系统计算得分"
        prop="systemCalculateScore"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.systemCalculateScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="岗位绩效考核得分"
        prop="assessScore"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.assessScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="备注(点击查看)"
        width="160"
        align="center"
        prop="comment"
      >
        <template slot-scope="scope">
          <div class="show-text" @click="() => handleInput(scope.row)">
            {{ scope.row.comment || '---' }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      width="600px"
      title="备注"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input v-model.trim="tempInput" type="textarea" disabled :rows="12" />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  export default {
    name: 'post-list-detail',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        inputVisited: false,
        tempInput: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      handleInput(row) {
        this.inputVisited = true;
        this.tempInput = row.comment;
      },
      // 完成情况弹窗关闭
      inputClose() {
        this.inputVisited = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
    cursor: pointer;
  }
</style>
