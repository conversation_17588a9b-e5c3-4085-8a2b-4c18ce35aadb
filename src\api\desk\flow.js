import request from '@/router/axios';

export const getRuleList = () => {
  return request({
    url: '/api/attila-desk/leave-rule/record/form-flow-leave',
    method: 'post'
  });
};

export const getTimeInterval = (params) => {
  return request({
    url: '/api/attila-desk/flow/form-flow-hour',
    method: 'get',
    params
  });
};

export const getMakeUpInfo = () => {
  return request({
    url: '/api/attila-desk/flow/form-flow-makeup',
    method: 'get'
  });
};

export const getOvertimeInfo = () => {
  return request({
    url: '/api/attila-desk/flow/form-flow-overtime',
    method: 'get'
  });
};

export const getTripDays = (data) => {
  return request({
    url: '/api/attila-desk/flow/form-flow-business-trip',
    method: 'post',
    data
  });
};

export const getTimeInterval2 = (params) => {
  return request({
    url: '/api/attila-desk/flow/v2/form-flow-hour',
    method: 'get',
    params
  });
};
