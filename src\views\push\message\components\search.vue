<template>
  <div>
    <el-form ref="form" :model="queryParams" :inline="true" size="small">
      <el-form-item label="推送标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入推送标题"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="推送内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入推送内容"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in statusDict"
            :key="dict.value"
            :label="dict.label"
            :value="+dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推送日期" prop="scheduleTime">
        <el-date-picker
          v-model="queryParams.scheduleTime"
          style="width: 100%"
          type="date"
          value-format="yyyy-MM-dd hh:mm:ss"
          placeholder="请输入推送日期"
          autocomplete="off"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  export default {
    name: 'MessagePushSearch',
    data() {
      return {
        statusDict: [
          {
            value: '0',
            label: '未推送'
          },
          {
            value: '1',
            label: '已推送'
          }
        ],
        queryParams: {
          title: '',
          content: '',
          status: '',
          scheduleTime: ''
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        this.$emit('search', this.queryParams);
      }
    }
  };
</script>
<style lang=""></style>
