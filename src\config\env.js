// 阿里矢量图标库配置
// let iconfontVersion = ['567566_pwc3oottzol'];
let iconfontVersion = [
  '567566_pwc3oottzol',
  '1066523_6bvkeuqao36',
  '2059706_ppk64uqnyme'
];
let iconfontUrl = `//at.alicdn.com/t/font_$key.css`;

let baseUrl = '';
let imgUrl = '';
let codeUrl = `${baseUrl}/code`;
let wsUrl = '';
// 文件预览
let preUrl = '';
const env = process.env.VUE_APP_ENV;
if (env === 'development') {
  baseUrl = ``; // 开发环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'http://127.0.0.1:8077/onlinePreview?url=';
} else if (env === 'production') {
  baseUrl = ``; //生产环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'http://127.0.0.1:8077/onlinePreview?url=';
} else if (env === 'test') {
  baseUrl = ``; //测试环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'http://127.0.0.1:8077/onlinePreview?url=';
} else if (env === 'show') {
  baseUrl = ``; // 演示环境地址
  imgUrl = ``;
  wsUrl = 'ws://101.201.140.144:30831/websocket/';
  preUrl = 'http://127.0.0.1:8077/onlinePreview?url=';
}
export {
  baseUrl,
  imgUrl,
  iconfontUrl,
  iconfontVersion,
  codeUrl,
  wsUrl,
  preUrl,
  env
};
