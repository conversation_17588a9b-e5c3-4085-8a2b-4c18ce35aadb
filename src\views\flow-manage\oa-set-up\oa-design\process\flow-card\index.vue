<script>
  import { NodeUtils } from './util.js';
  const isCondition = (data) => data.type === 'condition';
  const notEmptyArray = (arr) => Array.isArray(arr) && arr.length > 0;
  const hasBranch = (data) => notEmptyArray(data.conditionNodes);
  // const stopPro = (ev) => ev.stopPropagation(); // 20221025

  // eslint-disable-next-line no-unused-vars
  function createNormalCard(ctx, conf, h) {
    const classList = ['flow-path-card', 'approver'];
    const afterTrue = (isTrue, name) => (
      isTrue && classList.push(name), isTrue
    );
    // const isStartNode = afterTrue(NodeUtils.isStartNode(conf), 'start-node');
    // console.log(isStartNode); // 20221025
    // afterTrue(NodeUtils.isStartNode(conf), "start-node");
    const isApprNode = afterTrue(NodeUtils.isApproverNode(conf), 'approver');
    const isCopyNode = afterTrue(NodeUtils.isCopyNode(conf), 'copy');
    let isShowTip = null;
    if (conf.properties.approverPerson || conf.properties.ids) {
      isShowTip = (
        <el-tooltip
          content={conf.content}
          placement="top"
          open-delay={500}
          effect="dark"
        >
          <div class="body">
            <span class="text">{conf.content}</span>
            <div class="icon-wrapper right">
              <i class="el-icon-arrow-right icon right-arrow"></i>
            </div>
          </div>
        </el-tooltip>
      );
    } else {
      isShowTip = (
        <div class="body">
          <span class={`${conf.type === 'start' ? '' : 'is-placeholder'} text`}>
            {conf.content}
          </span>
          <div class="icon-wrapper right">
            <i class="el-icon-arrow-right icon right-arrow"></i>
          </div>
        </div>
      );
    }
    return (
      <section
        class={classList.join(' ')}
        onClick={this.eventLancher.bind(ctx, 'edit', conf)}
      >
        <header class="header">
          <div class="title-box" style="height: 100%;width:140px">
            {isApprNode && (
              <i
                class="iconfont iconshenpi"
                style="font-size:12px;color:white;margin-right:4px;"
              ></i>
            )}
            {isCopyNode && (
              <i style="font-size:12px;color:white;margin-right:4px;"></i>
            )}
            <span class="title-text">{conf.properties.title}</span>
          </div>
          <div class="actions">
            <img
              class="icon-close"
              src="/oa/icon-close.png"
              v-oss
              onClick={this.eventLancher.bind(
                ctx,
                'deleteNode',
                conf,
                ctx.data,
                false
              )}
            />
          </div>
        </header>
        {isShowTip}
      </section>
    );
  }
  // arg = ctx, data, h
  const createFunc = (...arg) => createNormalCard.call(arg[0], ...arg);
  let nodes = {
    start: createFunc,
    approver: createFunc,
    copy: createFunc,
    empty: () => '',
    // eslint-disable-next-line no-unused-vars
    condition: function (ctx, conf, h) {
      <i
        class="el-icon-document-copy icon"
        onClick={this.eventLancher.bind(ctx, 'copyNode', conf, ctx.data)}
      ></i>;
      let isShowConditionTip = null;
      if (conf.content !== '条件: 请设置' && conf.content !== '其他情况') {
        isShowConditionTip = (
          <el-tooltip
            content={conf.content}
            open-delay={500}
            placement="top"
            effect="dark"
          >
            <div class="body">
              <pre class="text">{conf.content}</pre>
              <div class="icon-wrapper right">
                <i class="el-icon-arrow-right icon right-arrow"></i>
              </div>
            </div>
          </el-tooltip>
        );
      } else {
        isShowConditionTip = (
          <div class="body">
            <pre
              class={`${
                conf.content === '其他情况' ? '' : 'is-placeholder'
              } text`}
            >
              {conf.content}
            </pre>
            <div class="icon-wrapper right">
              <i class="el-icon-arrow-right icon right-arrow"></i>
            </div>
          </div>
        );
      }
      return (
        <section
          class="flow-path-card condition"
          onClick={this.eventLancher.bind(ctx, 'edit', conf)}
        >
          <header class="header">
            <div class="actions">
              <img
                class="icon-close"
                src="/oa/icon-close.png"
                v-oss
                onClick={this.eventLancher.bind(
                  ctx,
                  'deleteNode',
                  conf,
                  ctx.data
                )}
              />
            </div>
          </header>
          {isShowConditionTip}
        </section>
      );
    }
  };

  function addNodeButton(ctx, data, h, isBranch = false) {
    // 只有非条件节点和条件分支树下面的那个按钮 才能添加新分支树
    let isEmpty = data.type === 'empty';
    if (isEmpty && !isBranch) {
      return '';
    }
    return (
      <div class="add-node-btn-box flex  justify-center">
        <div class="add-node-btn">
          <el-popover
            appendToBody={false}
            placement="right"
            trigger="click"
            class="condition-popover"
            width="140"
          >
            <div class="condition-box">
              <div
                onClick={ctx.eventLancher.bind(
                  ctx,
                  'addApprovalNode',
                  data,
                  isBranch
                )}
              >
                <i class="icon-audit-0 iconfont condition-icon"></i>
                添加审批
              </div>
              <div
                onClick={this.eventLancher.bind(
                  ctx,
                  'appendBranch',
                  data,
                  isBranch
                )}
              >
                <i class="icon-branches-0 iconfont condition-icon"></i>
                添加分支
              </div>
              <div
                onClick={ctx.eventLancher.bind(
                  ctx,
                  'addCopyNode',
                  data,
                  isBranch
                )}
              >
                <i class="icon-send-0 iconfont condition-icon"></i>
                抄送人
              </div>
            </div>

            <button class="btn" type="button" slot="reference">
              <i class="el-icon-plus icon"></i>
            </button>
          </el-popover>
        </div>
      </div>
    );
  }
  function showError(data) {
    let showErrorTip = false;
    if (this.$store.state.oaSetUp.isValidate) {
      showErrorTip = true;
      if (data.type === 'approver' && !data.isCC) {
        if (data.content === '请设置审批人') {
          showErrorTip = true;
        } else {
          showErrorTip = false;
        }
      } else {
        showErrorTip = false;
      }
      if (data.type === 'condition') {
        if (data.content === '条件: 请设置') {
          showErrorTip = true;
        } else {
          showErrorTip = false;
        }
      }
    }
    this.$emit('update-error-number', true);
    return showErrorTip;
  }
  function NodeFactory(ctx, data, h) {
    if (!data) {
      return;
    }
    let showErrorTip = showError.call(this, data);
    let res = [],
      branchNode = '',
      selfNode = (
        <div class={`${data.isCC ? 'isCCNode' : ''} node-wrap`}>
          <div
            class={`node-wrap-box ${data.type} ${showErrorTip ? 'error' : ''}`}
          >
            <el-tooltip
              content="请将信息填写完整"
              placement="top"
              effect="dark"
            >
              <div
                class="error-tip"
                onClick={this.eventLancher.bind(ctx, 'edit', data)}
              >
                <img class="icon-error" src="/oa/icon-error.png" v-oss />
              </div>
            </el-tooltip>
            {nodes[data.type].call(ctx, ctx, data, h)}
            {addNodeButton.call(ctx, ctx, data, h)}
          </div>
        </div>
      );

    if (hasBranch(data)) {
      branchNode = (
        <div class="branch-wrap">
          <div class="branch-box-wrap">
            <div class="branch-box  flex justify-center relative">
              <button
                class="btn"
                onClick={this.eventLancher.bind(
                  ctx,
                  'appendConditionNode',
                  data
                )}
              >
                添加条件
              </button>
              {data.conditionNodes.map((d) => NodeFactory.call(ctx, ctx, d, h))}
            </div>
          </div>
          {addNodeButton.call(ctx, ctx, data, h, true)}
        </div>
      );
    }

    if (isCondition(data)) {
      return (
        <div class="col-box">
          <div class="center-line"></div>
          <div class="top-cover-line"></div>
          <div class="bottom-cover-line"></div>
          {selfNode}
          {branchNode}
          {NodeFactory.call(ctx, ctx, data.childNode, h)}
        </div>
      );
    }
    res.push(selfNode);
    branchNode && res.push(branchNode);
    data.childNode && res.push(NodeFactory.call(ctx, ctx, data.childNode, h));
    return res;
  }
  // eslint-disable-next-line no-unused-vars
  function addStartNode(h) {
    return <section class="end-node start-pointer-node">流程开始</section>;
  }

  // eslint-disable-next-line no-unused-vars
  function addEndNode(h) {
    return <section class="end-node">流程结束</section>;
  }
  export default {
    props: {
      data: { type: Object, required: true },
      // 点击发布时需要校验节点数据完整性 此时打开校验模式
      verifyMode: { type: Boolean, default: true }
    },
    methods: {
      /**
       *事件触发器 统筹本组件所有事件并转发到父组件中
       * @param { Object } 包含event（事件名）和args（事件参数）两个参数
       */
      eventLancher(event, ...args) {
        // args.slice(0,-1) vue 会注入MouseEvent到最后一个参数 去除事件对象
        let param = { event, args: args.slice(0, -1) };
        this.$emit('emits', param);
      }
    },
    // eslint-disable-next-line no-unused-vars
    render(h) {
      return (
        <div
          class="node-wrap-list"
          style="display: inline-flex; flex-direction: column; align-items: center;"
        >
          {addStartNode(h)}
          {this.data && NodeFactory.call(this, this, this.data, h)}
          {addEndNode(h)}
        </div>
      );
    }
  };
</script>

<style lang="stylus" scoped>
  @import 'index.styl';
</style>
