<template>
  <h-select
    v-model="hValue"
    :data-source="options"
    :props="props"
    :placeholder="data.placeholder"
    clearable
  />
</template>

<script>
  export default {
    name: 'RadioComp',
    props: {
      value: {
        type: String
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      props: {
        type: Object,
        default() {
          return { label: 'value' };
        }
      }
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      options() {
        return this.data.options || [];
      }
    }
  };
</script>
