<template>
  <el-dialog
    :title="title"
    :visible.sync="rangeVisible"
    width="544px"
    :append-to-body="true"
    custom-class="range"
    :before-close="handleClose"
  >
    <el-tabs v-show="radio === '1'" v-model="activeName">
      <el-tab-pane label="成员" name="people"></el-tab-pane>
      <el-tab-pane v-if="isJob" label="按岗位" name="dept"></el-tab-pane>
      <!-- <el-tab-pane v-if="isJob" label="按职务" name="job"></el-tab-pane> -->
    </el-tabs>
    <div v-loading="loading" class="content">
      <dept
        v-if="isDept"
        @getDept="getData"
        @setCheckIds="setCheckIds"
        :checked-list="checkList"
        :tree-data="treeData"
        :radio="radio"
        :exclude="exclude"
        :is-radio="isRadio"
        :type="radio === '2' ? 'menu' : radio === '1' ? 'people' : ''"
        :check-strictly="checkStrictly"
        :max-select-length="maxSelectLength"
      ></dept>
    </div>
    <div class="footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button @click="save" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dept from '@/components/dept/dept';
  import { userTree, getList } from '@/api/system/post';
  import { jobUserTree } from '@/api/system/company';
  import {
    getTreeList,
    positionPerson,
    getStaffTreeList
  } from '@/api/system/dept';
  export default {
    components: { dept },
    props: {
      rangeVisible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '选择成员'
      },
      ids: {
        type: Array,
        default: () => []
      },
      radio: {
        type: String,
        default: '1'
      },
      isRadio: {
        type: Boolean,
        default: true
      },
      // 最大选择数量
      maxSelectLength: {
        type: Number,
        default: 0
      },
      // 最小选择数量
      minSelectLength: {
        type: Number,
        default: 0
      },
      // 是否显示按岗位按职务
      isJob: {
        type: Boolean,
        default: true
      },

      // 用于圈定人员范围
      nodeId: {
        type: String,
        default: ''
      },
      id: {
        type: String,
        default: ''
      },
      // 禁止选中某项
      exclude: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        activeName: 'people',
        selectList: [],
        treeData: [],
        checkList: [],
        loading: false,
        isDept: true,
        checkStrictly: false
      };
    },
    watch: {
      activeName() {
        this.treeData = [];
        this.checkList = [];
        this.loading = true;
        const url = {
          people: getStaffTreeList,
          dept: userTree,
          job: jobUserTree
        };
        url[this.activeName](this.nodeId, this.id)
          .then((data) => {
            if (data && data.data && data.data.success) {
              this.treeData = data.data.data;
            } else if (data && data.data && !data.data.success) {
              this.$message.error(data.data.message);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      rangeVisible() {
        if (this.rangeVisible) {
          this.checkList = this.ids || [];
          this.isDept = true;
          if (this.radio === '3') {
            this.checkStrictly = true;
          }
          this.getData();
        } else {
          this.activeName = 'people';
          this.isDept = false;
          this.checkStrictly = false;
        }
      },
      radio() {
        this.treeData = [];
        this.getData();
      }
    },
    mounted() {
      this.getData();
    },
    methods: {
      async getData() {
        let data = '';
        this.loading = true;
        try {
          if (this.radio === '4') {
            data = await getList();
          } else if (this.radio === '3') {
            data = await getTreeList();
          } else if (this.radio === '2') {
            data = await positionPerson();
          } else if (this.radio === '1') {
            data = await getStaffTreeList(this.nodeId, this.id);
          }
          if (data && data.data && data.data.success) {
            this.treeData = data.data.data;
            if (this.radio === '4') {
              this.treeData = data.data.data.records || [];
            }
          } else if (data && data.data && !data.data.success) {
            this.$message.error(data.data.message);
          }
          this.loading = false;
        } catch {
          this.treeData = [];
          this.loading = false;
        }
      },
      setCheckIds(list) {
        this.selectList = list;
      },
      save() {
        if (['dept', 'job'].includes(this.activeName)) {
          this.selectList.forEach((li) => {
            li.id = li.userId;
          });
        }
        if (
          this.minSelectLength &&
          this.minSelectLength > this.selectList.length
        ) {
          this.$message.error('人数过少，请重新选择');
          return;
        }
        if (
          this.maxSelectLength &&
          this.maxSelectLength < this.selectList.length
        ) {
          this.$message.error(`最多选择${this.maxSelectLength}个成员`);
          return;
        }
        this.$emit('rangeSave', this.selectList);
        this.handleClose();
      },
      handleClose() {
        this.$emit('update:rangeVisible', false);
      }
    }
  };
</script>
<style lang="scss">
  .el-dialog__wrapper .range .el-dialog__header {
    padding-bottom: 17px !important;
  }

  .range {
    .el-tabs--top {
      padding-right: 24px;
      padding-left: 24px;

      .el-tabs__header {
        margin-top: 20px;
        margin-bottom: 0;
      }
    }

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      padding: 0 !important;

      .header-tip {
        margin-bottom: 4px;
        padding-bottom: 16px;
        padding-left: 24px;
        border-bottom: 1px solid #d9d9d9;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88px;

      button {
        width: 140px;
        height: 40px;
      }
    }
  }
</style>
