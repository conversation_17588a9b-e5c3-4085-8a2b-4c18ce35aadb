<template>
  <el-scrollbar style="height: 556px">
    <div class="table_wrapper" v-if="source && source.length">
      <el-card v-for="item in source" :key="item.id" shadow="never">
        <div slot="header">
          <span>
            <el-tag
              v-if="item.hasRead === 0"
              size="small"
              effect="plain"
              type="warning"
              >未读</el-tag
            >
            <el-tag
              v-else-if="item.hasRead === 1"
              size="small"
              effect="plain"
              type="success"
              >已读</el-tag
            >
            <el-tag v-else size="small" effect="plain">--</el-tag>
            <span class="card_title_time">{{ item.createTime }}</span>
          </span>
          <el-button
            style="float: right; padding: 3px 0"
            type="text"
            @click="$emit('dispatch', 'view', item)"
            >查看详情 <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
        <div>
          <div class="card_body_title">{{ item.title }}</div>
          <div class="card_body_content">{{ item.content }}</div>
        </div>
      </el-card>
    </div>
    <el-empty v-else></el-empty>
  </el-scrollbar>
</template>

<script>
  export default {
    name: 'MessagePushTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    filters: {
      statusFilter(status) {
        switch (status) {
          case 0:
            return '未读';
          case 1:
            return '已读';
          default:
            return '--';
        }
      }
    },
    data() {
      return {};
    },
    methods: {}
  };
</script>

<style lang="scss" scoped>
  .table_wrapper {
    .el-card {
      ::v-deep .el-card__header {
        padding: 12px 18px !important;

        .el-tag {
          padding: 0 15px;
          border-radius: 12px;
        }

        .card_title_time {
          margin-left: 15px;
          color: #999;
          font-size: 12px;
        }
      }

      ::v-deep .el-card__body {
        padding: 12px 18px;

        .card_body_title {
          margin-bottom: 10px;
          color: #2c405a;
          font-size: 16px;
        }

        .card_body_content {
          overflow: hidden;
          color: #3f536e;
          font-size: 14px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      & + .el-card {
        margin-top: 10px;
      }
    }
  }
</style>
