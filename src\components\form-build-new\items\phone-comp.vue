<template>
  <el-input
    v-model="hValue"
    :maxlength="maxlength"
    show-word-limit
    :placeholder="data.placeholder"
    clearable
  />
</template>

<script>
  export default {
    name: 'PhoneComp',
    props: {
      value: {
        type: String,
        default: ''
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      maxlength() {
        return this.data.phoneType === 3 ? 11 : 13;
      }
    }
  };
</script>
