<template>
  <div class="wrapper">
    <div class="nav_header">
      <h3>基本信息</h3>
    </div>
    <el-form label-width="100px" label-suffix="：" size="small">
      <el-row :gutter="15">
        <el-col :span="6">
          <el-form-item label="期间类型" style="margin-bottom: 0">
            {{ form.type | filterType }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年" style="margin-bottom: 0">
            {{ form.yearly || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="开始时间" style="margin-bottom: 0">
            {{ form.startTime || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结束时间" style="margin-bottom: 0">
            {{ form.endTime || '--' }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="nav_header">
      <h3>周期明细</h3>
    </div>
    <content-table :detail="detail" :source="source" />
    <div class="footer_button">
      <el-button type="primary" size="small" @click="back">返回</el-button>
    </div>
  </div>
</template>

<script>
  import ContentTable from './content-table';
  import { getDetail } from '@/api/examine/cycle';
  export default {
    name: 'detail',
    props: {
      id: {
        type: String,
        default: ''
      },
      detail: {
        type: Boolean,
        default: false
      }
    },
    components: {
      ContentTable
    },
    data() {
      return {
        form: {},
        source: []
      };
    },
    mounted() {
      if (this.id) {
        this.editInfo();
      }
    },
    filters: {
      filterType(type) {
        switch (type) {
          case 1:
            return '月度';
          case 2:
            return '季度';
          case 3:
            return '半年度';
          case 4:
            return '年度';
          default:
            return '--';
        }
      }
    },
    methods: {
      async editInfo() {
        const res = await getDetail(this.id);
        const data = res.data.data;
        this.form = data;
        this.source = data.items;
      },
      back() {
        this.$emit('close', false);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 10px 30px;

    .nav_header {
      position: relative;
      margin-bottom: 20px;
      border-bottom: 1px solid #dfdfdf;

      h3 {
        margin: 10px 0;
        padding-left: 12px;
        font-size: 17px;
        border-left: 5px solid #1e9fff;
      }
    }

    .footer_button {
      margin: 30px 0 0;
      text-align: center;
    }
  }
</style>
