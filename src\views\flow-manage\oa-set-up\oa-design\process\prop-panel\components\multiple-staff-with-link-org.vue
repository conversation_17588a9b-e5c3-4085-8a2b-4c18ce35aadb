<template>
  <el-dialog
    :title="title"
    :visible.sync="rangeVisible"
    width="544px"
    :append-to-body="true"
    custom-class="multiple-staff-with-link-org"
    :before-close="handleClose"
  >
    <el-tabs slot="title" v-model="tab" @tab-click="changeTab">
      <el-tab-pane label="本组织" name="1" />
      <!-- <el-tab-pane name="2">
        <span slot="label">
          关联组织
          <el-tooltip
            effect="dark"
            content="抄送给本组织所关联的直接上、下级组织中的某类或某个人员。"
            placement="top"
          >
            <i class="el-icon-question"></i>#CFCFCF
          </el-tooltip>
        </span>
      </el-tab-pane> -->
    </el-tabs>
    <div v-if="tab === '2'" class="select-link-org">
      <span>选择组织：</span>
      <h-select
        v-model="orgId"
        v-loading="relationLoading"
        @change="handleOrgIdChange"
        :data-source="relationDepts"
        :props="deptProps"
        :placeholder="relationDepts.length ? '请选择' : '暂无关联组织'"
        size="small"
      />
    </div>
    <el-tabs
      v-model="activeName"
      @tab-click="changeTabs"
      class="tab-type"
      :class="{ lower: tab === '2' }"
    >
      <el-tab-pane label="成员" name="people"></el-tab-pane>
      <el-tab-pane label="部门" name="dept"></el-tab-pane>
      <el-tab-pane v-if="isHide" label="岗位" name="position"></el-tab-pane>
      <!-- <el-tab-pane v-if="isHide" label="职务" name="job"></el-tab-pane> -->
    </el-tabs>
    <div v-loading="loading" class="content">
      <select-tree
        v-if="!reloadFlag"
        :checked-list.sync="searchList"
        :tree-data="treeData"
        :radio="activeName"
        :check-strictly="checkStrictly"
        :max-select-length="maxSelectLength"
        :tips="tab === '1' || orgId ? '' : '选中组织后，加载对应数据'"
      ></select-tree>
    </div>
    <div class="footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button @click="save" type="primary">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex';
  // import { deepClone } from '@/util/util.js';
  import { getList } from '@/api/system/post';
  import { jobList } from '@/api/system/company';
  import {
    getTreeList,
    getStaffTreeList,
    getLinkDepts
  } from '@/api/system/dept';

  import selectTree from './select-tree';

  export default {
    components: { selectTree },
    props: {
      rangeVisible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '选择'
      },
      ids: {
        type: Array,
        default: () => []
      },
      radio: {
        type: String,
        default: 'people'
      },
      isHide: {
        type: Boolean,
        default: true
      },
      // 选择的最大数量，0为不限制数量
      maxSelectLength: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        tab: '1',
        activeName: 'people',
        searchList: [],
        treeData: [],
        loading: false,
        isDept: true,
        checkStrictly: false,
        orgId: '',
        relationLoading: false,
        relationDepts: [],
        deptProps: {
          value: 'orgId',
          label: 'orgName'
        },
        reloadFlag: false
      };
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    watch: {
      rangeVisible: {
        handler(val) {
          if (val) {
            this.searchList = this.ids || [];
          } else {
            this.activeName = 'people';
            this.getData();
          }
        },
        immediate: true
      }
    },
    created() {
      this.getLinkDepts();
      this.getData();
    },
    methods: {
      getLinkDepts() {
        this.relationLoading = true;
        getLinkDepts()
          .then((res) => {
            this.relationLoading = false;
            this.relationDepts = res.data.data || [];
          })
          .catch(() => {
            this.relationLoading = false;
          });
      },
      async getData() {
        this.treeData = [];
        this.searchList = [];
        let data = {};
        this.loading = true;
        try {
          switch (this.activeName) {
            case 'people':
              data = await getStaffTreeList(null, null, this.orgId);
              break;
            case 'dept':
              data = await getTreeList(this.orgId);
              break;
            case 'position':
              data = await getList(this.orgId);
              break;
            case 'job':
              data = await jobList(this.orgId);
              break;
          }
          if (data && data.data && data.data.success) {
            this.treeData = data.data.data;
          } else if (data && data.data && !data.data.success) {
            this.$message.error(data.data.message);
          }
          this.loading = false;
        } catch {
          this.loading = false;
        }
      },
      changeTab(tab) {
        this.clearSelected();
        this.orgId = '';
        this.activeName = 'people';
        if (tab.name === '1') {
          this.getData();
        } else {
          this.treeData = [];
          this.searchList = [];
        }
      },
      changeTabs(tab) {
        this.activeName = tab.name;
        (this.tab === '1' || this.orgId) && this.getData();
      },
      save() {
        let orgId, orgName;
        if (this.orgId) {
          orgId = this.orgId;
          let org = this.relationDepts.find((item) => item.orgId === orgId);
          orgName = org ? org.orgName : '';
        } else {
          orgId = this.userInfo.activeCompanyId;
          orgName = this.userInfo.activeCompanyName;
        }
        const list = this.searchList.map((item) => {
          return { ...item, orgId, orgName };
        });
        this.$emit('range-save', list);
        this.handleClose();
      },
      handleClose() {
        this.searchList = [];
        this.$emit('update:rangeVisible', false);
      },
      handleOrgIdChange(val) {
        this.clearSelected();
        val && this.getData();
      },
      clearSelected() {
        this.reloadFlag = true;
        this.$nextTick(() => {
          this.reloadFlag = false;
        });
      }
    }
  };
</script>
<style lang="scss">
  .el-dialog__wrapper {
    .el-dialog {
      &.multiple-staff-with-link-org {
        .el-dialog__header {
          padding: 0 !important;
          border: 0;

          .el-tabs__item {
            height: 63px;
            line-height: 63px;

            .el-icon-question {
              color: #cfcfcf;
            }
          }

          .el-tabs--top {
            & > div {
              margin-bottom: 0;
            }

            .el-tabs__nav-wrap {
              padding-left: 24px;
            }
          }
        }

        .el-dialog__body {
          display: flex;
          flex-direction: column;
          padding: 0 !important;

          .tab-type {
            .el-tabs__header {
              margin-top: 0 !important;
              margin-bottom: 0 !important;

              .el-tabs__nav-wrap {
                padding: 0;

                .el-tabs__nav {
                  .el-tabs__item {
                    height: 45px;
                    padding: 0 13px;
                    line-height: 45px;
                  }
                }

                &::after {
                  background: none;
                  border: none;
                }
              }
            }
          }
        }

        .footer {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 88px;

          button {
            width: 140px;
            height: 40px;
          }
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  .multiple-staff-with-link-org {
    .select-link-org {
      display: flex;
      align-items: center;
      padding: 12px 24px 0;

      .h-select {
        flex: 1;
      }
    }

    .tab-type {
      position: absolute;
      box-sizing: border-box;
      width: 240px;
      margin-top: 12px;
      margin-left: 24px;
      padding: 0 12px;
      border-bottom: 1px solid #e4e7ed;

      &.lower {
        top: 108px;
      }
    }
  }
</style>
