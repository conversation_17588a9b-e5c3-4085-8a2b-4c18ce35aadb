import request from '@/router/axios';

// 部门业绩绩效结果单 - 详情
export function getDetail(params) {
  return request({
    url: '/api/examine/performance-form-dept/detail',
    method: 'get',
    params
  });
}

// 部门业绩绩效结果单 - 保存
export function postSave(data) {
  return request({
    url: '/api/examine/performance-form-dept/save',
    method: 'post',
    data
  });
}

// 部门业绩绩效结果单 - 提交
export function postSubmit(data) {
  return request({
    url: '/api/examine/performance-form-dept/submit',
    method: 'post',
    data
  });
}

// 部门业绩绩效结果单 - 审批
export const postApprove = (data) => {
  return request({
    url: `/api/examine/performance-form-dept/approve`,
    method: 'post',
    data
  });
};

// 部门业绩绩效结果单 - 撤销
export const getRepeal = (id) => {
  return request({
    url: `/api/examine/performance-form-dept/revocation?processInstanceId=${id}`,
    method: 'get'
  });
};

// 部门业绩绩效结果单 - 导出
export function getImport(params) {
  return request({
    url: '/api/examine/performance-form-dept/export',
    method: 'get',
    params
  });
}
