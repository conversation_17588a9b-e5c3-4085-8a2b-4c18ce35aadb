<template>
  <div>
    <process
      @start-node-change="onStartChange"
      @change-error-number="hasApprovel"
      @add-approve-node="addApproveNode"
      @delete-approve-node="deleteApproveNode"
      :conf="processData"
      :node-fields.sync="nodeFields"
    />
  </div>
</template>
<script>
  import './index.scss';
  import process from './process';
  import { deepClone } from '@/util/util.js';
  import { Base64 } from 'js-base64';
  import { message } from '@/components/message';
  let nodeArray = [],
    conditionArray = [],
    errorType = true;
  const getNodes = (node) => {
    if (node.type === 'approver') {
      nodeArray.push(node);
    } else if (node.type === 'condition') {
      conditionArray.push(node);
    }
    if (node.childNode) {
      getNodes(node.childNode);
    }
    if (node.conditionNodes && node.conditionNodes.length) {
      node.conditionNodes.forEach((item) => {
        getNodes(item);
      });
    }
  };
  // 重新构建审批抄送人数据
  const createJson = (dataObj) => {
    errorType = true;
    const obj = deepClone(dataObj);
    // 前端要用的json
    const json = { nodeNewJson: Base64.encode(JSON.stringify(deepClone(obj))) };
    const infoMessage = {};
    // 获取所有的抄送人节点
    const traverse = (obj) => {
      let copyApplyNodeId = '';
      const copyApplyList = [];
      const traverseChildNode = (obj) => {
        // 存所有的抄送人数据
        if (obj.isCC && obj.properties.values) {
          const list = obj.properties.values.map(({ id, type, orgId }) => {
            return { id, type, orgId };
          });
          copyApplyList.push(...list);
        }
        // 取当前分支的第一个审批人nodeId
        if (obj.type === 'approver' && !obj.isCC && !copyApplyNodeId) {
          copyApplyNodeId = obj.nodeId;
        }

        // 如果有childNode 递归，没有的话存到json里面
        if (obj.childNode) {
          traverseChildNode(obj.childNode);
        } else {
          infoMessage[copyApplyNodeId || 'noApplyPeople'] = copyApplyList;
        }

        // 有conditionNodes的话循环递归
        if (obj.conditionNodes) {
          obj.conditionNodes.forEach((cnObj) => {
            traverse(cnObj);
          });
        }
      };
      traverseChildNode(obj);
    };
    traverse(obj);
    Object.keys(infoMessage).forEach((info) => {
      if (!infoMessage[info].length) {
        delete infoMessage[info];
      }
    });
    json.infoMessage = infoMessage;

    // 获取所有的非抄送人节点
    const filterCopy = (obj) => {
      // if (
      //   obj.childNode &&
      //   obj.childNode.isCC &&
      //   obj.childNode.conditionNodes &&
      //   errorType
      // ) {
      //   errorType = false;
      //   message.error("条件分支上方不能存在抄送人");
      //   return;
      // }
      if (obj.conditionNodes) {
        obj.conditionNodes.forEach((cnObj) => {
          filterCopy(cnObj);
        });
      }
      if (obj.childNode) {
        if (obj.childNode.isCC) {
          let childNode = obj.childNode.childNode;
          let conditionNodes = obj.childNode.conditionNodes;
          if (childNode) {
            childNode.prevId = obj.childNode.prevId;
            obj.childNode = childNode;
          } else {
            delete obj.childNode;
          }
          if (conditionNodes) {
            conditionNodes.forEach((c) => (c.prevId = obj.childNode.prevId));
            obj.conditionNodes = conditionNodes;
          }
          filterCopy(obj);
        } else if (obj.childNode) {
          filterCopy(obj.childNode);
        }
      }
    };
    filterCopy(obj);
    json.processJson = obj;
    return json;
  };
  const getAllNode = (list) => {
    let temp = [];
    const getNode = (array) => {
      array.forEach((item) => {
        temp.push(item);
        if (item.children && item.children.length) {
          getNode(item.children);
        }
      });
    };
    getNode(list);
    return temp;
  };
  const READONLY = '1';
  import { getMockData, getApproverNodeIds } from './process/flow-card/util.js';
  import { GROUP_TYPES } from '@/components/form-build-new/const/index';
  export default {
    components: { process },
    props: {
      oaDesignDetails: {
        type: Object,
        default: () => {}
      },
      ruleList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      let processData = getMockData();
      let idList = getApproverNodeIds(processData);
      return {
        processData,
        childNode: {},
        index: 0,
        json: [],
        typeList: [],
        nodeFields: idList.map((id) => {
          return { nodeId: id, fieldStatus: {} };
        }),
        baseFieldMap: {},
        fieldInitFlag: false
      };
    },
    computed: {
      formDesignList() {
        return this.$store.state.oaSetUp.formDesignList;
      }
    },
    watch: {
      formDesignList: {
        handler(val) {
          this.typeFilter();
          if (
            (!this.$route.query.id || this.fieldInitFlag) &&
            val &&
            val.length
          ) {
            let fieldIds = val
              .filter(
                ({ id, type }) => id && type && !GROUP_TYPES.includes(type)
              )
              .map(({ id }) => id);

            let oldFieldIds = Object.keys(this.baseFieldMap);

            let addIdList = fieldIds.filter(
              (id) => !oldFieldIds.find((item) => item === id)
            );
            let delIdList = oldFieldIds.filter(
              (id) => !fieldIds.find((item) => item === id)
            );

            delIdList.forEach((id) => {
              delete this.baseFieldMap[id];
              this.nodeFields = this.nodeFields.map((item) => {
                delete item.fieldStatus[id];
                return item;
              });
            });
            addIdList.forEach((id) => {
              this.baseFieldMap[id] = READONLY;
              this.nodeFields = this.nodeFields.map((item) => {
                item.fieldStatus[id] = READONLY;
                return item;
              });
            });
          }
        },
        immediate: true,
        deep: true
      },
      oaDesignDetails: {
        handler(val) {
          if (val.personsAll) {
            let arrAll = JSON.parse(val.personsAll);
            let values = [];
            let type = ['people', 'dept', 'position', 'job'];
            let typeList = [
              'informUserIds',
              'informDeptIds',
              'informPostIds',
              'informJobIds'
            ];
            let obj = {
              informUserIds: 'label',
              informDeptIds: 'title',
              informPostIds: 'postName',
              informJobIds: 'jobName'
            };
            // 将四个ids转成各自类型的数组对象
            arrAll.map((item) => {
              if (typeList.includes(item.type)) {
                let arr = [];
                item.valueList.map((i) => {
                  arr.push({
                    dataType:
                      type[typeList.indexOf(item.type)] === 'people' ? 3 : 1,
                    id: i.id,
                    type: type[typeList.indexOf(item.type)],
                    name: i.name
                  });
                });
                arr.map((l) => {
                  l[obj[item.type]] = l.name;
                  delete l.name;
                });
                values = [...values, ...arr];
              }
            });
            let contentIntro = []; // 流程页面显示
            values.map((item) => {
              if (item.type === 'people') {
                contentIntro.push('成员：' + item.label + ',');
              } else if (item.type === 'dept') {
                contentIntro.push('部门：' + item.title + ',');
              } else if (item.type === 'position') {
                contentIntro.push('岗位：' + item.postName + ',');
              } else if (item.type === 'jobName') {
                contentIntro.push('职务：' + item.jobName + ',');
              }
            });
            this.childNode = {
              title: '抄送',
              isCC: true,
              type: 'approver',
              content: contentIntro.join(''),
              properties: {
                ids: arrAll,
                title: '抄送',
                values: values
              }
            };
          }
          if (val.processJson) {
            let processJson = JSON.parse(val.processJson);

            // 有val.personsAll为以前的老数据，需要单独兼容
            this.processData = val.personsAll
              ? this.setPersonNode(processJson)
              : processJson;
            this.$forceUpdate();
          }
          let map = {};
          this.formDesignList.forEach(({ id, type }) => {
            if (!GROUP_TYPES.includes(type)) {
              map[id] = READONLY;
            }
          });
          this.baseFieldMap = map;
          if (val.nodeFieldList && val.nodeFieldList.length) {
            this.nodeFields = deepClone(val.nodeFieldList);
          } else {
            this.nodeFields = getApproverNodeIds(this.processData).map((id) => {
              return { nodeId: id, fieldStatus: deepClone(this.baseFieldMap) };
            });
          }
          this.$nextTick(() => {
            this.fieldInitFlag = true;
          });
        },
        deep: true
      }
    },
    methods: {
      addApproveNode(id) {
        this.nodeFields.push({
          nodeId: id,
          fieldStatus: deepClone(this.baseFieldMap)
        });
      },
      deleteApproveNode(id) {
        this.nodeFields = this.nodeFields.filter(({ nodeId }) => nodeId !== id);
      },
      // 父组件调用的校验方法
      validate(isClick) {
        let num = 0;
        nodeArray = [];
        conditionArray = [];
        const { processJson } = createJson(this.processData);
        if (!errorType) {
          isClick = true;
          num++;
        }
        getNodes(processJson);
        nodeArray.map((item) => {
          if (
            item.type === 'approver' &&
            !item.isCC &&
            item.content === '请设置审批人'
          ) {
            num++;
          }
        });
        conditionArray.map((item) => {
          if (!item.childNode && !item.conditionNodes) {
            !isClick && message.error('条件分支下需要审批节点');
            num++;
          }
          if (item.type === 'condition' && item.content === '条件: 请设置') {
            num++;
          }
        });
        // 根据num来判断有无错误
        return num;
      },
      onStartChange(node) {
        this.processData = node;
      },
      typeFilter() {
        // 根据组件判断条件显示哪些
        let tList = this.formDesignList.filter((l) => {
          return (
            ([
              'checkbox',
              'radio',
              'inputMoney',
              'inputNumber',
              'computed'
            ].includes(l.type) &&
              l.valueJson.required) ||
            ['rest', 'work', 'out', 'trip'].includes(l.type)
          );
        });
        let arr = [];
        let rList = this.ruleList;
        tList.forEach((item) => {
          // rest4、work4、out3、trip8
          let types = ['rest1', 'rest4', 'work4', 'out3', 'trip8'];
          if (['rest', 'work', 'out', 'trip'].includes(item.type)) {
            let list = getAllNode(item.children);
            let oneObj = list.filter((i) => types.includes(i.id));
            if (oneObj) {
              oneObj.forEach(({ type, id, valueJson }) => {
                arr.push({
                  type,
                  id,
                  name: valueJson.name || '',
                  valueList: id === 'rest1' ? rList : ''
                });
              });
            }
          } else {
            arr.push({
              type: item.type || '',
              id: item.id || '',
              name: item.valueJson.name || '',
              valueList: item.valueJson.options || []
            });
          }
        });
        arr.unshift({
          type: 'selectPerson',
          id: 'startUser',
          name: '发起人'
        });
        this.typeList = [...arr];
        this.getConditionNodes(this.processData);
      },
      // 递归processData判断表单设计中有改变就清空content
      getConditionNodes(node) {
        if (node.childNode) {
          this.getConditionNodes(node.childNode);
        }
        if (node.conditionNodes && node.conditionNodes.length) {
          node.conditionNodes.forEach((item) => {
            if (item.content !== '其他情况') {
              let that = this;
              item.properties.conditions.forEach((i) => {
                i.forEach((m) => {
                  let arr = that.typeList.filter(
                    (n) => n.id === m.form.condition
                  );
                  if (arr.length) {
                    if (['checkbox'].includes(m.type) && arr[0].valueList) {
                      let ab = [];
                      arr[0].valueList.map((o) => {
                        ab.push(o.value);
                      });
                      if (m.form.value.length) {
                        m.form.value.forEach((p) => {
                          if (!ab.includes(p)) {
                            m.form.value = '';
                            item.content = '条件: 请设置';
                          }
                        });
                      }
                    } else if (['radio'].includes(m.type) && arr[0].valueList) {
                      if (m.form.condition !== 'rest1') {
                        let brr = arr[0].valueList.filter(
                          (o) => o.value === m.form.value
                        );
                        if (!brr.length) {
                          m.form.value = '';
                          item.content = '条件: 请设置';
                        }
                      }
                    }
                    if (arr[0].name !== m.name) {
                      item.content = '条件: 请设置';
                    }
                  } else if (m.form.condition !== 'startUser') {
                    m.form.value = '';
                    item.content = '条件: 请设置';
                  }
                });
              });
            }
            this.getConditionNodes(item);
          });
        }
      },
      // 子组件的错误信息抛到父组件
      hasApprovel() {
        if (this.$store.state.oaSetUp.isValidate) {
          this.$nextTick(() => {
            this.$emit('update-error-number', true);
          });
        }
      },
      // 递归加入抄送人节点
      setPersonNode(data) {
        let { childNode } = data;
        if (childNode && Object.keys(childNode).length) {
          data['childNode'] = this.setPersonNode(childNode);
        } else if (Object.keys(this.childNode).length) {
          data['childNode'] = this.childNode;
        }
        return data;
      },
      submit() {
        let form = {};
        let obj = createJson(this.processData);
        if (
          Object.keys(obj.processJson).length === 0 ||
          (obj.processJson.type === 'start' &&
            !obj.processJson.childNode &&
            !obj.processJson.conditionNodes)
        ) {
          form = '';
        } else {
          form = {
            ...obj,
            processJson: Base64.encode(JSON.stringify(obj.processJson)),
            infoMessage: JSON.stringify(obj.infoMessage),
            nodeFieldList: this.nodeFields
          };
        }
        return form;
      }
    }
  };
</script>
