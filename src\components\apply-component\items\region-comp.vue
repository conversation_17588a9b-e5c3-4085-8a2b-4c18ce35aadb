<template>
  <div>
    <el-col :span="12">
      <el-form-item :label="`${data.name}：`">
        <common-comp :value="hValue" />
      </el-form-item>
    </el-col>
    <el-col v-if="data.provinceType === 2" :span="12">
      <el-form-item label="街道：">
        <common-comp :value="value.street" />
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
  import CommonComp from './common-comp';

  export default {
    name: 'RegionComp',
    components: { CommonComp },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    computed: {
      hValue() {
        let { provinceName = '', cityName = '', countyName = '' } = this.value;
        return `${provinceName}${cityName ? '-' + cityName : ''}${
          countyName ? '-' + countyName : ''
        }`;
      }
    }
  };
</script>
