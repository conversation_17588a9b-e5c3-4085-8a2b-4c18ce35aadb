<template>
  <div>
    <!-- <el-alert :title="title" type="warning" show-icon :closable="false" /> -->
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column
        fixed
        type="index"
        label="序号"
        width="50"
        align="center"
      >
      </el-table-column>
      <el-table-column
        fixed
        label="部门名称"
        prop="deptName"
        width="200"
        align="center"
      >
      </el-table-column>
      <el-table-column label="经济效益(30%)" align="center">
        <el-table-column label="利润总额(50%)" align="center">
          <el-table-column
            label="原始分"
            prop="profitOriginalScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.profitOriginalScore || '---'
            }}</template>
          </el-table-column>
          <el-table-column
            label="折算分"
            prop="profitConvertScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.profitConvertScore || '---'
            }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="商品煤单位完全成本(50%)" align="center">
          <el-table-column
            label="原始分"
            prop="costOriginalScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.costOriginalScore || '---'
            }}</template>
          </el-table-column>
          <el-table-column
            label="折算分"
            prop="costConvertScore"
            align="center"
            width="120px"
          >
            <template slot-scope="{ row }">{{
              row.costConvertScore || '---'
            }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="折算分小计"
          prop="economyConvertScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.economyConvertScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="KPI指标(40%)" align="center">
        <el-table-column
          label="原始分"
          prop="kpiOriginalScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.kpiOriginalScore || '---'
          }}</template>
        </el-table-column>
        <el-table-column
          label="折算分"
          prop="kpiConvertScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.kpiConvertScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="工作效能(30%)" align="center">
        <el-table-column
          label="原始分"
          prop="workOriginalScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.workOriginalScore || '---'
          }}</template>
        </el-table-column>
        <el-table-column
          label="折算分"
          prop="workConvertScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.workConvertScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="部门建设(扣分项)" align="center">
        <el-table-column
          label="原始分"
          prop="deptOriginalScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.deptOriginalScore || '---'
          }}</template>
        </el-table-column>
        <el-table-column
          label="扣分"
          prop="deptDeductScore"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">{{
            row.deptDeductScore || '---'
          }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="年度重点工作" align="center" width="150px">
        <template slot-scope="{ row }">
          <el-input-number
            v-model.number="row.annualTaskScore"
            size="small"
            controls-position="right"
            style="width: 100%"
            :precision="1"
            :step="0.1"
            @change="() => systemSum(row)"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column prop="systemCalculateScore" align="center" width="120px">
        <div slot="header">
          系统计算得分
          <el-tooltip
            class="item"
            effect="dark"
            content="系统计算得分=【经济效益】折算得分+【KPI指标】折算得分+【工作效能】折算得分-【部门建设】扣分+年度重点工作"
            placement="top"
            ><i class="el-icon-question"></i
          ></el-tooltip>
        </div>
        <template slot-scope="{ row }">{{
          row.systemCalculateScore || '---'
        }}</template>
      </el-table-column>
      <el-table-column
        label="本周期考核得分"
        width="160"
        prop="assessScore"
        align="center"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.assessScore"
            size="small"
            controls-position="right"
            style="width: 100%"
            :min="0"
            :precision="2"
            :step="0.01"
            @change="handleScoreChange"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column label="名次" width="50" align="center" prop="rank">
      </el-table-column>
      <el-table-column label="备注(点击输入)" align="center" prop="comment">
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput(scope.row)"
          >
            {{
              scope.row.comment && scope.row.comment.length
                ? scope.row.comment
                : '点击输入内容'
            }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--  同步文本输入  -->
    <el-dialog
      width="600px"
      title="备注"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        placeholder="请输入备注"
        maxlength="50"
        show-word-limit
        :rows="8"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
        <el-button size="small" type="primary" @click="inputSave"
          >完 成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getDeptsRank } from '../utils';
  import { cloneDeep } from 'lodash';
  import { mapState } from 'vuex';
  import { decimalPointFn } from '@/util/util';
  export default {
    name: 'depart-list',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      list: {
        handler(newVal) {
          this.tempList = cloneDeep(newVal);
          // this.countFunction();
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        tempList: [],
        count: 0,
        // 弹出框
        inputVisited: false,
        temp: null,
        tempInput: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      },
      title() {
        return `共 ${this.tempList.length} 个被评价机构 ，已评价 ${this.count} 个`;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      // 弹窗打开
      handleInput(row) {
        this.inputVisited = true;
        this.temp = row;
        this.tempInput = row.comment;
      },
      // 弹窗关闭
      inputClose() {
        this.inputVisited = false;
        this.temp = {};
      },
      // 保存
      inputSave() {
        this.tempList.map((item) => {
          if (item.id === this.temp.id) {
            item.comment = this.tempInput;
          }
          return item;
        });
        this.$emit('sync', this.tempList);
        this.inputVisited = false;
      },
      // 考核得分改变时, 重新计算已评价机构和排名, 并同步数据
      handleScoreChange() {
        // this.countFunction();
        this.tempList = getDeptsRank(this.tempList);
        // 同步数据
        this.$emit('sync', this.tempList);
      },
      // 系统计算得分
      // 【经济效益】折算得分+【KPI指标】折算得分+【工作效能】折算得分-【部门建设】扣分+年度重点工作
      systemSum(row) {
        if (typeof row.annualTaskScore !== 'number') {
          row.annualTaskScore = null;
        }
        const economyConvertScore = Number(row.economyConvertScore) || 0;
        const kpiConvertScore = Number(row.kpiConvertScore) || 0;
        const workConvertScore = Number(row.workConvertScore) || 0;
        const deptDeductScore = Number(row.deptDeductScore) || 0;
        const annualTaskScore = Number(row.annualTaskScore) || 0;

        const systemCalculateScore =
          economyConvertScore +
          kpiConvertScore +
          workConvertScore -
          deptDeductScore +
          annualTaskScore;
        row.systemCalculateScore = decimalPointFn(systemCalculateScore);
        this.handleScoreChange();
      }
      // 统计评价状态
      // countFunction() {
      // let count = 0;
      // // 重新计算已评价机构数
      // this.tempList.forEach((item) => {
      //   if (typeof item.assessScore === 'number') {
      //     count += 1;
      //   }
      // });
      // this.count = count;
      // }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }
  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
  }
</style>
