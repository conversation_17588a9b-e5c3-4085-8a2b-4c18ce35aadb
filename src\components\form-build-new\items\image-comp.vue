<template>
  <h-image-upload
    ref="upload"
    v-model="hValue"
    multiple
    show-loading
    show-file-list
    :limit="5"
    :disabled="disabled"
    :image-width="80"
    :image-height="80"
    class="bc-form-build-image"
  >
  </h-image-upload>
</template>

<script>
  export default {
    name: 'ImageComp',
    props: {
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
          if (this.$parent.$options.componentName === 'ElFormItem') {
            this.$parent.$emit('el.form.change', val);
          }
        }
      }
    }
  };
</script>

<style lang="scss">
  .bc-form-build-image {
    &.h-image-upload {
      .h-image-upload-multiple {
        .image-area {
          .image-list {
            .image-block {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
</style>
