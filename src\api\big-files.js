import request from '@/router/axios';

/**
 * 根据文件的md5获取未上传完的任务
 * @param identifier 文件md5
 * @returns {Promise<AxiosResponse<any>>}
 */
export const taskInfo = (identifier) => {
  return request({
    url: `/api/minio/tasks/${identifier}`,
    method: 'get'
  });
};

/**
 * 初始化一个分片上传任务
 * @param identifier 文件md5
 * @param fileName 文件名称
 * @param totalSize 文件大小
 * @param chunkSize 分块大小
 * @returns {Promise<AxiosResponse<any>>}
 */
export const initTask = ({ identifier, fileName, totalSize, chunkSize }) => {
  return request({
    url: '/api/minio/tasks',
    method: 'post',
    data: {
      identifier,
      fileName,
      totalSize,
      chunkSize
    }
  });
};

/**
 * 获取预签名分片上传地址
 * @param identifier 文件md5
 * @param partNumber 分片编号
 * @returns {Promise<AxiosResponse<any>>}
 */
export const preSignUrl = ({ identifier, partNumber }) => {
  return request({
    url: `/api/minio/tasks/${identifier}/${partNumber}`,
    method: 'get'
  });
};

/**
 * 合并分片
 * @param identifier
 * @returns {Promise<AxiosResponse<any>>}
 */
export const merge = (identifier) => {
  return request({
    url: `/api/minio/tasks/merge/${identifier}`,
    method: 'post'
  });
};

/**
 * 文件下载
 * @param taskId
 * @return {Promise<AxiosResponse<any>>}
 */
export const tasksDownload = (taskId) => {
  return request({
    url: `/api/minio/tasks/download/${taskId}`,
    method: 'get'
  });
};
