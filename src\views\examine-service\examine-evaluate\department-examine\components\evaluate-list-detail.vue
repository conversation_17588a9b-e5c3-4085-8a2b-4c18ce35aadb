<template>
  <el-table
    style="width: 100%"
    border
    size="small"
    v-bind="bindProps()"
    :data="list"
    :cell-style="fontStyle"
    :header-cell-style="fontStyle"
  >
    <el-table-column type="index" label="序号" width="50" align="center" />
    <el-table-column label="被评价机构" width="250" align="center">
      <template slot-scope="scope">
        {{ scope.row.deptName || '---' }}
      </template>
    </el-table-column>
    <el-table-column label="原始分" align="center">
      <template slot-scope="scope">
        {{ scope.row.originalScore | scoreFilter }}
      </template>
    </el-table-column>
    <el-table-column label="扣分" align="center">
      <template slot-scope="scope">
        {{ scope.row.deductScore | scoreFilter }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { mapState } from 'vuex';
  export default {
    name: 'evaluate-list-detail',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      }
    }
  };
</script>
