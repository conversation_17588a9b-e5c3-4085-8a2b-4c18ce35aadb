<template>
  <el-form
    v-loading="loading"
    ref="form"
    :model="form"
    :rules="rules"
    label-width="100px"
    size="small"
  >
    <el-row>
      <el-col :span="24">
        <el-form-item label="上级菜单">
          <yk-menu-select
            v-model="form.parentId"
            placeholder="默认主目录"
            type="APP"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="菜单类型" prop="category">
          <el-radio-group v-model="form.category">
            <el-radio :label="1">菜单</el-radio>
            <el-radio :label="2">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          :label="form.category === 1 ? '菜单名称' : '按钮名称'"
          prop="name"
        >
          <el-input
            v-model="form.name"
            placeholder="请输入菜单名称"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          :label="form.category === 1 ? '菜单编号' : '权限字符'"
          prop="code"
        >
          <el-input
            v-model="form.code"
            placeholder="请输入"
            maxlength="100"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category !== 2">
        <el-form-item label="路由地址" prop="path">
          <el-input
            v-model="form.path"
            placeholder="请输入路由地址"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.category === 1">
        <el-form-item prop="componentPath">
          <span slot="label">
            <el-tooltip
              content="访问的组件路径，如：`system/user/index`，默认在`views`目录下"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
            组件路径
          </span>
          <el-input
            v-model="form.componentPath"
            placeholder="请输入组件路径"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <el-input-number
            style="width: 100%"
            v-model="form.sort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注">
          <el-input type="textarea" :row="3" v-model="form.remark" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import YkIconSelect from '@/components/yk-icon-select';
  import YkMenuSelect from '@/components/yk-menu-select';
  import { getMenu } from '@/api/system/menu';

  const rules = {
    name: {
      required: true,
      message: '请输入菜单名称',
      trigger: 'blur'
    },
    code: {
      required: true,
      message: '请输入菜单编号',
      trigger: 'blur'
    },
    sort: {
      required: true,
      message: '请输入菜单排序',
      trigger: 'blur'
    },
    path: {
      required: true,
      message: '请输入路由地址',
      trigger: 'blur'
    }
  };
  export default {
    inheritAttrs: false,
    name: 'customize-form',
    props: {
      isEdit: {
        type: Boolean,
        default: false
      },
      id: {
        type: String,
        default: ''
      },
      childId: {
        type: String,
        default: ''
      }
    },
    watch: {
      'form.category'(val) {
        if (val === 1) {
          this.form.alias = 'menu';
        } else {
          this.form.alias = 'button';
        }
      }
    },
    components: {
      YkIconSelect,
      YkMenuSelect
    },
    data() {
      return {
        rules,
        form: {
          parentId: '1',
          category: 1,
          name: '',
          code: '',
          path: '',
          componentPath: '',
          sort: '',
          remark: '',
          alias: 'menu'
        },
        loading: false
      };
    },
    mounted() {
      if (this.isEdit) {
        this.init();
      }
      if (this.childId.length) {
        this.form.parentId = this.childId;
      }
    },
    methods: {
      // 编辑获取初始数据
      async init() {
        this.loading = true;
        try {
          const res = await getMenu(this.id);
          this.form = res.data.data;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 表单校验
      validateFn() {
        return new Promise((resolve, reject) => {
          this.$refs.form.validate((valid) => {
            if (valid) {
              resolve(this.form);
            } else {
              reject(new Error('please complete the options'));
            }
          });
        });
      }
    }
  };
</script>
