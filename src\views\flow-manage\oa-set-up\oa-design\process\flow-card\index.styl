$bg-color = #f5f5f7;
$line-color = #CACACA
$base-size = 12px

// Mixin flex 垂直居中布局
flex-center()
    display flex
    flex-wrap nowrap
    justify-content center
    align-items center

//  三点省略 支持单行多行
// Mixin { n:Number } n：省略行数限制
ellipsis(n)
    overflow: hidden;
    text-overflow: ellipsis;
    if n > 1
        display: -webkit-box;
        -webkit-line-clamp: n;
        -webkit-box-orient: vertical;
    else
        white-space: nowrap;

// 按钮变大效果
btn-bigger()
    transform: scale(1.2);
    box-shadow: 0 8px 16px 0 rgba(0,0,0,.1);
.node-wrap-box {
  position: relative;
  flex-center()
  flex-direction column
  &.condition{
      padding 30px 20px 0
  }
  .card-list {
    position: absolute;
    top: -8px;
    right: -7px;
    color: #FF5151;
  }
  &.approver::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    width: 0;
    height: 4px;
  }
  &.error {
    .error-tip{
      left: 10px;
    }
    &.condition .error-tip{
      display: block;
    }
    .flow-path-card {
      border: 1px solid #FF5151;
    }
  }

  .error-tip {
    position: absolute;
    left: 60px;
    top: 0;
    width: 16px;
    height: 16px;
    color: #FF5151;
    cursor: pointer;
    background: transparent;
    // line-height: 30px;
    transition: right .5s;
    .icon-error{
      width: 16px;
      height: 16px;
    }
  }

  &.condition .error-tip {
    top: 30px;
    left: 32px;
    display: none;
  }
}
.start-pointer-node{
  margin-bottom: 40px;
  position: relative;
  background: #3B7CFF !important;
  &::before {
    content: '';
    position: absolute;
    top: 32px;
    left: 0;
    right: 0;
    z-index: -1;
    margin: auto;
    width: 2px;
    height: 40px;
    background-color: #cacaca;
  }
}
.end-node {
  width: 100px;
  height: 32px;
  color: #fff;
  background: #B1BBCF;
  border-radius: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: $base-size;
  text-align: center;
  flex-center()
  flex-direction column
}
.flow-path-card {
  width: 160px;
  min-height: 32px;
  font-size: 12px !important;
  border-radius: 4px;
  text-align: left;
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
  // box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
  background #FFF

  &.copy{
    .header{
      background-color #3296fa
    }
  }

  &.approver{
    // box-shadow: 0 0 0 4px #B1BBCF, 0 0 5px 4px rgba(0, 0, 0, 0.2);
    background #B1BBCF
    padding 4px
    .header{
      background-color #B1BBCF
    }
  }

  &.start-node{
    box-shadow: 0 0 0 4px #B1BBCF, 0 0 5px 4px rgba(0, 0, 0, 0.2);
    .header{
      background-color #B1BBCF
      padding-left 5px
    }
    .right-arrow {
      display: none;
    }
    .body {
      padding-left: 12px;
      padding-right: 12px;
    }
    .text {
      text-align: center;
    }
  }


  .header {
    // padding-left: 8px;
    padding-right: 30px;
    margin-bottom: 0;
    width: 100%;
    height: 24px;
    color: white;
    position: relative;
    box-sizing: border-box;
    // border-radius: 4px 4px 0 0;

    .title-box {
      position: relative;
      display: inline-block;
    }

    .title-input {
      position: absolute;
      left: 0;
      border: none;
      background: inherit;
      color: inherit;
      opacity: 0;

      &:focus {
        border-radius: 6px;
        font-size: $base-size;
        padding: 2px;
        padding-left: 4px;
        width: 97%;
        margin-left: 1px;
        height: 18px;
        box-sizing: border-box;
        box-shadow: 0 0 2px 2px #7ec3e1;
        background-color: $bg-color;
        color: black;
        opacity: 1;
      }
    }

    > .actions {
      position: absolute;
      right: -12px;
      top: -12px;
      width: 16px;
      height: 16px;
      visibility: hidden;
      .icon-close {
        width: 16px;
        height: 16px;
      }
      .icon-process_no {
        color: #FF4648;
      }
    }
  }

  &:not(.start-node):hover {
    .actions {
      visibility: visible;
    }

    .title-text {
      // border-bottom: 1px dashed currentColor;
    }
  }

  .body {
    position: relative;
    padding: 12px;
    padding-right: 30px;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: 4px;

    .text {
      margin 0
      ellipsis(2)

    }
    .is-placeholder{
      color #ccc
    }
  }

  .icon-wrapper {
    position: absolute;
    top: 0;
    height: 100%;
    width: 14px;
    box-sizing: border-box;

    &.left {
      left: 0;
    }

    &.right {
      right: 12px;
    }

    > {
      .right-arrow, .left-arrow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.flow-path-card.condition {
  height: 54px;
  box-sizing: border-box;
  padding: 5px 16px 5px 12px;
  border: 1px solid #B1BBCF;
  .header {
    height: 0;
    margin: 0;
    border: 0;
    color: inherit;
    padding: 0;
    .title-text{
        color #15bc83;
    }
    > .actions {
      position: absolute;
      right: -24px;
      top: -16px;
      width: 16px;
      height: 16px;
      .icon-close {
        width: 16px;
        height: 16px;
      }
    }
  }

  .body {
    width: 100%;
    height: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    .text{
      display: block;
      width: 104px;
      ellipsis(2)
    }
    .icon-wrapper {
      right: 0;
    }
  }

  .icon-wrapper {
    &:hover {
      // background-color: #f1f1f1;
    }
  }
}

.col-box:first-of-type .condition .left
    display none
.col-box:last-of-type .condition .right
    display none

.add-node-btn-box {
  width: 220px;
  height: 50px;
  position: relative;
  padding-top: 30px;
  margin auto

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    margin: auto;
    width: 2px;
    height: 100%;
    background-color: $line-color;
  }



  .add-node-btn {
    display: flex;
    justify-content: center;
    &>span {
      position: relative;
      color: red;
    }
    /deep/ .condition-popover {
      .el-popover {
        padding: 2px 16px !important;
        box-sizing: border-box;
      }
    }

    .btn {
      width: 21px;
      height: 21px;
      border-radius: 15px;
      cursor: pointer;
      border: 1px solid #409EFF;
      background: #fff;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: 0.2s;
        &:hover {
          transition: 0.2s;
          border-width: 2px;
          .icon {
            transition: 0.2s;
            font-weight: bold
          }
        }
      .icon {
        color: #409EFF;
      }
    }
  }
}

.branch-wrap
    margin-top: -11px;
    .branch-box-wrap
        display: inline-flex;
        flex-direction: column;
        align-items: center;
    .branch-box
        align-items: stretch;
        border-bottom: 2px solid $line-color;
        border-top: 2px solid $line-color;
        box-sizing: border-box;
        background #F0F2F5
        > .col-box
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            &:first-of-type
                &::before, &::after
                    content ""
                    position: absolute;
                    left: 0;
                    height: 3px;
                    width: calc(50% - 1px);
                    background: #F0F2F5;
                &::before
                    top: -2px;
                &::after
                    bottom: -2px;
            &:last-of-type
                &::before, &::after
                        content ""
                        position: absolute;
                        right: 0;
                        height: 3px;
                        width: calc(50% - 1px);
                        background: #F0F2F5;
                    &::before
                        top: -2px;
                    &::after
                        bottom: -2px;

            .center-line
                height: 100%;
                width: 2px;
                background: $line-color;
                position absolute
        > .btn
            z-index 99
            cursor pointer
            position: absolute;
            top: 0;
            outline none
            transform: translateY(-50%);
            padding: 6px 22px;
            border: none;
            border-radius: 25px;
            border: 1px solid #409EFF
            background: white;
            font-size: 14px;
            color: #409EFF;
            // box-shadow: 0 0 10px 0px rgba(0, 0, 0, 0.2);
            transition transform .3s
            &:hover
                box-shadow: 0 0 10px 0px rgba(0, 0, 0, 0.2);
                // transform scale(1.1) translateY(-50%)

.condition-box
    display: flex;
    justify-content: space-around;
    flex-direction:column;
    font-size: 14px;
    color: #333;
    .no-condition {
      display: none;
    }
    div
      padding-left: 4px;
      padding-right: 4px;
      cursor: pointer;
      line-height: 32px;
      &:hover {
        color: #409EFF;
      }
    .condition-icon
        margin-right: 14px;
        cursor pointer
        margin-bottom 4px
.relative
    position relative

.flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.icon {
  vertical-align: middle;
  width: 14px;
  height: 14px;
  font-size 14px

}

.priority{
    position absolute
    right 0
    font-size 12px
}

.node-wrap-list {
  .branch-box {
    .node-wrap:last-of-type {
      .approver {
        .actions {
          display: block;
        }
      }
    }
  }
  .branch-wrap {
    .branch-box-wrap {
      .branch-box {
        .col-box:last-of-type {
          >.node-wrap:not(.branch-wrap) {
            >.node-wrap-box.condition {
              >.flow-path-card.condition {
                cursor: default;
                .header {
                  .actions {
                    display: none;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.el-popover {
  padding: 0!important;
}
