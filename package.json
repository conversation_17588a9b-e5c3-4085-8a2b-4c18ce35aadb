{"name": "saber-admin", "version": "2.0.0", "updateDate": "000000", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "build:show": "vue-cli-service build --mode show", "lint": "vue-cli-service lint", "lint:eslint": "eslint --fix --ext .js,.vue src", "lint:stylelint": "stylelint 'src/**/*.(vue|css|less|scss)' --fix", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@smallwei/avue": "^2.9.5", "avue-plugin-ueditor": "^0.1.4", "axios": "^0.18.0", "axios-extra": "0.0.6", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "crypto-js": "^4.0.0", "element-ui": "^2.15.6", "html2canvas": "^1.0.0-rc.7", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "lodash": "^4.17.21", "mockjs": "^1.0.1-beta3", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "pdfjs-dist": "2.5.207", "portfinder": "^1.0.23", "print-js": "^1.5.0", "promise-queue-plus": "^1.2.2", "qrcodejs2": "^0.0.2", "script-loader": "^0.7.2", "sortablejs": "^1.15.0", "spark-md5": "^3.0.2", "stylus": "^0.54.8", "stylus-loader": "^3.0.2", "vue": "^2.6.10", "vue-amap": "^0.5.10", "vue-axios": "^2.1.2", "vue-i18n": "^8.7.0", "vue-jsonp": "^2.0.0", "vue-pdf": "4.2.0", "vue-router": "^3.0.1", "vue-slicksort": "^1.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^8.2.2", "chai": "^4.1.2", "eslint": "^4.19.1", "eslint-config-prettier": "^8.5.0", "eslint-loader": "^2.1.2", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^4.7.1", "html-webpack-plugin": "^4.5.0", "husky": "3.0.9", "lint-staged": "9.4.3", "mini-css-extract-plugin": "^2.6.1", "node-sass": "^6.0.1", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.5", "prettier": "^2.6.2", "sass-loader": "^10.0.5", "stylelint": "^14.5.3", "stylelint-config-clean-order": "^0.8.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-scss": "^5.0.2", "stylelint-config-recommended-vue": "^1.3.0", "stylelint-config-standard-scss": "^3.0.0", "stylelint-scss": "^4.3.0", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"], "src/**/*.{nvue,vue,less,css,scss}": ["git add"]}}