<template>
  <el-dialog
    title="选择图标"
    :visible.sync="addImgVisible"
    width="408px"
    :append-to-body="true"
    custom-class="add-img"
    :before-close="handleClose"
  >
    <div class="content">
      <p>系统图标</p>
      <span v-for="key in 17" :key="key">
        <img
          v-oss
          @click="selectImg(img(key - 1))"
          :src="img(key - 1)"
          :class="{ 'img-click': emitSrc === img(key - 1) }"
        />
        <img
          v-if="emitSrc === img(key - 1)"
          v-oss
          class="checkFill"
          src="/oa/checkFill.png"
        />
      </span>
      <p>自定义图标</p>
      <span v-for="key in 30" :key="`1-${key}`">
        <img
          v-oss
          @click="selectImg(img(key + 16))"
          :src="img(key + 16)"
          :class="{ 'img-click': emitSrc === img(key + 16) }"
        />
        <img
          v-if="emitSrc === img(key + 16)"
          v-oss
          class="checkFill"
          src="/oa/checkFill.png"
        />
      </span>
    </div>
    <div class="footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button @click="save" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    props: {
      addImgVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        emitSrc: ''
      };
    },
    computed: {
      img() {
        return (key) => {
          return `/${key > 16 ? 'apply' : 'oa'}/icon_diy_${key}.png`;
        };
      }
    },
    methods: {
      selectImg(src) {
        this.emitSrc = src;
      },
      handleClose() {
        this.$emit('update:addImgVisible', false);
        this.emitSrc = '';
      },
      save() {
        this.$emit('emit-src', this.emitSrc);
        this.handleClose();
      }
    }
  };
</script>
<style lang="scss">
  .add-img {
    .el-dialog__body {
      display: flex;
      flex-direction: column;
      height: 432px;
      padding: 12px 0 12px 12px !important;
    }

    .content {
      flex: 1;
      overflow-y: scroll;

      span {
        position: relative;

        img:first-child:not(.img-click):hover {
          padding: 3px;
          border: 1px solid #409eff;
          border-radius: 50%;
        }
      }

      .checkFill {
        position: absolute;
        right: 5px;
        bottom: 12px;
        width: 16px;
        height: 16px;
        margin: 0;
        padding: 0;
      }

      p {
        margin: 12px;
        color: #333;
        font-size: 14px;
        line-height: 14px;
      }

      .img-click {
        padding: 2px;
        border: 2px solid #409eff;
        border-radius: 50%;
      }

      img {
        width: 40px;
        height: 40px;
        margin: 8px;
        padding: 4px;
        cursor: pointer;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 64px;

      button {
        width: 140px;
        height: 40px;
      }
    }
  }
</style>
