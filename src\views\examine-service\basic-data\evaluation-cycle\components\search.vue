<template>
  <el-form ref="form" :inline="true" :model="form" size="small">
    <el-form-item label="周期类型" prop="type">
      <el-select
        v-model="form.type"
        style="width: 200px"
        clearable
        placeholder="请选择周期类型"
      >
        <el-option
          v-for="dict in serviceDicts.type.period_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="年度" prop="yearly">
      <el-date-picker
        v-model="form.yearly"
        type="year"
        value-format="yyyy"
        :editable="false"
        style="width: 200px"
        placeholder="选择年度"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item label="开始日期" prop="startTime">
      <el-date-picker
        v-model="form.startTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 200px"
        :editable="false"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item label="结束日期" prop="endTime">
      <el-date-picker
        v-model="form.endTime"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 200px"
        :editable="false"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="handleQuery"
        >查询</el-button
      >
    </el-form-item>
    <el-form-item>
      <el-button icon="el-icon-delete" @click="onReset">清空</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
  export default {
    name: 'search',
    serviceDicts: ['period_type'],
    data() {
      return {
        form: {
          type: undefined, // 考核周期
          yearly: undefined, // 年度
          startTime: undefined, // 开始时间
          endTime: undefined // 结束时间
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      handleQuery() {
        this.$emit('query', this.form);
      },
      onReset() {
        this.$refs.form.resetFields();
        this.handleQuery();
      }
    }
  };
</script>
