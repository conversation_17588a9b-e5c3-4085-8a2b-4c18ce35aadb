<template>
  <el-table
    :data="tableData"
    border
    style="width: 100%"
    size="small"
    @selection-change="selectChange"
  >
    <el-table-column type="selection" width="55"> </el-table-column>
    <el-table-column type="index" label="序号" width="50" align="center" />
    <el-table-column prop="code" label="部门绩效单号" align="center">
    </el-table-column>
    <el-table-column prop="periodItemName" label="考核周期" align="center">
    </el-table-column>
    <el-table-column prop="submitDate" label="提报日期" align="center">
      <template slot-scope="scope">
        {{ scope.row.submitDate || '---' }}
      </template>
    </el-table-column>
    <el-table-column prop="status" label="单据状态" align="center">
      <template slot-scope="scope">
        {{ scope.row.status | statusFilter }}
      </template>
    </el-table-column>
    <el-table-column prop="examineStatus" label="审核状态" align="center">
      <template slot-scope="scope">
        {{ scope.row.examineStatus | changeExamineStatus }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="220" align="center">
      <template slot-scope="scope">
        <el-button
          type="text"
          size="small"
          icon="el-icon-view"
          @click="$emit('dispatch', 'detail', scope.row)"
          >查看</el-button
        >
        <el-button
          v-if="scope.row.canApprove"
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'exam', scope.row)"
          >审核</el-button
        >
        <el-button
          v-if="scope.row.status == '1'"
          type="text"
          size="small"
          icon="el-icon-edit-outline"
          @click="$emit('dispatch', 'edit', scope.row)"
          >编辑</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-list',
    props: {
      tableData: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    filters: {
      statusFilter(val) {
        switch (val) {
          case '1':
            return '未提交';
          case '2':
            return '已提交';
          case '3':
            return '已完成';
          default:
            return '---';
        }
      }
    },
    methods: {
      // 选择
      selectChange(selection) {
        const arr = selection.map((item) => {
          return item.code;
        });
        this.$emit('dispatch', 'selection', {
          selections: arr
        });
      }
    }
  };
</script>
