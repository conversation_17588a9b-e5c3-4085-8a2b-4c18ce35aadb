<!--
 * @Author: your name
 * @Date: 2021-08-31 09:58:59
 * @LastEditTime: 2021-12-20 15:48:04
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /AttilaPortal/src/components/form-build-new/items/peopleComp.vue
-->
<template>
  <div class="bc-form-build-people">
    <el-input
      @click.native="onClick"
      :value="names"
      :placeholder="data.placeholder"
      readonly
      :title="names"
      :size="data.inputSize"
      :disabled="disabled"
      :class="{ readonly: data.readonly }"
    />
    <select-people
      @rangeSave="onChange"
      :range-visible.sync="visible"
      :is-radio="data.peopleSelect === 1"
      :ids="ids"
    ></select-people>
  </div>
</template>

<script>
  import SelectPeople from '@/components/select-tree/select-people';

  export default {
    name: 'PeopleComp',
    components: { SelectPeople },
    props: {
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false
      };
    },
    computed: {
      names() {
        return Array.from(this.value, (item) => item.name).join('，');
      },
      ids() {
        return Array.from(this.value, (item) => item.id);
      }
    },
    methods: {
      onClick() {
        if (!this.disabled && !this.data.readonly) {
          this.visible = true;
        }
      },
      onChange(list = []) {
        this.$emit(
          'input',
          Array.from(list, (item) => {
            return {
              id: item.userId,
              name: item.label,
              parentId: item.parentId,
              parentName: item.parentName
            };
          })
        );
      }
    }
  };
</script>

<style lang="scss">
  .bc-form-build-people {
    .el-input {
      &.readonly {
        .el-input__inner {
          cursor: not-allowed;
        }
      }
      .el-input__inner {
        cursor: pointer;
      }
    }
  }
</style>
