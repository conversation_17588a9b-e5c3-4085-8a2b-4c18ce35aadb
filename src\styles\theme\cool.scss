/* stylelint-disable selector-pseudo-element-no-unknown */
.theme-cool {
  .el-menu--popup{
      .el-menu-item{
          background-color: #fff;

          i,span{
              color: #666;
          }

          &:hover{
              i,span{
                  color: #333;
              }
          }

          &.is-active {
              background-color: #409EFF;

              &::before {
                  position: absolute;
                  top: 0;
                  bottom: 0;
                  left: 0;
                  width: 4px;
                  background: #409eff;
                  content: '';
              }

              i,span{
                  color: #fff;
              }
          }
      }
  }

.avue-header{
  background: linear-gradient(120deg,#25aff3,#008ad3);
}

.avue-tags {
  margin: 8px 0;
  padding: 0 3px;
  background-color: transparent;
  box-shadow: none;

  .el-tabs__header .el-tabs__item{
    height: 30px;
    margin-right: 5px;
    padding: 0 10px !important;
    color: #909399;
    font-size: 12px;
    line-height: 30px;
    background-color: #fff;
    border-radius: 3px;

     &.is-active {
      color: #008ad3;
      border: none;
     }
  }
}

.avue-logo{
  background: #fff;
  box-shadow: none;
}

.avue-sidebar--tip{
  color: #333;
  background-color: transparent;
}

.el-dropdown{
  color: #fff;
}

.avue-logo_title{
  color: #008ad3;
  font-weight: 400;
}

.logo_title,
.avue-breadcrumb
{
    color: #fff ;

    i {
        color: #fff;
    }
}

.avue-top{
  .el-menu-item {
    i,
    span {
        color: #fff ;
    }

    &:hover {
        i,
        span {
            color: #fff ;
        }
    }
  }
}

.avue-sidebar{
  padding-top: 70px;
  background-color: #fff;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 15%);

  .el-menu-item,.el-submenu__title{
    font-size: 13px;

    i,span{
        color: #000;
    }

    &:hover{
        background: transparent;

        i,span{
           color: #000;
        }
    }

    &.is-active {
      background-color: #e5f1fb;

      &::before{
        width: 0;
      }

        i,span{
          color: #25aff3;
        }
    }
  }
}

.top-search {
    .el-input__inner{
      color: #333;
    }

    input::input-placeholder,
    textarea::input-placeholder {
        /* WebKit browsers */
        color: #fff;
    }

    input:placeholder,
    textarea:placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #fff;
    }

    input::placeholder,
    textarea::placeholder {
        /* Mozilla Firefox 19+ */
        color: #fff;
    }

    input:input-placeholder,
    textarea:input-placeholder {
        /* Internet Explorer 10+ */
        color: #fff;
    }
}

.top-bar__item {
    i {
        color: #fff;
    }
}
}