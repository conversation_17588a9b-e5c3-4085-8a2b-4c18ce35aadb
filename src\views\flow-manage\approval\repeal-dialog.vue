<template>
  <el-dialog
    title="提示"
    :visible.sync="repealVisible"
    width="30%"
    :before-close="handleClose"
    append-to-body
    custom-class="apply-confirm"
  >
    <el-input
      v-model="applyOpinion"
      type="textarea"
      :maxlength="50"
      show-word-limit
      placeholder="请输入销假原因(必填)"
    >
    </el-input>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返 回</el-button>
      <el-button @click="submit" type="primary">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
  export default {
    props: {
      repealVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        applyOpinion: ''
      };
    },
    watch: {
      repealVisible() {
        this.applyOpinion = '';
      }
    },
    methods: {
      submit() {
        if (!this.applyOpinion) {
          this.$message.error('请输入销假原因');
          return;
        }
        this.$emit('submit', this.applyOpinion);
      },
      handleClose() {
        this.$emit('update:repealVisible', false);
      }
    }
  };
</script>
