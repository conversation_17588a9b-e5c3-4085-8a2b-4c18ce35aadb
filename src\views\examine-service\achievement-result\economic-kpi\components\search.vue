<template>
  <div class="info_form_mb5">
    <el-form
      ref="form"
      inline
      label-suffix="："
      label-width="120px"
      size="small"
      :model="form"
    >
      <el-row :gutter="12">
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="综合绩效单号" prop="code">
            <el-input
              placeholder="请输入综合绩效单号"
              v-model.trim="form.code"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="考核周期" prop="periodItemId">
            <el-input
              class="disable_cursor"
              placeholder="请选择考核周期"
              :value="periodItemName"
              @click.native="periodDialogShow = true"
              style="width: 200px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="单据状态" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择单据状态"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type.performance_form_status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="审批状态" prop="examineStatus">
            <el-select
              v-model="form.examineStatus"
              placeholder="请选择审批状态"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type.process_status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item label="提报日期" prop="submitDate">
            <el-date-picker
              v-model="form.submitDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择提报日期"
              style="width: 100%"
              :editable="false"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="8">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >清空</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <period-select
      v-if="periodDialogShow"
      :selectPeriodId="form.periodItemId"
      @dialogSave="handleDialogSave"
      @dialogClose="periodDialogShow = false"
    />
  </div>
</template>

<script>
  import { PeriodSelect } from '@/views/examine-service/components/period-select';
  import { scheme_result_type } from '@/constant/service';

  export default {
    name: 'search',
    components: { PeriodSelect },
    serviceDicts: ['performance_form_status', 'process_status'],
    data() {
      return {
        // 考核周期属性
        periodDialogShow: false,
        periodItemName: undefined,
        // 表单
        form: {
          code: undefined,
          periodItemId: undefined,
          status: undefined,
          examineStatus: undefined,
          submitDate: undefined,
          type: scheme_result_type.econ_kpi
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      // 接收选中的考核周期
      handleDialogSave(data) {
        this.periodDialogShow = false;
        this.form.periodItemId = data.id;
        this.periodItemName = data.itemName;
      },
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.periodItemName = undefined;
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        this.$emit('search', this.form);
      }
    }
  };
</script>
