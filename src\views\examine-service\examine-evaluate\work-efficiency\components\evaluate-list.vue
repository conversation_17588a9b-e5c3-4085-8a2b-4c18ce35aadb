<template>
  <el-table
    :data="list"
    border
    style="width: 100%"
    row-key="id"
    class="table_wrapper_sp"
  >
    <el-table-column label="排名" width="180" align="center">
      <template slot-scope="scope">
        {{ `第${scope.row.rank}名` }}
      </template>
    </el-table-column>
    <el-table-column prop="deptName" label="被评价机构" align="center" />
    <el-table-column prop="score" label="得分" align="center" />
  </el-table>
</template>

<script>
  import SortTable from 'sortablejs';

  export default {
    name: 'evaluate-list',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      },
      maxScore: {
        type: Number,
        default: 0
      }
    },
    mounted() {
      this.rowDrop();
    },
    methods: {
      // 行拖拽
      rowDrop() {
        // 要侦听拖拽响应的DOM对象
        const tbody = document.querySelector(
          '.table_wrapper_sp .el-table__body-wrapper tbody'
        );
        const that = this;
        SortTable.create(tbody, {
          animation: 180,
          delay: 0,
          // 结束拖拽后的回调函数
          onEnd({ newIndex, oldIndex }) {
            const currentRow = that.list.splice(oldIndex, 1)[0];
            that.list.splice(newIndex, 0, currentRow);
            that.$emit(
              'update:list',
              that.list.map((item, i) => {
                item.score = that.maxScore - i;
                item.rank = i + 1;
                return item;
              })
            );
          }
        });
      }
    }
  };
</script>
