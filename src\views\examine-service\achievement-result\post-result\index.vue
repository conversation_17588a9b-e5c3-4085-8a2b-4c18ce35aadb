<template>
  <basic-container>
    <transition-group name="fade">
      <div v-show="!open" :key="1">
        <search @search="searchQuery" />
        <div style="margin-bottom: 10px">
          <el-button
            type="warning"
            size="small"
            icon="el-icon-download"
            :disabled="disabled"
            @click="handleExport"
            >导 出</el-button
          >
        </div>
        <table-list
          v-loading="loading"
          :tableData="tableData"
          @dispatch="handleEvent"
        />
        <yk-pagination
          small
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.current"
          :limit.sync="queryParams.size"
          @pagination="getList"
        />
      </div>
      <yk-local-model :key="2" :title="title" :open="open" @close="handleClose">
        <exam
          :row-code="rowCode"
          :is-approval="isApproval"
          :is-detail="isDetail"
          @close="handleClose"
          @refresh="getList"
        />
      </yk-local-model>
    </transition-group>
  </basic-container>
</template>

<script>
  import { Search, TableList } from './components';
  import { downloadUrl, downloadFile } from '@/util/download';
  import Exam from './exam.vue';
  import { getPageList } from '@/api/examine/economic-kpi-result';

  export default {
    name: 'postResult',
    components: {
      Search,
      TableList,
      Exam
    },
    data() {
      return {
        open: false,
        // 表格参数
        queryParams: {
          current: 1,
          size: 10
        },
        total: 10,
        loading: false,
        tableData: [],
        // model参数
        rowCode: '',
        title: '',
        isDetail: false,
        isApproval: false,
        // 导出
        selections: []
      };
    },
    methods: {
      // 导出
      handleExport() {
        const url = downloadUrl('post_download', '', {
          codes: this.selections.toString()
        });
        downloadFile(url);
      },
      // 查询
      searchQuery(param) {
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          param
        );
        this.getList();
      },
      // 请求列表数据
      async getList() {
        try {
          this.loading = true;
          const res = await getPageList(this.queryParams);
          const data = res.data.data;
          this.total = data.total;
          this.tableData = data.records;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 监听表格事件
      handleEvent(type, { code, selections }) {
        if (code) {
          this.open = true;
          this.rowCode = code;
        }
        switch (type) {
          case 'detail':
            this.title = '查看';
            this.isDetail = true;
            this.isApproval = false;
            break;
          case 'edit':
            this.title = '编辑';
            this.isDetail = false;
            this.isApproval = false;
            break;
          case 'exam':
            this.title = '审核';
            this.isDetail = true;
            this.isApproval = true;
            break;
          case 'selection':
            this.selections = selections;
            break;
          default:
            break;
        }
      },
      // model关闭
      handleClose(val) {
        this.open = val;
      }
    },
    computed: {
      disabled() {
        return !this.selections.length;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .page-height {
    box-sizing: border-box;
    height: calc(100vh - 125px);
    overflow-y: auto;
  }
</style>
