import request from '@/router/axios';

export const getPage = ({ current, size, ...params }) => {
  return request({
    url: '/api/demo/order/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};
export const getDetail = (id) => {
  return request({
    url: '/api/demo/order/fetchById',
    method: 'get',
    params: {
      id
    }
  });
};

export const postDel = (params) => {
  return request({
    url: '/api/demo/order/deleteById',
    method: 'post',
    params
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-demo/order/remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const addOrUpdate = (row) => {
  return request({
    url: '/api/demo/order/save',
    method: 'post',
    data: row
  });
};

export const submit = (row) => {
  return request({
    url: '/api/demo/order/save',
    method: 'post',
    data: row
  });
};
