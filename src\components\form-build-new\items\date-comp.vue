<template>
  <div class="bc-form-build-date">
    <el-date-picker
      v-model="hValue1"
      :placeholder="data.placeholder"
      :type="type"
      :value-format="format"
      :format="format"
      clearable
      :class="{ 'short-date-editor': isSplit }"
    />
    <h-select
      v-if="isSplit"
      v-model="hValue2"
      :data-source="options"
      :props="props"
    ></h-select>
  </div>
</template>

<script>
  export default {
    name: 'DateComp',
    props: {
      value: {
        type: String,
        default: ''
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        options: [{ value: '上午' }, { value: '下午' }],
        props: { label: 'value' }
      };
    },
    computed: {
      type() {
        return this.data.dateType === 3 ? 'datetime' : 'date';
      },
      isSplit() {
        return this.data.dateType === 2;
      },
      hValue1: {
        get() {
          if (this.isSplit) {
            let arr = this.value.split(' ');
            if (arr.length > 1 || this.value.indexOf('午') === -1) {
              return arr[0];
            }
            return '';
          }
          return this.value;
        },
        set(val) {
          if (this.isSplit) {
            if (val && this.hValue2) {
              val = val + ' ' + this.hValue2;
            } else {
              val = val || this.hValue2 || '';
            }
          }
          this.$emit('input', val);
        }
      },
      hValue2: {
        get() {
          if (this.isSplit) {
            let arr = this.value.split(' ');
            if (arr.length > 1) {
              return arr[1];
            } else if (arr.length === 1 && this.value.indexOf('午') > -1) {
              return this.value;
            }
          }
          return '';
        },
        set(val) {
          if (this.isSplit) {
            if (this.hValue1 && val) {
              val = this.hValue1 + ' ' + val;
            } else {
              val = this.hValue1 || val || '';
            }
            this.$emit('input', val);
          }
        }
      },
      format() {
        return `yyyy-MM-dd${this.data.dateType === 3 ? ' HH:mm' : ''}`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .bc-form-build-date {
    width: 100%;
    .el-date-editor {
      width: 100%;
      &.short-date-editor {
        width: calc(100% - 95px);
      }
    }
    .el-select {
      width: 90px;
      margin-left: 5px;
    }
  }
</style>
