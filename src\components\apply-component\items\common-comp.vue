<template>
  <div :title="hValue" class="b-c-apply-component-common">{{ hValue }}</div>
</template>

<script>
  export default {
    name: 'CommonComp',
    props: {
      value: [String, Number]
    },
    computed: {
      hValue() {
        return this.value || '--';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .b-c-apply-component-common {
    overflow: hidden;
    // white-space: nowrap; // 单行文本超出后不在省略
    // text-overflow: ellipsis;
  }
</style>
