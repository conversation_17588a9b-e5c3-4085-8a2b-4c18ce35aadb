<template>
  <div class="h-panel">
    <div class="panel-title">
      <div class="title-content">
        {{ title }}
      </div>
      <slot name="title-append"></slot>
    </div>
    <div class="panel-main">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: '<PERSON><PERSON><PERSON>',
    props: {
      title: String
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-ui';

  .h-panel {
    .panel-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 45px;
      padding: 2px 0;
      font-weight: 700;
      font-size: 16px;
      border-bottom: 1px #eaeaed solid;

      .title-content {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 16px;
      }
    }

    .panel-main {
      padding: 16px 0;
    }
  }
</style>
