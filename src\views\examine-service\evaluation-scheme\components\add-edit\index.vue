<template>
  <basic-container>
    <!-- 标题 -->
    <div class="page-title">
      {{ pageTitle }}
    </div>
    <div class="detail-item-title">基本信息</div>
    <div class="detail-item-content">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-suffix=": "
        label-width="100px"
      >
        <el-row :gutter="16">
          <el-col v-if="!isAdd" :span="8">
            <el-form-item label="方案编号" prop="code">
              <el-input
                disabled
                clearable
                placeholder="请输入方案编号"
                v-model="form.code"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="方案名称" prop="name">
              <el-input
                clearable
                :disabled="isDetail"
                placeholder="请输入方案名称"
                v-model.trim="form.name"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="考核维度" prop="dimension">
              <el-select
                :value="form.dimension"
                :disabled="!isAdd"
                placeholder="请选择考核维度"
                size="small"
                style="width: 100%"
                @change="(val) => showSelectConfirm(val, 'dimension')"
              >
                <el-option
                  v-for="dict in indexDict.type.assess_dimension"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="评价类型" prop="type">
              <el-select
                v-model="form.type"
                :disabled="!isAdd || !this.form.dimension"
                :placeholder="
                  this.form.dimension ? '请选择评价类型' : '请先选择考核维度'
                "
                size="small"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in filteredSchemeType"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生效">
              <el-switch
                v-model="form.status"
                :disabled="isDetail"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 方案配置表格 -->
      <scheme-table
        ref="table"
        :dimension="form.dimension"
        :isDetail="isDetail"
        :schemeTableData="form.schemeItemList"
        :canAddIndicator="canAddIndicator"
      />
    </div>
    <!-- 按钮 -->
    <div class="btn-wrapper">
      <el-button
        v-if="!isDetail"
        type="primary"
        size="small"
        @click="handleSubmit"
        >提 交</el-button
      >
      <el-button size="small" @click="handleCancel">返 回</el-button>
    </div>
  </basic-container>
</template>
<script>
  import SchemeTable from './scheme-table.vue';
  import { addEditRules as rules } from '../rules';
  import {
    postSchemeAdd,
    getSchemeDetail,
    postSchemeEdit
  } from '@/api/examine/evaluation-scheme';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'collectionslip-add-edit',
    components: { SchemeTable },
    inject: ['indexDict'],
    props: {
      schemeCode: String,
      pageTitle: String,
      pageType: String,
      visible: Boolean
    },
    watch: {
      visible(val) {
        if (
          val &&
          ['edit', 'detail'].includes(this.pageType) &&
          this.schemeCode
        ) {
          this.getSchemeDetailData();
        }
      }
    },
    data() {
      return {
        rules,
        form: {
          code: undefined,
          name: undefined,
          deptId: 0,
          dimension: undefined,
          status: 1,
          dingTalkAssess: 0,
          type: undefined
        }
      };
    },
    methods: {
      // 获取方案详情
      async getSchemeDetailData() {
        try {
          const res = await getSchemeDetail(this.schemeCode);
          const data = res.data.data;
          Object.assign(this.form, data);
        } catch (e) {
          console.error(e);
        }
      },
      // 选择提示框
      showSelectConfirm(val, field) {
        // 更改考核维度时, 清空评价类型
        this.form.type = undefined;
        // 更改考核维度时, 如果方案配置有数据, 需将表格清空
        if (this.form[field] && this.$refs.table.tableData.length) {
          this.$confirm('修改将清空明细行，是否修改并清空?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.form[field] = val;
              this.$refs.table.clearTable();
            })
            .catch(() => {});
        } else {
          this.form[field] = val;
        }
      },
      // 提交
      handleSubmit() {
        this.$refs.form.validate(async (valid) => {
          // 上方表单必填项校验
          if (!valid) return;
          // 表格校验
          const { submitFlag, data } = this.$refs.table.submitTable();
          if (!submitFlag) return this.$message.warning(data);
          this.form.schemeItemList = cloneDeep(data);
          // 新增
          try {
            const typeObj = {
              add: { api: postSchemeAdd, msg: '新增成功' },
              edit: { api: postSchemeEdit, msg: '编辑成功' }
            };
            const res = await typeObj[this.pageType].api(this.form);
            if (res.data.code === 200) {
              this.$message.success(typeObj[this.pageType].msg);
              this.handleCancel();
              this.$emit('refresh');
            }
          } catch (e) {
            console.error(e);
          }
        });
      },
      // 返回
      handleCancel() {
        this.$refs.form.resetFields();
        this.$refs.table.clearTable();
        this.form = {
          code: undefined,
          name: undefined,
          deptId: 0,
          dimension: undefined,
          type: undefined,
          status: 1,
          dingTalkAssess: 0
        };
        this.$emit('editEmit', 'show', { showFlag: false });
      }
    },
    computed: {
      isAdd() {
        return this.pageType === 'add';
      },
      isEdit() {
        return this.pageType === 'edit';
      },
      isDetail() {
        return this.pageType === 'detail';
      },
      canAddIndicator() {
        return !!this.form.dimension;
      },
      // 根据考核维度和评价类型字典值备注确定 当前考核维度下可选的评价类型
      filteredSchemeType() {
        const res = this.indexDict.type.scheme_type.filter(
          (item) => item.raw.remark === this.form.dimension
        );
        return this.form.dimension ? res : [];
      }
    }
  };
</script>
<style lang="scss" scoped>
  .page-title {
    margin-bottom: 20px;
    font-weight: bold;
    font-size: 20px;
  }

  .detail-item-content ::v-deep {
    .el-form-item {
      margin-bottom: 10px;
    }
  }

  .btn-wrapper {
    margin-top: 20px;
    text-align: center;
  }
</style>
