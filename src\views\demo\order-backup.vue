<template>
  <basic-container>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="true">
      <el-form-item label="名称" prop="deptName">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row>
      <el-col :span="24">
        <div class="tool-box">
          <el-button
            type="primary"
            icon="el-icon-circle-plus-outline"
            size="small"
            @click="handleAdd"
            >新增</el-button
          >
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="small"
            @click="handleMultiDelete"
            >批量删除</el-button
          >
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-table
        :data="data"
        @selection-change="selectChange"
        style="width: 100%"
      >
        <el-table-column prop="id" type="selection" width="55">
        </el-table-column>
        <el-table-column prop="num" label="订单号" width="180">
        </el-table-column>
        <el-table-column prop="remark" label="备注"> </el-table-column>
        <el-table-column prop="createTime" label="创建日期" width="180">
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="250">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type=""
              plain
              v-if="permission.dict_delete11"
              @click="handleView(scope.$index, scope.row)"
              >查看
            </el-button>
            <el-button
              size="mini"
              type="primary"
              plain
              @click="handleEdit(scope.$index, scope.row)"
              >编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <el-row>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChange"
        @current-change="currentChange"
        :total="page.total"
      >
      </el-pagination>
    </el-row>
    <el-dialog
      append-to-body
      :title="dialogTitle"
      width="600px"
      :visible.sync="formVisible"
      @close="resetForm('orderForm')"
    >
      <el-form :model="order" :rules="rules" ref="orderForm">
        <el-form-item label="标题" prop="title" label-width="55px">
          <el-input v-model="order.title" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="时间" label-width="55px">
          <el-date-picker
            v-model="order.time"
            style="width: 100%"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            autocomplete="off"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="内容" label-width="55px">
          <el-input
            v-model="order.content"
            autocomplete="off"
            type="textarea"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formVisible = false">返 回</el-button>
        <el-button
          v-if="!viewMode"
          type="primary"
          @click="submitorder('orderForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import { getPage, getDetail, submit, remove } from '@/api/demo/order';

  import { mapGetters } from 'vuex';

  export default {
    data() {
      return {
        // 是否显示
        formVisible: false,
        // 是否查看
        viewMode: false,
        // dialog标题
        dialogTitle: '',
        // 列表数据
        data: [],
        // 选中的数据
        multiSelection: [],
        // 分页数据
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        // 校验规则
        rules: {
          title: [{ required: true, message: '请输入标题', trigger: 'blur' }]
        },
        // 表单映射模型
        order: {
          id: '',
          num: '',
          remark: '',
          createTime: ''
        }
      };
    },
    created() {
      this.onLoad();
    },
    computed: {
      ...mapGetters(['permission']),
      // permissionList() {
      //   return {
      //     addBtn: this.vaildData(this.permission.notice_add, false),
      //     viewBtn: this.vaildData(this.permission.notice_view, false),
      //     delBtn: this.vaildData(this.permission.notice_delete, false),
      //     editBtn: this.vaildData(this.permission.notice_edit, false)
      //   };
      // },
      ids() {
        let ids = [];
        this.multiSelection.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(',');
      }
    },
    methods: {
      onLoad() {
        getPage().then((res) => {
          this.data = res.data.data.records;
          this.page.total = res.data.data.total;
        });
      },
      selectChange(val) {
        this.multiSelection = val;
        console.log(this.multiSelection);
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      resetForm(formName) {
        this.$refs[formName].clearValidate();
      },
      handleAdd() {
        this.dialogTitle = '新增订单';
        this.formVisible = true;
        this.viewMode = false;
        console.log('add');
      },
      handleView(index, row) {
        this.dialogTitle = '查看订单';
        this.formVisible = true;
        this.viewMode = true;
        getDetail(row.id).then((res) => {
          if (res.data.success) {
            this.order = res.data.data;
          }
        });
        console.log('view');
        console.log(index);
        console.log(row);
      },
      handleEdit(index, row) {
        this.dialogTitle = '修改订单';
        this.formVisible = true;
        this.viewMode = false;
        getDetail(row.id).then((res) => {
          if (res.data.success) {
            this.order = res.data.data;
          }
        });
        console.log('edit');
        console.log(index);
        console.log(row);
      },
      handleDelete(index, row) {
        this.$confirm('确定删除选择的数据吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          remove(row.id).then((res) => {
            if (res.data.success) {
              this.onLoad();
              this.$message({
                type: 'success',
                message: '操作成功！'
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg
              });
            }
          });
        });
        console.log('delete');
        console.log(index);
        console.log(row);
      },
      handleMultiDelete() {
        console.log('multi-delete');
        if (this.multiSelection.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定删除选择的数据吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          remove(this.ids).then((res) => {
            if (res.data.success) {
              this.onLoad();
              this.$message({
                type: 'success',
                message: '操作成功！'
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg
              });
            }
          });
        });
      },
      submitorder(formName) {
        // 表单验证
        this.$refs[formName].validate((valid) => {
          if (valid) {
            console.log(this.order);
            submit(this.order).then((res) => {
              if (res.data.success) {
                this.formVisible = false;
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                });
              } else {
                this.$message({
                  type: 'error',
                  message: res.data.msg
                });
              }
            });
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }
</style>
