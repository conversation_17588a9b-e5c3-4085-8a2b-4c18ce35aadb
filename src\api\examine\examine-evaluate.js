import request from '@/router/axios';
// kpi - kpi填报单 - 分页
export const getKpiFillList = (params) => {
  return request({
    url: `/api/examine/kpi-filling-form/page`,
    method: 'get',
    params
  });
};

// kpi - kpi填报单 - 填报单详情
export const getKpiFillDetail = (code) => {
  return request({
    url: `/api/examine/kpi-filling-form/detail?fillingFormCode=${code}`,
    method: 'get'
  });
};

// kpi - kpi填报单 - 保存
export const postKpiFillSave = (data) => {
  return request({
    url: `/api/examine/kpi-filling-form/save`,
    method: 'post',
    data
  });
};

// kpi - kpi填报单 - 提交 & 发起流程
export const postKpiFillSubmit = (data) => {
  return request({
    url: `/api/examine/kpi-filling-form/submit`,
    method: 'post',
    data
  });
};

// kpi - kpi填报单 - 审批
export const postKpiFillApprove = (data) => {
  return request({
    url: `/api/examine/kpi-filling-form/approve`,
    method: 'post',
    data
  });
};

// kpi - kpi填报单 - 撤销
export const getKpiFillRepeal = (id) => {
  return request({
    url: `/api/examine/kpi-filling-form/revocation?processInstanceId=${id}`,
    method: 'get'
  });
};

// kpi - kpi评价单 - 分页
export const getKpiEvaluateList = (params) => {
  return request({
    url: `/api/examine/evaluation-form/page`,
    method: 'get',
    params
  });
};

// kpi - kpi填报单 - 填报单详情
export const getKpiEvaluateDetail = (code) => {
  return request({
    url: `/api/examine/evaluation-form/kpi-detail?evaluationCode=${code}`,
    method: 'get'
  });
};

// kpi - kpi填报单 - 保存
export const postKpiEvaluateSave = (data) => {
  return request({
    url: `/api/examine/evaluation-form/kpi-submit`,
    method: 'post',
    data
  });
};
