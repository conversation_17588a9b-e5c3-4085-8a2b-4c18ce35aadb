import request from '@/router/axios';

export const processList = () => {
  return request({
    url: '/api/pc/process-group/process-definition/list',
    method: 'get'
  });
};

export const processListYd = () => {
  return request({
    url: '/api/pc/process-group/process-definition/list-zikuang',
    method: 'get'
  });
};

export const stopProcess = (id) => {
  return request({
    url: '/api/pc/process-definition/stop',
    method: 'get',
    params: { id }
  });
};
export const startProcess = (id) => {
  return request({
    url: '/api/pc/process-definition/start',
    method: 'get',
    params: { id }
  });
};
export const deleteProcess = (id) => {
  return request({
    url: '/api/pc/process-definition/delete',
    method: 'get',
    params: { id }
  });
};
export const resetName = (groupName, id) => {
  return request({
    url: '/api/pc/process-group/reset-name',
    method: 'post',
    data: { id, groupName }
  });
};
export const createName = (groupName) => {
  return request({
    url: '/api/pc/process-group/create',
    method: 'post',
    data: { groupName }
  });
};
export const deleteGroup = (id) => {
  return request({
    url: '/api/pc/process-group/delete',
    method: 'get',
    params: { id }
  });
};
export const moveGroup = (ids) => {
  return request({
    url: '/api/pc/process-group/group/move',
    method: 'post',
    data: { ids }
  });
};
export const moveFlow = (data) => {
  return request({
    url: '/api/pc/process-group/process-definition/flow/move',
    method: 'post',
    data
  });
};
