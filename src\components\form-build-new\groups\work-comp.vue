<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    :component-list="componentList"
    :disabled="disabled"
    :rules="rules"
    :loading="loading"
  />
</template>

<script>
  import { mapGetters } from 'vuex';
  import { getOvertimeInfo, getTimeInterval2 } from '@/api/desk/flow';
  import { deepClone } from '@/util/util';
  import { str2Date } from '@/util/date';

  export default {
    name: 'WorkComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        hour: undefined,
        loading: {
          work4: false,
          work5: false
        },
        enable: true // 是否配置考勤组
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      rules() {
        let rules = {
          work1: [{ type: 'array', required: true, message: '请选择' }],
          work2: [
            { required: true, message: '请选择' },
            { validator: this.validateWork2 }
          ],
          work3: [
            { required: true, message: '请选择' },
            { validator: this.validateWork3 }
          ],
          work4: [{ validator: this.validateWork4 }]
        };
        return rules;
      }
    },
    watch: {
      'hValue.work1'(val, oldVal) {
        if (JSON.stringify(val) === JSON.stringify(oldVal)) {
          return;
        }
        this.setWork4();
      },
      'hValue.work2'() {
        this.setWork4();
      },
      'hValue.work3'() {
        this.setWork4();
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        let { id, employeeName } = this.userInfo;
        this.$set(this.hValue, 'work1', [{ id: id, name: employeeName }]);
        if (!this.data.instead) {
          let componentList = deepClone(this.componentList);
          componentList[1].valueJson.readonly = true;
          this.$emit('update:component-list', componentList);
        }
        this.$set(this.loading, 'work5', true);
        getOvertimeInfo()
          .then((res) => {
            this.$set(this.loading, 'work5', false);
            let {
              applyNum = 0,
              totalHour,
              passMinHour = 0
            } = res.data.data || {};
            this.$set(
              this.hValue,
              'work5',
              `本月已申请${applyNum}次，累计时长${totalHour}小时，最小有效时长${passMinHour}小时`
            );
          })
          .catch(() => {
            this.$set(this.loading, 'work5', false);
            this.enable = false;
          });
        this.setWork4();
      },
      setWork4() {
        this.$set(this.hValue, 'work4', '');
        this.hour = undefined;
        let { work1 = [], work2, work3 } = this.hValue;
        if (this.enable && work2 && work3) {
          if (work2 === work3) {
            this.hour = 0;
          } else if (this.validateTime()) {
            this.$set(this.loading, 'work4', true);
            this.hour = -1;
            getTimeInterval2({
              flowType: 0,
              beginTime: work2,
              endTime: work3,
              dateUnit: 3,
              userIdList: work1.map((item) => item.id).join(',')
            })
              .then((res) => {
                this.$set(this.loading, 'work4', false);
                let { hour } = res.data.data || {};
                this.hour = hour;
                if (hour || hour === 0) {
                  this.$set(this.hValue, 'work4', hour + '');
                }
                this.$refs.formBuild.validateField('work4');
              })
              .catch(() => {
                this.$set(this.loading, 'work4', false);
                this.hour = -2;
              });
          }
        }
      },
      validateWork2(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('开始时间必须早于结束时间'));
          return;
        }
        callback();
      },
      validateWork3(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('结束时间必须晚于开始时间'));
          return;
        }
        callback();
      },
      validateWork4(rule, value, callback) {
        let { work2, work3 } = this.hValue;
        if (work2 && work3) {
          if (this.hour === -1) {
            callback(new Error('正在计算时长，请等待'));
            return;
          }
          if (this.hour === -2) {
            callback(new Error('时长计算错误，请重试'));
            return;
          }
          if (this.hour <= 0) {
            callback(new Error('时长必须大于0'));
            return;
          }
        }
        callback();
      },
      validateTime() {
        let { work2, work3 } = this.hValue;
        if (!work2 || !work3) {
          return true;
        }
        let startDate = str2Date(work2);
        let endDate = str2Date(work3);
        return startDate.getTime() < endDate.getTime();
      },
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
