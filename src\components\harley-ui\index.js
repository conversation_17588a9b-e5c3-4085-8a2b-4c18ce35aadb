import HAdvancedSearch from './advanced-search.vue';
import HBlock from './block.vue';
import HCarPlate from './car-plate.vue';
import HCarPlateColorRadio from './car-plate-color-radio.vue';
import HCarPlateNoEditor from './car-plate-no-editor/index.vue';
import HCheckboxGroup from './checkbox-group.vue';
import HImageUpload from './image-upload.vue';
import HInputNumber from './input-number.vue';
import HNavigation from './navigation.vue';
import HOperationBar from './operation-bar.vue';
import HPanel from './panel.vue';
import HRadioGroup from './radio-group.vue';
import HSearchBar from './search-bar.vue';
import HSearchCondition from './search-condition.vue';
import HSelect from './select.vue';
import HSelectTable from './select-table.vue';
import HSelectTree from './select-tree.vue';
import HSmsCodeInput from './sms-code-input.vue';
import HTable from './table.vue';
import HTipBoard from './tip-board.vue';
import HUpload from './upload.vue';
import HUploadList from './upload-list.vue';

const Harley = {
  install: function (Vue) {
    Vue.component('HAdvancedSearch', HAdvancedSearch);
    Vue.component('HBlock', HBlock);
    Vue.component('HCarPlate', HCarPlate);
    Vue.component('HCarPlateColorRadio', HCarPlateColorRadio);
    Vue.component('HCarPlateNoEditor', HCarPlateNoEditor);
    Vue.component('HCheckboxGroup', HCheckboxGroup);
    Vue.component('HImageUpload', HImageUpload);
    Vue.component('HInputNumber', HInputNumber);
    Vue.component('HNavigation', HNavigation);
    Vue.component('HNavigation', HNavigation);
    Vue.component('HOperationBar', HOperationBar);
    Vue.component('HPanel', HPanel);
    Vue.component('HRadioGroup', HRadioGroup);
    Vue.component('HSearchBar', HSearchBar);
    Vue.component('HSearchCondition', HSearchCondition);
    Vue.component('HSelect', HSelect);
    Vue.component('HSelectTable', HSelectTable);
    Vue.component('HSelectTree', HSelectTree);
    Vue.component('HSmsCodeInput', HSmsCodeInput);
    Vue.component('HTable', HTable);
    Vue.component('HTipBoard', HTipBoard);
    Vue.component('HUpload', HUpload);
    Vue.component('HUploadList', HUploadList);
  }
};

export default Harley;
