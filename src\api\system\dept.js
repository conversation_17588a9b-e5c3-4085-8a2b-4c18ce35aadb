import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/szyk-system/dept/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getLazyList = (parentId, params) => {
  return request({
    url: '/api/szyk-system/dept/lazy-list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-system/dept/check-remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const add = (row) => {
  return request({
    url: '/api/szyk-system/dept/submit',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/szyk-system/dept/submit',
    method: 'post',
    data: row
  });
};

export const getDept = (id) => {
  return request({
    url: '/api/szyk-system/dept/detail',
    method: 'get',
    params: {
      id
    }
  });
};

export const getDeptTree = (tenantId) => {
  return request({
    url: '/api/szyk-system/dept/tree',
    method: 'get',
    params: {
      tenantId
    }
  });
};

export const getDeptLazyTree = (parentId) => {
  return request({
    url: '/api/szyk-system/dept/lazy-tree',
    method: 'get',
    params: {
      parentId
    }
  });
};
/**
 * 切换企业
 * @param {String} deptId 企业ID
 */
export const setUserCompany = (deptId) =>
  request({
    url: '/api/attila-system/dept/company/switch',
    method: 'post',
    params: { deptId }
  });
// 职务
export const positionPerson = () => {
  return request({
    url: '/api/attila-system/company-job/list',
    method: 'get'
  });
};
/**
 * 创建新组织
 */
export const orgCreate = (data) => {
  return request({
    url: '/api/attila-system/dept/org/create',
    method: 'post',
    data
  });
};
/**
 * 企业下组织机构部门树
 */
export const getTreeList = (orgId) => {
  return request({
    url: '/api/system-attila/attila/tree',
    method: 'get',
    params: { orgId }
  });
};
/**
 * 组织机构部门员工树(选择直属上级用)
 */
export const getStaffTreeList = (nodeId, processId, orgId) => {
  return request({
    url: '/api/attila-user/user/all-tree',
    method: 'get',
    params: { nodeId, processId, orgId }
  });
};
/**
 * 获取企业关联企业（仅一层）
 * @param {Object} orgId 参数
 */
export const getLinkDepts = (orgId) =>
  request({
    url: '/api/system-attila/org/linkList',
    method: 'get',
    params: {
      orgId
    }
  });

export const getDeptLazyTreeByParent = (parentId, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/lazy-tree-by-parent',
    method: 'get',
    params: {
      parentId,
      tenantId
    }
  });
};

// 获取所有部门
export const getDeptAll = (tenantId) => {
  return request({
    url: '/api/szyk-system/dept/all',
    method: 'get',
    params: {
      tenantId
    }
  });
};

// 获取部门的所有父级部门id列表(查名字)
export const getDeptNameList = (deptName, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/select-ancestor-id-list',
    method: 'get',
    params: {
      deptName,
      tenantId
    }
  });
};

// 获取部门的所有父级部门id列表(查ids)
export const getDeptIdList = (ids, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/select-ancestor-id-list-by-ids',
    method: 'get',
    params: {
      ids,
      tenantId
    }
  });
};

// // 获取部门下的人员列表
// export const getUserList = (params) => {
//   return request({
//     url: '/api/szyk-user/page',
//     method: 'get',
//     params
//   });
// };

// 获取部门下的人员列表
export const getUserList = (params) => {
  return request({
    url: '/api/szyk-user/select-page',
    method: 'get',
    params
  });
};
