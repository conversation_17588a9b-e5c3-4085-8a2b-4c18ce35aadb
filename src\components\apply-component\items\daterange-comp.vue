<template>
  <div>
    <el-col :span="12">
      <el-form-item :label="`${data.nameOne}：`">
        <common-comp :value="value.startTime" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item :label="`${data.nameTwo}：`">
        <common-comp :value="value.endTime" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item
        :label="`${data.nameThree}(${data.dateType === 3 ? '小时' : '天'})：`"
      >
        <common-comp :value="value.interval" />
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
  import CommonComp from './common-comp';
  export default {
    name: 'DaterangeComp',
    components: { CommonComp },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    }
  };
</script>
