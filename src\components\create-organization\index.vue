<template>
  <el-dialog
    v-if="closeDia"
    v-loading="dialogLoading"
    :visible.sync="dialogVisible"
    append-to-body
    :before-close="closeDialog"
    width="600px"
    class="createOrganization"
    title="创建新组织"
  >
    <el-form ref="form" size="small" :rules="rules" :model="form">
      <el-form-item label="组织名称：" prop="orgName">
        <el-input
          v-model="form.orgName"
          :maxlength="50"
          show-word-limit
          placeholder="请填写组织真实名称"
        />
      </el-form-item>
      <el-form-item label="所属行业：" prop="industry">
        <el-cascader
          v-model="form.industry"
          placeholder="请选择所属行业"
          :options="statusList"
          :props="props"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="组织规模：" prop="orgScale">
        <h-select
          v-model="form.orgScale"
          :data-source="deptList"
          :props="DeptProps"
          placeholder="请选择组织规模"
        ></h-select>
      </el-form-item>
      <el-form-item label=" ">
        <span
          >新组织创建后，你将成为主管理员，可用管理组织架构、添加成员，登录管理后台。</span
        >
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">返回</el-button>
      <el-button @click="handleClose(0)" type="primary">创建组织</el-button>
      <el-button v-if="isChildBtn" @click="handleClose(1)" type="primary"
        >创建并关联为子组织</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
  import { getList } from '@/api/system/dict';
  import { orgCreate, setUserCompany } from '@/api/system/dept';
  import { mapGetters } from 'vuex';
  import { resetRouter } from '@/router/router';
  export default {
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      },
      isChildBtn: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        statusList: [],
        deptList: [],
        dialogLoading: false,
        closeDia: true,
        props: {
          value: 'dictKey',
          label: 'dictValue'
        },
        DeptProps: {
          value: 'dictKey',
          label: 'dictValue'
        },
        rules: {
          orgName: [
            {
              required: true,
              message: '请填写组织真实名称',
              trigger: 'change'
            }
          ],
          industry: [
            {
              required: true,
              message: '请选择所属行业',
              trigger: 'change'
            }
          ],
          orgScale: [
            {
              required: true,
              message: '请选择组织规模',
              trigger: 'change'
            }
          ]
        },
        form: {
          orgName: '',
          industry: [],
          orgScale: ''
        }
      };
    },
    computed: {
      ...mapGetters(['isVisitor'])
    },
    watch: {
      dialogVisible() {
        if (!this.dialogVisible) {
          this.closeDia = false;
          this.$nextTick(() => {
            this.closeDia = true;
          });
        }
      }
    },
    mounted() {
      getList('', '', { code: 'org_industry_class' }).then((res) => {
        if (res && res.data && res.data.data) {
          this.statusList = res.data.data[0].children;
        }
      });
      this.$store.dispatch('GetDictByCode', 'org_size').then((list) => {
        this.deptList = list;
      });
    },
    methods: {
      handleClose(isRelation) {
        this.$refs.form.validate((v) => {
          if (v) {
            this.dialogLoading = true;
            orgCreate({
              ...this.form,
              isRelation,
              industry: this.form.industry[1]
            })
              .then((res) => {
                if (res && res.data && res.data.data) {
                  this.$message.success('创建成功');
                  this.checkOrg(isRelation, res.data.data.id);
                }
              })
              .catch(() => {
                this.dialogLoading = false;
              });
          }
        });
      },
      checkOrg(isRelation, deptId) {
        if (isRelation) {
          this.dialogLoading = false;
          this.closeDialog();
          this.$emit('updateData');
          this.$router.push({ name: 'organizationManager' });
          return;
        }
        if (!this.isVisitor) {
          this.dialogLoading = false;
          this.closeDialog();
          return;
        }
        setUserCompany(deptId).then(() => {
          this.$store.commit('SET_REFRESH_LOCK', true);
          this.$store
            .dispatch('refreshToken')
            .then((data) => {
              return this.$store.dispatch('saveLoginData', data);
            })
            .then(() => {
              this.$router.go(0);
            })
            .catch(() => {
              this.dialogLoading = false;
              resetRouter();
              this.$router.push({ name: 'login' });
            });
        });
      },
      closeDialog() {
        this.$emit('update:dialogVisible', false);
        this.form = {
          orgName: '',
          industry: [],
          orgScale: ''
        };
        this.$refs.form.clearValidate();
      }
    }
  };
</script>

<style lang="scss">
  .createOrganization {
    .el-dialog__headerbtn {
      top: 27px;
    }
    .el-form-item {
      margin-bottom: 24px;
    }
    .el-dialog__body {
      padding-left: 54px !important;
      padding-right: 48px !important;
    }
    .el-form-item {
      display: flex;
      .el-form-item__content {
        line-height: 22px;
        color: #999999;
      }
    }
    .el-dialog__footer {
      padding-top: 0 !important;
    }
    .el-form-item__content {
      flex: 1;
      .el-cascader {
        width: 100%;
      }
    }
  }
</style>
