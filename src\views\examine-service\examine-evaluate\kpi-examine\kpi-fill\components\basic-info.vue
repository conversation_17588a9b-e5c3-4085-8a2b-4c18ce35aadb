<template>
  <div class="info_form_mb5">
    <el-form label-width="110px" label-suffix="：" size="small">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="填报单号"> {{ form.code || '--' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被评价机构">
            {{ form.deptName || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="考核周期"
            >{{ form.periodItemName || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="截止日期">
            {{ form.deadline | formatDate }}</el-form-item
          >
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联评价方案"
            >{{ form.schemeCode || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联评价单号"
            >{{ form.evaluationCode || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据状态">
            {{ form.status | fillStatus }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批状态"
            >{{ form.examineStatus | receiptStatus }}
          </el-form-item>
        </el-col>
        <template v-if="isDetail">
          <el-col :span="8">
            <el-form-item label="提报日期"
              >{{ form.fillingDate | formatDate }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="提报人"
              >{{ form.fillingUserName || '--' }}
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="8" v-else>
          <el-form-item label="提报日期">
            <el-date-picker
              type="datetime"
              v-model="cFillingDate"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择提报日期"
              style="width: 200px"
              :editable="false"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'basic-info',
    props: {
      isDetail: {
        type: Boolean,
        default: false
      },
      form: {
        type: Object,
        default() {
          return {};
        }
      },
      fillingDate: {
        type: String,
        require: true
      }
    },
    model: {
      prop: 'fillingDate',
      event: 'change'
    },
    computed: {
      cFillingDate: {
        get() {
          return this.fillingDate;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-form-item {
    margin-bottom: 0 !important;
  }
</style>
