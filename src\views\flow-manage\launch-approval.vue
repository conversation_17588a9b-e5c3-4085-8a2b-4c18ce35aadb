<template>
  <h-block v-loading="loading" :title="title" class="approval-launch">
    <h-panel title="审批内容">
      <c-form-build
        :style="isForm ? 'display:none' : ''"
        v-if="componentList.length"
        ref="formBuild"
        v-model="form"
        :component-list="componentList"
        :accept="'.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .xls, .xlsx, .pdf, .ppt, .rar, .zip'"
      />
      <FormData
        v-if="isForm"
        :form="formData"
        @upDateFormData="upDateFormData"
      />
    </h-panel>
    <h-panel title="审批流程">
      <edit-apply-log
        v-if="users.length"
        ref="selectPeople"
        @change="updateLogList"
        :id="processId"
        :apply-log-list="users"
      ></edit-apply-log>
      <div v-else class="no-flow">
        <img v-oss src="/apply/noFlow.png" />
        <div>必填信息填写后，流程将自动显示</div>
      </div>
    </h-panel>
    <div class="button-area">
      <el-button @click="cancel">返回</el-button>
      <el-button @click="submit" type="primary">提交</el-button>
    </div>
  </h-block>
</template>

<script>
  import {
    getFlowInfo,
    getFlowCondition,
    launchApproval,
    processDetail
  } from '@/api/flow/process';
  import { setErrorComponentFocus } from '@/util/validate.js';
  import CFormBuild from '@/components/form-build-new';
  import editApplyLog from '@/components/apply-log/edit-apply-log';
  import FormData from './form.vue';
  export default {
    components: { CFormBuild, editApplyLog, FormData },
    // props: {
    //   id: String
    // },
    data() {
      return {
        id: undefined,
        isForm: undefined,
        loading: false,
        processName: '',
        componentList: [],
        form: {},
        formData: {
          proName: undefined,
          startTime: undefined,
          endTime: undefined,
          remark: undefined
        },
        hasCondition: 0, // 1-需要必填项填完后加载审批节点；0-直接加载审批节点
        users: [],
        processId: '',
        usersStr: '', // 用于判断是否重新【加载】审批人节点
        conditionForm: {} // 用于判断是否重新【查询】审批人节点
      };
    },
    computed: {
      title() {
        return this.processName ? `发起审批-${this.processName}` : '发起审批';
      }
    },
    watch: {
      form: {
        handler(val) {
          if (this.hasCondition) {
            let reoladFlag = true;
            let newConditionForm = {};
            let keys = Object.keys(this.conditionForm);
            for (let i = 0; i < keys.length; i++) {
              let key = keys[i];
              let value = val[key];
              if (!this.checkRequired(value)) {
                reoladFlag = false;
              }
              newConditionForm[key] = value;
            }
            if (
              reoladFlag &&
              JSON.stringify(this.conditionForm) !==
                JSON.stringify(newConditionForm)
            ) {
              this.$nextTick(() => {
                this.getFlowCondition();
              });
            }
            this.conditionForm = newConditionForm;
          }
        },
        deep: true
      }
    },
    created() {
      this.id = this.$route.query.id;
      this.init();
    },
    methods: {
      init() {
        this.loading = true;
        getFlowInfo(this.id)
          .then((res) => {
            let {
              processName,
              formStyleJson,
              hasCondition,
              usersNodeinfos,
              isForm,
              id
            } = res.data.data || {};
            this.processName = processName || '';
            let componentList = JSON.parse(formStyleJson || '[]');
            this.hasCondition = hasCondition || 0;
            this.processId = id;
            this.isForm = isForm !== null ? isForm : 0;
            if (!hasCondition) {
              this.users = usersNodeinfos || [];
            } else {
              const conditionTypes = [
                'radio',
                'checkbox',
                'computed',
                'inputNumber',
                'inputMoney'
              ];
              // 构造requiredForm
              componentList.forEach((item) => {
                if (
                  item.valueJson.required &&
                  conditionTypes.includes(item.type)
                ) {
                  this.conditionForm[item.id] = '';
                } else if (
                  [
                    'rest',
                    'replacecard',
                    'work',
                    'out',
                    'trip',
                    'turnFormal',
                    'leave'
                  ].includes(item.type) ||
                  item.type.startsWith('yd-') ||
                  item.type.startsWith('htTo')
                ) {
                  item.children.forEach((item) => {
                    if (
                      (item.valueJson.required &&
                        conditionTypes.includes(item.type)) ||
                      'text' === item.type
                    ) {
                      this.conditionForm[item.id] = '';
                    }
                  });
                }
              });
            }
            // 再次发起查询之前的kv
            if (this.$route.query.instanceId) {
              processDetail(this.$route.query.instanceId)
                .then((res) => {
                  let { dataJson = '{}', testPluginForm } = res.data.data || {};
                  this.form = JSON.parse(dataJson);
                  this.formData = testPluginForm;
                })
                .finally(() => {
                  this.loading = false;
                  this.componentList = componentList;
                });
            } else {
              this.loading = false;
              this.componentList = componentList;
            }
          })
          .catch(() => {
            this.loading = false;
          });
      },
      checkRequired(value) {
        if (value instanceof Array) {
          value = value.length || '';
        } else if (typeof value === 'object') {
          value = value.value;
        }
        if (
          (value || value === 0) &&
          value !== '编辑的计算公式为空或不符合计算法则，无法计算'
        ) {
          this.$refs.formBuild.validate();
        }
        return (
          (value || value === 0) &&
          value !== '编辑的计算公式为空或不符合计算法则，无法计算'
        );
      },
      getFlowCondition() {
        this.loading = true;
        getFlowCondition(this.id, JSON.stringify(this.form))
          .then((res) => {
            this.loading = false;
            let users = res.data.data || [];
            let usersStr = JSON.stringify(users);
            // 如果审批人节点与上次加载的节点不同，则重新加载；否则不加载
            if (usersStr !== this.usersStr) {
              this.users = users;
              this.usersStr = usersStr;
            }
          })
          .catch(() => {
            this.loading = false;
          });
      },
      updateLogList(v) {
        this.users = v;
      },
      submit() {
        this.$refs.formBuild
          .validate()
          .then(() => {
            if (this.$refs.selectPeople.validate()) {
              this.$confirm('是否确定发起审批？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.loading = true;
                launchApproval({
                  processDefinitionId: this.id,
                  dataJson: JSON.stringify(this.form),
                  usersNodeinfos: this.users
                })
                  .then(() => {
                    this.loading = false;
                    this.$message.success('发起审批成功');
                    this.cancel();
                  })
                  .catch(() => {
                    this.loading = false;
                  });
              });
            }
          })
          .catch(() => {
            setErrorComponentFocus();
            if (this.$refs.selectPeople) {
              this.$refs.selectPeople.validate();
            }
          });
      },
      findTag(value) {
        let tag, key;
        let tagList = this.$store.state.tags.tagList;
        tagList.map((item, index) => {
          if (item.value === value) {
            tag = item;
            key = index;
          }
        });
        return { tag: tag, key: key };
      },
      cancel() {
        let { tag } = this.findTag(this.$route.fullPath);
        this.$store.commit('DEL_TAG', tag);
        setTimeout(() => {
          this.$router.push('/flow-manage/approval');
        }, 500);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .approval-launch {
    .h-panel {
      margin-top: 10px;

      .bc-form-build {
        padding-right: 37px;
      }

      .edit-apply-log {
        margin: 30px 72px 7px 120px;
        padding-right: 37px;
      }

      .no-flow {
        margin: 44px 0 64px;
        color: #999;
        text-align: center;

        img {
          width: 120px;
          margin-bottom: 20px;
        }
      }
    }

    .button-area {
      text-align: center;

      .el-button {
        width: 140px;
        margin-bottom: 50px;
      }
    }
  }
</style>

<style lang="scss" scoped>
  .approval-launch {
    padding: 30px;

    &.h-block {
      .h-block-content {
        margin: auto;
      }
    }
  }
</style>
