<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column type="index" label="序号" width="50" align="center" />
      <el-table-column
        label="考核指标"
        width="300"
        align="center"
        prop="indexName"
      />
      <el-table-column
        label="考核目标及计分标准"
        align="center"
        prop="standard"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="本周期考核完成情况(点击输入)"
        align="center"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput(scope.row, 'completionSituation')"
          >
            {{
              scope.row.completionSituation &&
              scope.row.completionSituation.length
                ? scope.row.completionSituation || '---'
                : '点击输入内容'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="累计完成(点击输入)"
        align="center"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput(scope.row, 'accumulatedCompletion')"
          >
            {{
              scope.row.accumulatedCompletion &&
              scope.row.accumulatedCompletion.length
                ? scope.row.accumulatedCompletion || '---'
                : '点击输入内容'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="证明材料" align="center">
        <template slot-scope="scope">
          <upload-file
            btn-type="text"
            btn-title="上传证明材料"
            onlyButton
            :files="scope.row.attachList || []"
            @onInput="(...args) => handleFileUpload(scope.row, ...args)"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--  同步文本输入  -->
    <el-dialog
      width="600px"
      :title="dialogTitle"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        :placeholder="`请输入${dialogTitle}`"
        maxlength="1000"
        show-word-limit
        :rows="8"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
        <el-button size="small" type="primary" @click="inputSave"
          >完 成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { UploadFile } from '@/components/yk-upload-file';
  import { mapState } from 'vuex';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'fill-list',
    components: { UploadFile },
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      list: {
        handler(newVal) {
          this.tempList = cloneDeep(newVal);
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        // 弹窗数据
        inputVisited: false,
        temp: {},
        tempInput: '',
        dialogField: '',
        // 表格数据
        tempList: {}
      };
    },
    mounted() {
      document.addEventListener(
        'resize',
        () => {
          this.bindProps();
        },
        false
      );
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      handleInput(row, field) {
        this.inputVisited = true;
        this.temp = row;
        this.tempInput = row[field];
        this.dialogField = field;
      },
      // 完成情况弹窗打开
      inputClose() {
        this.inputVisited = false;
        this.temp = {};
      },
      // 完成情况弹窗关闭
      inputSave() {
        this.tempList.map((item) => {
          if (item.id === this.temp.id) {
            item[this.dialogField] = this.tempInput;
          }
          return item;
        });
        this.$emit('sync', this.tempList);
        this.inputVisited = false;
      },
      // 证明材料上传成功回调
      handleFileUpload(row, ids, files) {
        this.tempList.map((item) => {
          if (item.id === row.id) {
            item.file = ids.join();
            item.attachList = files;
          }
          return item;
        });
        this.$emit('sync', this.tempList);
      }
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      },
      dialogTitle() {
        const titleObj = {
          accumulatedCompletion: '累计完成',
          completionSituation: '完成情况'
        };
        return titleObj[this.dialogField];
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }
  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
  }
</style>
