import request from '@/router/axios';

// 分页查询
export function getPageList(params) {
  return request({
    url: '/api/examine/performance-form/page',
    method: 'get',
    params
  });
}

// 经济效益及部门kpi绩效结果单详情
export function getDetail(params) {
  return request({
    url: '/api/examine/performance-form-economy-kpi/detail',
    method: 'get',
    params
  });
}

// 保存-经济效益及部门kpi绩效结果单提交
export function postSave(data) {
  return request({
    url: '/api/examine/performance-form-economy-kpi/save',
    method: 'post',
    data
  });
}

// 提交-经济效益及部门kpi绩效结果单提交
export function postSubmit(data) {
  return request({
    url: '/api/examine/performance-form-economy-kpi/submit',
    method: 'post',
    data
  });
}

// 根据字典key获取对应最新发布流程定义id
export function getProcessId(params) {
  return request({
    url: '/api/pc/process-definition/getIdByKey',
    method: 'get',
    params
  });
}

// 审批（同意-拒绝）岗位业绩评价单
export function postApprove(data) {
  return request({
    url: '/api/examine/performance-form-economy-kpi/approve',
    method: 'post',
    data
  });
}

// 撤销岗位业绩评价的审批流程
export function getRevocation(params) {
  return request({
    url: '/api/examine/performance-form-economy-kpi/revocation',
    method: 'get',
    params
  });
}

// 导出-经济效益及部门kpi绩效结果单
export function getImport(params) {
  return request({
    url: '/api/examine/performance-form-economy-kpi/export',
    method: 'get',
    params
  });
}
