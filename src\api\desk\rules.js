import request from '@/router/axios';

export const leaveRule = (params) => {
  return request({
    url: '/api/attila-desk/leave-rule',
    method: 'get',
    params
  });
};
export const leaveDetail = (params) => {
  return request({
    url: '/api/attila-desk/leave-rule/detail',
    method: 'get',
    params
  });
};

// 假期余额列表
export const balancePage = (params) => {
  return request({
    url: '/api/attila-desk/leave-rule/record/balance/page',
    method: 'get',
    params
  });
};

// 假期余额详情
export const recordDetail = (params) => {
  return request({
    url: '/api/attila-desk/leave-rule/record/detail',
    method: 'get',
    params
  });
};

export const leaveRuleSave = (data) => {
  return request({
    url: '/api/attila-desk/leave-rule',
    method: 'post',
    data
  });
};
export const getRelevanceList = (data) => {
  return request({
    url: '/api/attila-desk/leave-rule/get-relevance-list',
    method: 'post',
    data
  });
};

// 手动发放假期提交接口
export const distribute = (data) => {
  return request({
    url: '/api/attila-desk/leave-rule/record/manual/distribute',
    method: 'post',
    data
  });
};
// 为加班规则提供的规则列表(加班时长记入余额)
export const leaveRulesForAttendOvertime = (params) => {
  return request({
    url: '/api/attila-desk/leave-rule/list-overtime',
    method: 'get',
    params
  });
};
