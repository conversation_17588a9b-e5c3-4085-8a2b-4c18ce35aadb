// 递归查找下属机构
export function getSubDept(deptData, id) {
  const queue = [...deptData];
  while (queue.length) {
    const o = queue.shift();
    if (o.id == id) return [o];
    queue.push(...(o.children || []));
  }
}

// 去除数组里每个成员字段组成字符串
export function transferAryToString(arr, field = 'name') {
  if (!arr || !arr.length) return '';
  return arr.map((item) => item[field]).join('; ');
}

// 校验字段
function checkValid(obj, key) {
  const _value = obj[key];

  if (Array.isArray(_value)) {
    return !!_value.length;
  } else if (Object.prototype.toString.call(_value) === `[object Object]`) {
    return JSON.stringify(obj[key]) === '{}';
  } else {
    return !!_value;
  }
}
// 校验表格必填字段
export function checkTableField(data, fieldObj) {
  if (!data || !data.length || !fieldObj) return;

  const fieldKeys = Object.keys(fieldObj);
  const _invalidRowIndex = data.findIndex((row) =>
    fieldKeys.some((key) => !checkValid(row, key))
  );
  if (_invalidRowIndex !== -1) {
    const _emptyField = fieldKeys.find(
      (key) => !checkValid(data[_invalidRowIndex], key)
    );
    return {
      flag: false,
      msg: fieldObj[_emptyField],
      index: _invalidRowIndex
    };
  }

  return {
    flag: true
  };
}
