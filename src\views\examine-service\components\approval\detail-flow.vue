<template>
  <apply-log
    title="审批日志"
    :process-instance-id="dataObj.processInstanceId"
    @refresh="resetDetails"
  />
</template>

<script>
  import './css/index.scss';
  import ApplyLog from '@/components/apply-log';
  import { processDetail } from '@/api/flow/process';

  export default {
    name: 'detail-flow',
    components: {
      ApplyLog
    },
    props: {
      id: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        dataObj: {}
      };
    },
    mounted() {
      this.resetDetails();
    },
    methods: {
      resetDetails() {
        const loading = this.$loading();
        processDetail(this.id)
          .then((res) => {
            this.getDetail(res.data.data);
          })
          .catch((err) => {
            console.error(err);
          })
          .finally(() => {
            loading.close();
          });
      },
      getDetail(data) {
        this.dataObj = data;
      }
    }
  };
</script>
