<template>
  <div v-if="status !== 3" class="apply-log">
    <h-panel title="审批日志">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          :icon="activity.icon"
          :color="activity.color"
        >
          <div class="log-content">
            <div>
              <span class="log-start">{{ activity.content }}</span>
              <div v-if="activity.avatars" class="avatar">
                <div v-for="({ name, avatar }, i) of activity.avatars" :key="i">
                  <div>
                    <img :src="avatar" />
                    <span>{{ name }}</span>
                  </div>
                </div>
              </div>
              <span @click="getUserInfo(activity)" class="image-photo">
                <img
                  v-if="activity.createUserPhoto"
                  :src="activity.createUserPhoto"
                />
                {{ activity.name }}
              </span>
            </div>
            <div class="log-end">
              <span>{{ activity.timestamp }}</span>
              <span :class="`log-end-${activity.status}`">{{
                activity.processName
              }}</span>
            </div>
          </div>
          <div v-if="activity.comment" class="footer">
            {{ activity.comment }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </h-panel>
    <user-info-dialog
      @close="userInfoVisible = false"
      @goToChatPage="goToChatPage"
      :visible="userInfoVisible"
      :user-info-id="userInfoId"
      :org-id="orgId"
    ></user-info-dialog>
  </div>
</template>
<script>
  import { oldCommonDetail } from '@/api/flow/process';
  import userInfoDialog from './user-info-dialog.vue';

  export default {
    components: { userInfoDialog },
    props: {
      processInstanceId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        activities: [],
        status: '',
        userInfoVisible: false,
        userInfoId: '',
        orgId: ''
      };
    },
    mounted() {
      this.getHistory();
    },
    methods: {
      getUserInfo({ userId, orgId }) {
        this.userInfoId = userId;
        this.orgId = orgId;
        this.userInfoVisible = true;
      },
      goToChatPage(userInfo) {
        this.$store.dispatch('chat/setChatUserInfo', userInfo);
        this.$router.push({ name: 'im' });
      },
      getHistory() {
        if (!this.processInstanceId) {
          return;
        }
        /**
        status 当前审批状态
        content: 当前审批节点名
        timestamp: 当前节点审批时间
        createUserPhoto: 当前节点人的头像
        icon  当前节点时间轴节点小图标
        processName: 当前节点审批状态
        name: 当前审批人姓名
        comment: 同意或拒绝意见
        avatar： 如果当前节点是抄送人需要传数组{ name: 姓名, avatar: 头像 }
      */
        oldCommonDetail(this.processInstanceId).then((res) => {
          if (!(res && res.data && res.data.success)) {
            return;
          }
          const { historyList, informUsers, status } = res.data.data;
          this.status = status;
          this.activities = (historyList || []).map((h) => {
            const flag = ['yes', 'no', 'recall'];
            const name = ['通过', '拒绝', '待审批'];
            const icon = `iconfont icon-process_${flag[h.status]}`;
            return {
              status: h.status,
              content: h.historyActivityName,
              timestamp: h.endTime,
              orgId: h.orgId,
              createUserPhoto: h.createUserPhoto,
              icon,
              processName: name[h.status],
              name: h.assigneeName,
              comment: h.comment,
              userId: h.assignee
            };
          });
          if (status === 1 && informUsers.length) {
            const avatars = informUsers.map((r) => {
              return {
                name: r.nickName,
                avatar: r.avatar,
                userId: r.assignee,
                orgId: r.orgId
              };
            });
            this.activities.push({ content: '抄送人', avatars });
          }
        });
      }
    }
  };
</script>
<style lang="scss">
  .apply-log {
    .el-timeline-item__icon {
      color: #0bbd87;
    }
    .icon-process_no {
      color: #ff5151;
    }
    .icon-process_recall {
      color: #ff791e;
    }

    .el-timeline-item {
      padding-bottom: 40px;
    }

    .is-bottom {
      display: none;
    }

    .h-panel {
      width: 100%;
    }

    .log-content {
      display: flex;
      justify-content: space-between;
      .image-photo {
        cursor: pointer;
      }
      .log-start {
        font-weight: 550;
        color: #333333;
        line-height: 22px;
      }
      .avatar {
        display: flex;
        flex-direction: inherit;
        flex-wrap: wrap;
        flex-direction: initial;
        margin-top: 6px;
        div {
          display: flex;
          flex-direction: initial;
          margin-right: 24px;
          line-height: 30px;
          img {
            margin-right: 8px;
          }
          span {
            margin-top: 0 !important;
          }
        }
      }
      .log-end {
        color: #999999;
        line-height: 22px;
        align-items: flex-end;
      }
      // .log-end span:last-child {
      //   color: #15bc84;
      // }
      .log-end .log-end-1 {
        color: #ff5151 !important;
      }
      .log-end .log-end-2 {
        color: #ff791e !important;
      }
      div {
        display: flex;
        flex-direction: column;
        span:last-child {
          display: flex;
          align-items: center;
          margin-top: 6px;
        }
      }
      img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-right: 8px;
      }
    }

    .footer {
      background: #f7f8fa;
      border-radius: 4px;
      padding: 16px;
      margin-top: 12px;
      color: #333;
      line-height: 22px;
      text-align: left;
    }
  }
</style>
