<template>
  <el-table
    style="width: 100%"
    border
    size="small"
    v-bind="bindProps()"
    :data="tempList"
    :cell-style="fontStyle"
    :header-cell-style="fontStyle"
  >
    <el-table-column type="index" label="序号" width="50" align="center">
    </el-table-column>
    <el-table-column label="被评价人" width="250" align="center">
      <template slot-scope="scope">
        {{ scope.row.userName || '---' }}
      </template>
    </el-table-column>
    <el-table-column
      label="被评价人员岗位"
      prop="post"
      width="250"
      align="center"
    >
      <template slot-scope="scope">
        {{ scope.row.post || '---' }}
      </template>
    </el-table-column>
    <el-table-column label="工号" align="center">
      <template slot-scope="scope">
        {{ scope.row.employeeNumber || '---' }}
      </template>
    </el-table-column>
    <el-table-column label="基本分值" align="center">
      <template slot-scope="scope">
        {{ scope.row.basicScore || '---' }}
      </template>
    </el-table-column>
    <el-table-column
      label="岗位业绩考核得分"
      prop="score"
      align="center"
      width="180"
    >
      <template slot-scope="scope">
        {{ scope.row.score | scoreFilter }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { mapState } from 'vuex';
  import { cloneDeep } from 'lodash';
  export default {
    name: 'evaluate-list-detail',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      list: {
        handler(newVal) {
          const temp = cloneDeep(newVal);
          this.tempList = temp.filter((item) => {
            return item.isMinister === 0;
          });
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        tempList: []
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      }
    }
  };
</script>
