<template>
  <div class="bc-form-build-computed">{{ value }}</div>
</template>

<script>
  export default {
    name: 'ComputedComp',
    props: {
      value: {
        type: String
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      form: {
        type: Object,
        default() {
          return {};
        }
      },
      totalValue: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    watch: {
      form: {
        handler() {
          this.init();
        },
        deep: true
      },
      totalValue: {
        handler() {
          this.init();
        },
        deep: true
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        let el = '';
        let result = '--';
        let formula = this.data.formula || [];
        if (formula.length) {
          try {
            formula.forEach(({ value = '' }) => {
              let newValue = value;
              if (newValue.length > 1) {
                newValue = this.form[value];
                if (newValue === undefined) {
                  newValue = this.totalValue[value];
                }
                if (typeof newValue === 'object' && newValue.value) {
                  newValue = newValue.value;
                }
                if (!newValue) {
                  newValue = 0;
                }
              }
              el += newValue;
            });
            /* eslint-disable-next-line */
          result = eval(el);
            if (isNaN(result) || Infinity === result) {
              result = '编辑的计算公式为空或不符合计算法则，无法计算';
            }
          } catch (error) {
            result = '编辑的计算公式为空或不符合计算法则，无法计算';
            // result = '--';
          }
        }
        this.$emit('input', result + '');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .bc-form-build-computed {
    line-height: 32.4px;
  }
</style>
