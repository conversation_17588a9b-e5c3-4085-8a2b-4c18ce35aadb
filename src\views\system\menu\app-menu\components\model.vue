<template>
  <el-dialog
    append-to-body
    top="60px"
    width="680px"
    :title="title"
    :visible.sync="open"
    :close-on-click-modal="false"
    @close="close"
  >
    <customize-form
      ref="customizeForm"
      v-bind="$attrs"
      :is-edit="isEdit"
      v-if="open"
    />
    <span slot="footer">
      <el-button type="primary" size="small" @click="ok">保 存</el-button>
      <el-button size="small" @click="open = false">返 回</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import CustomizeForm from './customize-form';
  import { update, add } from '@/api/system/menu';

  export default {
    components: {
      CustomizeForm
    },
    props: {
      title: {
        type: String,
        default: ''
      },
      visited: {
        type: Boolean,
        default: false
      },
      isEdit: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      open: {
        set(bool) {
          this.$emit('close', bool);
        },
        get() {
          return this.visited;
        }
      }
    },
    methods: {
      //关闭
      close() {
        this.open = false;
      },
      // 确定
      ok() {
        this.$refs.customizeForm
          .validateFn()
          .then((form) => {
            const data = Object.assign({}, form, {
              type: 'APP'
            });
            this.submit(data);
          })
          .catch((err) => {
            console.error(err);
          });
      },
      // 提交请求
      async submit(data) {
        try {
          if (this.isEdit) {
            await update(data);
          } else {
            await add(data);
          }
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.$emit('refresh');
          this.close();
        } catch (err) {
          console.error(err);
        }
      }
    }
  };
</script>
