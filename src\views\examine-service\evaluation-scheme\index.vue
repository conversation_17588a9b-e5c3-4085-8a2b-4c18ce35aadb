<template>
  <div>
    <transition-group name="fade">
      <el-row key="1" :gutter="20" v-show="!showEditPage">
        <el-col :span="24" class="page-height">
          <el-card class="page-height">
            <search ref="search" @search="searchQuery" />
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  v-if="permission.evaluation_add"
                  type="primary"
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAdd"
                  >新增
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-if="permission.evaluation_generate"
                  type="primary"
                  icon="el-icon-check"
                  size="mini"
                  :disabled="!multipleSelection.length"
                  @click="handleCreate"
                  >生成评价收集单
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="!multipleSelection.length"
                  @click="
                    handleBatchDelete(
                      multipleSelection.map((item) => item.code)
                    )
                  "
                  >删除
                </el-button>
              </el-col>
            </el-row>
            <table-info
              ref="tableInfo"
              :loading="loading"
              :tableData="tableData"
              @del="(ids) => handleBatchDelete(ids)"
              @select="handleTableSelect"
              @tableEmit="handleChildFn"
              @refresh="getList"
            />
            <yk-pagination
              small
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.current"
              :limit.sync="queryParams.size"
              @pagination="getList"
            />
          </el-card>
        </el-col>
      </el-row>
      <!-- 新增/编辑 -->
      <add-edit
        key="2"
        v-show="showEditPage"
        :visible="showEditPage"
        :schemeCode="schemeCode"
        :pageType="pageType"
        :pageTitle="pageTitle"
        @editEmit="handleChildFn"
        @refresh="getList"
      />
    </transition-group>
    <!-- 生成评价收集单 -->
    <create-collect
      v-if="showDialog"
      :originData="createOriginData"
      @dialogClose="showDialog = false"
      @refresh="getList"
    />
    <!-- 删除失败时打分方式失效方案列表弹框 -->
    <el-dialog
      title="删除失败方案列表"
      width="400px"
      append-to-body
      :visible.sync="failDialogShow"
    >
      <div>
        以下方案已被生成规则引用，不允许删除，可以置为不生效，重新新建方案。
      </div>
      <el-table :data="failData">
        <el-table-column
          property="schemeCode"
          label="方案编号"
        ></el-table-column>
        <el-table-column
          property="periodItemName"
          label="考核周期"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import { getListData, postSchemeDel } from '@/api/examine/evaluation-scheme';
  import { Search, TableInfo, CreateCollect, AddEdit } from './components';
  import { findByvalue } from '@/util/util';
  import { mapGetters } from 'vuex';

  // 评价方案
  export default {
    name: 'EvaluationScheme',
    components: {
      Search,
      TableInfo,
      CreateCollect,
      AddEdit
    },
    serviceDicts: ['assess_dimension', 'scheme_type'],
    provide() {
      return {
        indexDict: this.serviceDicts
      };
    },
    data() {
      return {
        loading: false,
        multipleSelection: [],
        tableData: [],
        queryParams: {
          current: 1,
          size: 10
        },
        total: 10,
        schemeCode: '',
        // 新增/编辑页
        pageType: '',
        showEditPage: false,
        pageTitle: '',
        // 生成评价收集单
        showDialog: false,
        createOriginData: {},
        // 删除失败方案列表
        failDialogShow: false,
        failData: []
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    methods: {
      // 使用mixins里内置的onDictReady方法监听字典获取完毕, 防止请求列表数据时未请求到字典导致表格字段显示数字
      onDictReady() {
        this.getList();
      },
      // 查询
      searchQuery(param) {
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          param
        );
        this.getList();
      },
      // 新增
      handleAdd() {
        this.pageType = 'add';
        this.pageTitle = '新增';
        this.showEditPage = true;
        this.schemeCode = undefined;
      },
      // 生成评价收集单
      handleCreate() {
        if (this.multipleSelection.length > 1) {
          return this.$message.warning('暂只支持根据一条方案生成评价收集单');
        }

        this.createOriginData = this.multipleSelection[0];
        this.showDialog = true;
      },
      // 重新获取人员信息
      handleRefresh() {},
      // 批量删除
      async handleBatchDelete(ids) {
        try {
          const res = await postSchemeDel(ids);
          const data = res.data.data;
          if (!data) {
            this.$message.success('删除方案成功');
            this.getList();
          } else if (data.length) {
            this.failDialogShow = true;
            this.failData = data;
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 请求列表数据
      async getList() {
        try {
          this.loading = true;
          const res = await getListData(this.queryParams);
          const data = res.data.data;
          this.total = data.total;
          this.tableData = data.records.map((item) => ({
            ...item,
            dimensionName: findByvalue(
              this.serviceDicts.type.assess_dimension,
              item.dimension
            )
          }));
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 表格多选事件
      handleTableSelect(val) {
        this.multipleSelection = val;
      },
      // 子页面事件监听
      handleChildFn(type, { showFlag, code, title }) {
        this.showEditPage = showFlag;
        if (type !== 'show') {
          this.pageTitle = title;
          this.pageType = type;
        }
        if (['edit', 'detail'].includes(type))
          [(this.schemeCode = code || this.schemeCode)];
      }
    }
  };
</script>

<style lang="scss" scoped>
  .page-height {
    box-sizing: border-box;
    height: calc(100vh - 125px);
    overflow-y: auto;

    &.overflow-x-scroll {
      position: relative;
      overflow-x: auto;

      &::-webkit-scrollbar {
        /* 滚动条整体样式 高宽分别对应横竖滚动条的尺寸 */
        width: 10px;
        height: 10px;
      }

      &::-webkit-scrollbar-thumb {
        /* 滚动条里面小方块 */
        background: #c4c4c4;
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(240, 240, 248, 10%);
      }

      &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        background: #ededed;
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(240, 240, 248, 10%);
      }
    }
  }
</style>
