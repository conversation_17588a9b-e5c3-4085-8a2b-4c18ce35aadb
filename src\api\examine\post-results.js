import request from '@/router/axios';

// 岗位业绩绩效结果单 - 详情
export function getDetail(params) {
  return request({
    url: '/api/examine/performance-form-post/detail',
    method: 'get',
    params
  });
}

// 岗位业绩绩效结果单 - 保存
export function postSave(data) {
  return request({
    url: '/api/examine/performance-form-post/save',
    method: 'post',
    data
  });
}

// 岗位业绩绩效结果单 - 提交
export function postSubmit(data) {
  return request({
    url: '/api/examine/performance-form-post/submit',
    method: 'post',
    data
  });
}

// 岗位业绩绩效结果单 - 审批
export const postApprove = (data) => {
  return request({
    url: `/api/examine/performance-form-post/approve`,
    method: 'post',
    data
  });
};

// 岗位业绩绩效结果单 - 撤销
export const getRepeal = (id) => {
  return request({
    url: `/api/examine/performance-form-post/revocation?processInstanceId=${id}`,
    method: 'get'
  });
};

// 岗位业绩绩效结果单 - 导出
export function getImport(params) {
  return request({
    url: '/api/examine/performance-form-post/export',
    method: 'get',
    params
  });
}
