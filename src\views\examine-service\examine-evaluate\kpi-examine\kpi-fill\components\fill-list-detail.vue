<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      :data="list"
      v-bind="bindProps()"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column type="index" label="序号" width="50" align="center" />
      <el-table-column
        label="考核指标"
        width="300"
        align="center"
        prop="indexName"
      />
      <el-table-column
        label="考核目标及计分标准"
        align="center"
        prop="standard"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="本周期考核完成情况(点击查看)"
        align="center"
        prop="completionSituation"
      >
        <template slot-scope="scope">
          <div
            class="show-text"
            @click="() => handleInput(scope.row, 'completionSituation')"
          >
            {{ scope.row.completionSituation || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="累计完成(点击查看)"
        align="center"
        prop="accumulatedCompletion"
      >
        <template slot-scope="scope">
          <div
            class="show-text"
            @click="() => handleInput(scope.row, 'accumulatedCompletion')"
          >
            {{ scope.row.accumulatedCompletion || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="证明材料" align="center">
        <template slot-scope="{ row }">
          <show-file
            v-if="row.attachList && row.attachList.length"
            :list="row.attachList"
          ></show-file>
          <span v-else>暂无</span>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      width="600px"
      :title="dialogTitle"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        maxlength="1000"
        show-word-limit
        disabled
        :rows="12"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { ShowFile } from '@/components/yk-upload-file';
  import { mapState } from 'vuex';
  export default {
    name: 'fill-list-detail',
    components: { ShowFile },
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        inputVisited: false,
        tempInput: '',
        dialogField: ''
      };
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
      // this.rowspan();
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      handleInput(row, field) {
        this.inputVisited = true;
        this.tempInput = row[field];
        this.dialogField = field;
      },
      // 完成情况弹窗关闭
      inputClose() {
        this.inputVisited = false;
      }
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      },
      dialogTitle() {
        const titleObj = {
          accumulatedCompletion: '累计完成',
          completionSituation: '完成情况'
        };
        return titleObj[this.dialogField];
      }
    }
  };
</script>

<style lang="scss" scoped>
  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
    cursor: pointer;
  }
</style>
