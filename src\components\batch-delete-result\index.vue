<template>
  <div>
    <el-dialog
      title="批量删除结果"
      width="555px"
      append-to-body
      :visible.sync="open"
    >
      <div class="content">
        <div class="res-text">
          <i class="el-icon-warning"></i>
          选择
          <span class="res-num">{{
            resData.successNumber + resData.failureNumber
          }}</span>
          条, 成功删除
          <span class="res-num">{{ resData.successNumber }}</span> 条
        </div>
        <el-table
          border
          :data="resData.detailVOList"
          style="width: 100%"
          size="small"
        >
          <el-table-column
            align="center"
            type="index"
            label="序号"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="item"
            label="操作内容"
            show-overflow-tooltip
          />
          <el-table-column
            align="center"
            prop="success"
            label="结果"
            width="60"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.success ? '成功' : '失败' }}</template
            >
          </el-table-column>
          <el-table-column
            align="center"
            prop="message"
            label="失败原因"
            width="300"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.success ? '--' : row.message }}</template
            >
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    name: 'batch-delete-result-modal',
    props: {
      // 弹框是否可见
      resVisible: {
        type: Boolean,
        default: false
      },
      // 删除结果
      resData: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    computed: {
      open: {
        get() {
          return this.resVisible;
        },
        set() {
          this.$emit('close');
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .content {
    .res-text {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .el-icon-warning {
        margin-right: 4px;
        color: #e6a23c;
        font-size: 24px;
      }

      .res-num {
        margin: 0 2px;
        color: red;
      }
    }
  }
</style>
