<template>
  <div class="app-tab">
    <el-badge
      v-for="{ value, num, label } of tabList"
      :key="value"
      :value="num"
      :max="99"
      :hidden="!num"
      :class="{ tabFocus: tabValue === value }"
    >
      <div @click="tabValueChange(value)">
        {{ label }}
      </div>
    </el-badge>
  </div>
</template>
<script>
  import { mapGetters } from 'vuex';
  export default {
    data() {
      return {
        tabValue: 1,
        tabList: [
          { label: '需我审批的', value: 1, num: 0 },
          { label: '我已审批的', value: 2, num: 0 },
          { label: '我发起的', value: 3, num: 0 },
          { label: '抄送我的', value: 4, num: 0 }
        ]
      };
    },
    computed: {
      ...mapGetters(['oabadgeTotal'])
    },
    watch: {
      oabadgeTotal(val, oldVal) {
        this.tabList.forEach((l) => {
          if (l.value === 1) {
            l.num = this.oabadgeTotal.todoNum;
          }
          if (l.value === 4) {
            l.num = this.oabadgeTotal.informUnReadNum;
          }
        });
        if (JSON.stringify(val) !== JSON.stringify(oldVal)) {
          this.$emit('refresh');
        }
      }
    },
    created() {
      this.$store.dispatch('cornerMark');
    },
    methods: {
      tabValueChange(value) {
        this.tabValue = value;
        this.$store.dispatch('cornerMark');
        this.$emit('set-tab-value', this.tabValue);
      }
    }
  };
</script>
<style lang="scss">
  .app-tab {
    display: flex;
    margin-bottom: 24px;
    padding-top: 20px;
    border-bottom: 1px solid #eaeaed;

    & > div {
      margin-right: 37px;
      padding-bottom: 14px;
      cursor: pointer;
    }

    .tabFocus {
      color: #409eff;
      border-bottom: 2px solid #409eff;
    }
  }
</style>
