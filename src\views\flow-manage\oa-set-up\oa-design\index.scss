@import "@/styles/element-ui";

.table-more {
  width: 120px;
  min-width: 120px;
  padding: 0;

  .table-status {
    display: flex;
    flex-direction: column;
    font-size: 14px;

    div {
      height: 34px;
      padding-left: 16px;
      color: #333;
      line-height: 34px;
      cursor: pointer;

      &:last-child:hover {
        color: #ff5151;
      }

      &:hover {
        color: #409EFF;
        background-color: #fafafa;
      }
    }
  }
}

.oa-list {
  position: relative;

  .cell > span {
    vertical-align: text-top;
  }

  .write-form {
    .iconfont {
      display: flex;
      justify-content: center;
      font-size: 14px;

      div {
        padding-left: 3px;
        font-size: 14px;
      }
    }

    &:hover {
      // background-color: $--color-white;
    }
  }

  .formName {
    // color: $--color-blue;
    cursor: pointer;
  }

  .h-table-container {
    width: 100%;
    padding-top: 18px;
  }

  .status-more {
    margin-left: 16px;
    cursor: pointer;
  }
}

.oa-detail {
  .el-popover__reference {
    display: none;
  }

  .right,
  .card-list,
  .iconfont {
    display: none;
  }

  .flow-path-card {
    cursor: auto !important;
    pointer-events: none;
  }
}

.oa-detail,
.oa-add {
  .h-block-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .detail-content {
      width: 680px;
    }

    .apply-design {
      width: 100%;

      // display: flex;
      // flex-direction:column;
      // align-items: center;
      & > div {
        display: inline-block !important;
      }

      & > header {
        width: 680px;
        margin: auto;
      }
    }

    header {
      margin-bottom: 7px;
      padding: 6px 0 12px 6px;
      color: #333;
      font-weight: 600;
      line-height: 16px;
      border-bottom: 1px solid #e9e9e9;
    }

    .detail-content > div {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 22px 0 12px;

      & > div > .node-wrap:last-of-type {
        .add-node-btn-box {
          padding: 0 !important;

          div {
            display: none;
          }
        }

        .actions {
          display: none;
        }
      }

      p {
        width: 95px;
        margin: 0;
        text-align: right;
      }

      p:last-child {
        display: flex;
        align-items: center;
        width: calc(100% - 95px);
        text-align: left;
        word-wrap: break-word;

        span {
          margin-left: 8px;
          color: #409EFF;
          cursor: pointer;
        }
      }
    }

    .image {
      justify-content: end !important;
      padding-bottom: 3px !important;

      img {
        width: 40px;
        height: 40px;
      }
    }

    .form {
      padding-top: 17px !important;
    }
  }
}

.rangeScope {
  max-width: 400px;
}

.oa-add {
  .image {
    margin-bottom: 20px;

    img {
      width: 40px;
      height: 40px;
    }
  }

  .range-people {
    width: 100%;
    padding: 0 12px;
    color: #cfcfcf;
    font-size: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
  }

  .select-range {
    overflow: hidden;
    color: #333;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .h-block-content .form {
    padding-top: 3px !important;
    padding-bottom: 41px !important;
  }

  .el-form-item {
    display: flex;
    margin-bottom: 24px;

    .el-form-item__label {
      width: 95px;
      padding: 0;
    }

    .el-form-item__content {
      flex: 1;
    }
  }

  .el-form-item__content {
    display: flex;
    align-items: center;
    width: calc(100% - 95px);

    img {
      width: 16px;
      height: 16px;
      margin-left: 8px;
      cursor: pointer;
    }
  }

  .footer {
    margin-top: 48px;

    button {
      width: 140px;
    }

    button:last-child {
      margin-left: 16px;
    }
  }
}
