<template>
  <div>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane
          v-for="(tab, index) in tabsAry"
          :key="index"
          :label="tab.label"
          :name="tab.name"
        ></el-tab-pane>
      </el-tabs>
      <search @search="handleQuery" />
    </basic-container>
    <basic-container :autoHeight="true" :zoomHeight="150">
      <table-info :source="tableData" :loading="loading" @dispatch="dispatch" />
      <yk-pagination
        small
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.current"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />
      <!-- 详情 -->
      <detail
        :formVisible="formVisible"
        :msgId="msgId"
        :hasRead="hasRead"
        :hasReadId="hasReadId"
        @close="handleDialogClose"
      />
    </basic-container>
  </div>
</template>
<script>
  import {
    getReceivePage,
    getDetail,
    postMsgRead
  } from '@/api/message/message';
  import Search from './components/search.vue';
  import TableInfo from './components/table-info.vue';
  import Detail from './components/detail.vue';

  const tabsAry = [
    {
      label: '消息中心',
      name: 'IN_APP'
    },
    {
      label: '待办中心',
      name: 'WORK_TODO'
    },
    {
      label: '预警中心',
      name: 'WARNING'
    },
    {
      label: '通知中心',
      name: 'NOTICE'
    }
  ];

  export default {
    name: 'Message_manage',
    components: {
      Search,
      TableInfo,
      Detail
    },
    data() {
      return {
        // tabs参数
        tabsAry,
        activeName: 'IN_APP',
        // 表格参数
        queryParams: {
          type: 'IN_APP',
          size: 10,
          current: 1
        },
        // 表格加载中
        loading: false,
        // 列表条目总数量
        total: 0,
        // 列表数据
        tableData: [],
        // 是否显示
        formVisible: false,
        msgId: '',
        hasRead: false,
        hasReadId: ''
      };
    },
    methods: {
      // 切换tab
      handleTabClick() {
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          {
            type: this.activeName
          }
        );
        this.request();
      },
      // 查询
      handleQuery(params) {
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          params
        );
        this.request();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 请求列表数据
      async request() {
        try {
          this.loading = true;
          const res = await getReceivePage(this.queryParams);
          const { total = 0, records = [] } = res.data.data;
          this.total = total;
          this.tableData = records;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 列表操作
      dispatch(type, data) {
        switch (type) {
          case 'view':
            return this.handleView(data);
          default:
            return false;
        }
      },
      handleView(row) {
        if (this.activeName === 'WORK_TODO') {
          this.getDetail(row);
        } else {
          this.formVisible = true;
          this.msgId = row.messageId;
          this.hasRead = row.hasRead === 1;
          this.hasReadId = row.id;
        }
      },
      // 获取待办详情获取url跳转审批详情页面
      async getDetail(row) {
        try {
          const { data = {} } = await getDetail(row.messageId);
          if (data.success) {
            let obj = data.data;
            // 如果消息未读,把消息状态更改为已读
            await postMsgRead({ id: row.id });
            await this.request();
            // this.$router.push(`${obj.url}&tabNum=1`);
            window.open(`#${obj.url}&tabNum=1`, '_blank');
          }
        } catch (e) {
          console.error(e);
        }
      },
      handleDialogClose() {
        this.request();
        this.formVisible = false;
        this.msgId = '';
        this.hasRead = false;
        this.hasReadId = '';
      }
    }
  };
</script>
<style lang="scss"></style>
