<template>
  <el-form
    ref="form"
    :inline="true"
    :model="form"
    size="small"
    label-suffix="："
  >
    <el-form-item label="指标名称" prop="indexName">
      <el-input
        v-model.trim="form.indexName"
        placeholder="请输入指标名称"
      ></el-input>
    </el-form-item>
    <el-form-item label="考核标准" prop="indexStandard">
      <el-input
        v-model.trim="form.indexStandard"
        placeholder="请输入考核标准"
      ></el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="handleQuery"
        >查询</el-button
      >
    </el-form-item>
    <el-form-item>
      <el-button icon="el-icon-delete" @click="onReset">清空</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
  export default {
    name: 'search',
    data() {
      return {
        form: {
          indexName: '',
          indexStandard: ''
        }
      };
    },
    mounted() {
      this.$emit('query', this.form);
    },
    methods: {
      handleQuery() {
        this.$emit('query', this.form);
      },
      onReset() {
        this.$refs.form.resetFields();
        this.handleQuery();
      }
    }
  };
</script>
