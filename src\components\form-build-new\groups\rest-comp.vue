<template>
  <c-form-build
    ref="formBuild"
    v-model="hValue"
    :component-list="componentList"
    :disabled="disabled"
    :rules="rules"
    :select-props="{ value: 'id', label: 'ruleName' }"
    :loading="loading"
  />
</template>

<script>
  import { getRuleList, getTimeInterval } from '@/api/desk/flow';
  import { deepClone } from '@/util/util';
  import { DATE_AND_HALF_DAY } from '@/const/validator';
  import { str2Date } from '@/util/date';

  export default {
    name: 'RestComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        list: [],
        unit: '1',
        hour: undefined,
        loading: {
          rest1: false,
          rest4: false
        }
      };
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      rules() {
        let rules = {
          rest2: [{ required: true, message: '请选择' }],
          rest3: [{ required: true, message: '请选择' }],
          rest4: [{ validator: this.validateRest4 }]
        };
        if (this.unit === '2') {
          rules.rest2.push(DATE_AND_HALF_DAY);
          rules.rest3.push(DATE_AND_HALF_DAY);
        }
        rules.rest2.push({ validator: this.validateRest2 });
        rules.rest3.push({ validator: this.validateRest3 });
        return rules;
      }
    },
    watch: {
      'hValue.rest1'(val) {
        let rule = this.list.find((item) => item.id === val);
        if (rule) {
          this.$set(this.hValue, 'rest6', rule.ruleName);
        }

        // 修改rest5
        this.setRest5(rule);

        // 修改rest2、rest3的unit和value和rest4的单位
        let unit = rule ? rule.unit : '1';
        if (unit !== this.unit) {
          this.unit = unit;
          unit = parseInt(rule.unit, 10);
          this.$set(this.hValue, 'rest2', '');
          this.$set(this.hValue, 'rest3', '');
          let componentList = deepClone(this.componentList);
          componentList[2].valueJson.dateType = unit;
          componentList[3].valueJson.dateType = unit;
          componentList[4].valueJson.unit = unit === 3 ? '小时' : '天';
          this.$emit('update:component-list', componentList);
        }

        // 修改rest4
        this.setRest4(rule);
      },
      'hValue.rest2'() {
        this.setRest4();
      },
      'hValue.rest3'() {
        this.setRest4();
      }
    },
    created() {
      this.$set(this.hValue, 'rest5', '');
      // this.init(); // 20221025
    },
    methods: {
      init() {
        this.$set(this.loading, 'rest1', true);
        getRuleList()
          .then((res) => {
            this.$set(this.loading, 'rest1', false);
            this.list = res.data.data || [];
            let componentList = deepClone(this.componentList);
            componentList[1].valueJson.options = this.list;
            this.$emit('update:component-list', componentList);
            if (this.value.rest1) {
              let rule = this.list.find((item) => item.id === this.value.rest1);
              if (rule) {
                this.unit = rule.unit;
                this.setRest4(rule);
                this.setRest5(rule);
              }
            }
          })
          .catch(() => {
            this.$set(this.loading, 'rest1', false);
          });
      },
      setRest4(rule) {
        this.$set(this.hValue, 'rest4', '');
        this.hour = undefined;
        let { rest1, rest2, rest3 } = this.hValue;
        if (rest1 && rest2 && rest3) {
          if (!rule) {
            rule = this.list.find((item) => item.id === rest1);
          }
          if (rest2 === rest3) {
            this.hour = 0;
          } else if (this.validateTime()) {
            this.$set(this.loading, 'rest4', true);
            this.hour = -1;
            getTimeInterval({
              flowType: 3,
              leaveTypeId: rest1,
              beginTime: rest2,
              endTime: rest3,
              dateUnit: rule.unit
            })
              .then((res) => {
                this.$set(this.loading, 'rest4', false);
                let { hour } = res.data.data || {};
                this.hour = hour;
                if (hour || hour === 0) {
                  this.$set(this.hValue, 'rest4', hour + '');
                }
              })
              .catch(() => {
                this.$set(this.loading, 'rest4', false);
                this.hour = -2;
              });
          }
        }
      },
      setRest5(rule) {
        if (!rule || rule.balance === -1) {
          this.$set(this.hValue, 'rest5', '');
        } else {
          let balanceArr = (rule.balance + '').split('.');
          if (!balanceArr[1]) {
            balanceArr[1] = '0';
          }
          let desc = `余额：${balanceArr.join('.')}${
            rule.unit === '3' ? '小时' : '天'
          }`;
          if ('调休' === rule.ruleName) {
            desc = `本月已提交${rule.applyNum}次，` + desc;
          }
          this.$set(this.hValue, 'rest5', desc);
        }
      },
      validateRest2(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('开始时间不得晚于结束时间'));
          return;
        }
        callback();
      },
      validateRest3(rule, value, callback) {
        if (!this.validateTime()) {
          callback(new Error('结束时间不得早于开始时间'));
          return;
        }
        callback();
      },
      validateRest4(rule, value, callback) {
        let { rest1, rest2, rest3 } = this.hValue;
        if (rest1 && rest2 && rest3) {
          if (this.hour === -1) {
            callback(new Error('正在计算时长，请等待'));
            return;
          }
          if (this.hour === -2) {
            callback(new Error('时长计算错误，请稍后重试'));
            return;
          }
          if (this.hour <= 0) {
            callback(new Error('时长必须大于0'));
            return;
          }
          let temp = this.list.find((item) => item.id === rest1);
          if (temp.balance !== -1 && this.hour > temp.balance) {
            callback(new Error('假期余额不足'));
            return;
          }
        }
        callback();
      },
      validateTime() {
        let { rest2, rest3 } = this.hValue;
        if (!rest2 || !rest3) {
          return true;
        }
        if (this.unit === '2') {
          rest2 = rest2.replace('上午', '00:00:00').replace('下午', '12:00:00');
          rest3 = rest3.replace('上午', '00:00:00').replace('下午', '12:00:00');
        }
        let startDate = str2Date(rest2);
        let endDate = str2Date(rest3);
        return startDate.getTime() <= endDate.getTime();
      },
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formBuild
            .validate()
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>
