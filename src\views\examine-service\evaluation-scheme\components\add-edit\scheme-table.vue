<template>
  <div>
    <div>
      <div class="table-title">方案配置</div>
      <el-button
        v-if="!isDetail"
        type="primary"
        plain
        icon="el-icon-plus"
        size="small"
        @click="handleAdd"
        >新 增
      </el-button>
    </div>
    <el-table
      size="small"
      max-height="464"
      :data="tableData"
      v-loading="loading"
      style="width: 100%"
    >
      <el-table-column align="center" type="index" label="序号" width="55" />
      <el-table-column
        align="center"
        prop="assessedDeptNames"
        label="被评价机构"
      >
        <template slot-scope="{ row }">
          <el-tooltip
            effect="dark"
            placement="top"
            :disabled="!row.assessedDeptList.length"
            :content="transferAryToString(row.assessedDeptList)"
          >
            <div class="dialog-div-wrapper disabled">
              {{ transferAryToString(row.assessedDeptList) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="totalScore"
        label="总分"
        width="90"
      />
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="{ row, $index }">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-edit-outline"
            v-if="!isDetail"
            @click="handleEditSelect(row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            size="mini"
            :icon="isDetail ? 'el-icon-view' : 'el-icon-edit'"
            @click="handleSelectIndicator(row)"
            >{{ isDetail ? '查看指标' : '选择指标' }}</el-button
          >
          <el-popconfirm
            v-if="!isDetail"
            style="margin-left: 8px"
            title="确定 删除 此数据吗？"
            @confirm="() => handleDelete($index)"
          >
            <el-button
              type="text"
              size="mini"
              icon="el-icon-delete"
              class="button-del"
              slot="reference"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- 新增表格行弹窗 - 机构 -->
    <yk-select-org
      :open="visited"
      :title="selectTitle"
      :rowData="rowData"
      v-bind="$attrs"
      @close="close"
      @emitSubmit="handleSelectUserEmit"
      @deptSubmit="handleSelectDeptEmit"
    />

    <!-- 选择指标弹窗 -->
    <indicator-select
      v-if="showDialog"
      :rowData="rowData"
      :isDetail="isDetail"
      :dimension="dimension"
      @dialogClose="handleIndicatorDialogClose"
      @dialogSave="handleDialogSave"
    />
  </div>
</template>
<script>
  import IndicatorSelect from './indicator-select.vue';
  import { checkTableField, transferAryToString } from '../util';
  import { cloneDeep, uniqBy } from 'lodash';
  // 根据部门岗位选择用户
  import { YkSelectUserByOrgPost } from '@/components/yk-select-org';
  // 部门选择
  import { YkSelectOrg } from '@/components/yk-select-org';

  export default {
    components: {
      IndicatorSelect,
      YkSelectUserByOrgPost,
      YkSelectOrg
    },
    props: {
      schemeTableData: {
        type: Array,
        default() {
          return [];
        }
      },
      canAddIndicator: {
        type: Boolean,
        default: false
      },
      isDetail: {
        type: Boolean,
        default: false
      },
      dimension: {
        type: String,
        default: ''
      }
    },
    watch: {
      schemeTableData: {
        handler(val) {
          if (val && val.length) {
            this.tableData = cloneDeep(this.schemeTableData);
            this.tableData.forEach((item) => {
              item.assessedDeptNames = item.assessedDeptList.map(
                ({ name }) => name
              );
              item.assessedDeptIds = item.assessedDeptList.map(({ id }) => id);
              if (item.assessedPostList) {
                item.assessedPostIds = item.assessedPostList.map(
                  ({ id }) => id
                );
              }
            });
          }
        },
        deep: true,
        default: true
      }
    },
    data() {
      return {
        transferAryToString,
        tableData: [],
        loading: false,
        // 被评价人弹窗属性
        visited: false,
        selectType: '',
        selectTitle: '',
        // 选择指标弹窗属性
        showDialog: false,
        rowData: {}
      };
    },
    methods: {
      uniqBy,
      // 新增
      handleAdd() {
        if (!this.canAddIndicator) {
          return this.$message.warning('请先选择考核维度');
        }
        this.selectType = 'add';
        this.rowData = {};
        this.selectTitle = '新增方案配置';
        this.visited = true;
      },
      // 选择指标
      handleSelectIndicator(row) {
        this.rowData = row;
        this.showDialog = true;
      },
      // 删除表格行
      handleDelete(index) {
        this.tableData.splice(index, 1);
      },
      // 清空表格
      clearTable() {
        this.tableData.splice(0, this.tableData.length);
      },
      // 提交
      submitTable() {
        if (!this.tableData.length) {
          return { submitFlag: false, data: `请配置至少一条方案` };
        }
        // 如果考核维度是机构, 只校验机构和指标
        let fieldObj = {
          assessedDeptList: '请选择被评价人机构',
          schemeItemIndexList: '请选择至少一个指标'
        };
        const { flag, index, msg } = checkTableField(this.tableData, fieldObj);
        if (!flag) {
          return {
            submitFlag: false,
            data: `第 ${index + 1} 行数据有误: ${msg}`
          };
        } else {
          return { submitFlag: true, data: this.tableData };
        }
      },
      // 选择被评价人回调
      handleSelectUserEmit(userData, deptData, postData) {
        const assessedDeptList = deptData.map(({ title: name, id }) => ({
          id,
          name
        }));
        const assessedPostList = postData.map(({ postName: name, id }) => ({
          id,
          name
        }));
        const assessedUserList = userData.map(
          ({ userId: id, name, deptId, deptName, postId, postName }) => ({
            id,
            name,
            deptId,
            deptName,
            postId,
            postName
          })
        );
        // 新增
        if (this.selectType === 'add') {
          this.tableData.push({
            assessedDeptList,
            assessedPostList,
            assessedUserList,
            schemeItemIndexList: [],
            totalScore: 0
          });
        } else if (this.selectType === 'edit') {
          this.rowData.assessedDeptList = assessedDeptList;
          this.rowData.assessedPostList = assessedPostList;
          this.rowData.assessedUserList = assessedUserList;
        }
      },
      // 选择部门回调
      handleSelectDeptEmit(deptData) {
        const assessedDeptList = deptData.map(({ title: name, id }) => ({
          id,
          name
        }));
        // 新增
        if (this.selectType === 'add') {
          this.tableData.push({
            assessedDeptList,
            schemeItemIndexList: [],
            totalScore: 0
          });
        } else if (this.selectType === 'edit') {
          this.rowData.assessedDeptList = assessedDeptList;
        }
      },
      // 编辑被评价人
      handleEditSelect(row) {
        this.rowData = row;
        this.selectType = 'edit';
        this.selectTitle = '编辑方案配置';
        this.visited = true;
      },
      // 关闭选择被评价人
      close(bool) {
        this.rowData = {};
        this.visited = bool;
      },
      // 保存选择指标弹窗数据
      handleDialogSave({ data, totalScore }) {
        this.rowData.totalScore = totalScore;
        this.rowData.schemeItemIndexList = data;
        this.handleIndicatorDialogClose();
      },
      // 选择指标弹窗关闭
      handleIndicatorDialogClose() {
        this.rowData = {};
        this.showDialog = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .table-title {
    display: inline-block;
    margin-right: 12px;
    padding: 10px 20px;
    color: #37a4f2;
    border-bottom: 1px solid #37a4f2;
  }
</style>
