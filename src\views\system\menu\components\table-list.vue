<template>
  <el-table
    size="small"
    row-key="id"
    :data="list"
    :tree-props="{ children: 'children', hasChildren: 'hasChildrens' }"
    @selection-change="handleSelections"
  >
    <el-table-column type="selection" width="55"> </el-table-column>
    <el-table-column
      v-if="columns[0].visible"
      prop="name"
      align="left"
      label="菜单名称"
      :show-overflow-tooltip="true"
    ></el-table-column>
    <el-table-column
      v-if="columns[1].visible"
      prop="path"
      align="center"
      label="路由地址"
      :show-overflow-tooltip="true"
    >
      <template slot-scope="scope">
        <div v-if="scope.row.category !== 2">{{ scope.row.path || '--' }}</div>
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[2].visible"
      prop="source"
      align="center"
      label="菜单图标"
      width="80px"
    >
      <template slot-scope="scope">
        <i :class="scope.row.source"></i>
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[3].visible"
      prop="category"
      align="center"
      label="菜单类型"
    >
      <template slot-scope="scope">
        {{ scope.row.category | menuType }}
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[7].visible"
      prop="code"
      align="center"
      label="权限字符"
    ></el-table-column>
    <el-table-column
      v-if="columns[4].visible"
      prop="isHide"
      align="center"
      label="菜单状态"
      :show-overflow-tooltip="true"
    >
      <template slot-scope="scope">
        <el-tag v-if="scope.row.isHide === 1 && scope.row.category !== 2">{{
          scope.row.isHide | hideType
        }}</el-tag>
        <el-tag
          v-else-if="scope.row.isHide === 2 && scope.row.category !== 2"
          type="danger"
          >{{ scope.row.isHide | hideType }}</el-tag
        >
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[5].visible"
      prop="isKeepAlive"
      width="100"
      align="center"
      label="是否缓存"
      :show-overflow-tooltip="true"
    >
      <template slot-scope="scope" v-if="scope.row.category !== 2">
        {{ scope.row.isKeepAlive | keepAliveType }}
      </template>
    </el-table-column>
    <el-table-column
      v-if="columns[6].visible"
      width="100"
      prop="sort"
      align="center"
      label="菜单排序"
      :show-overflow-tooltip="true"
    ></el-table-column>
    <el-table-column fixed="right" align="center" label="操作" width="250">
      <template slot-scope="scope">
        <el-button
          v-if="permissionWrapper.edit"
          type="text"
          icon="el-icon-edit"
          size="small"
          @click="$emit('dispatch', 'edit', scope.row)"
          >编辑</el-button
        >
        <el-button
          v-if="permissionWrapper.del"
          type="text"
          icon="el-icon-delete"
          size="small"
          style="color: red"
          @click="$emit('dispatch', 'delete', scope.row)"
          >删除</el-button
        >
        <el-button
          v-if="
            permissionWrapper.add &&
            scope.row.category !== 2 &&
            userInfo.role_name.includes('admin')
          "
          type="text"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click="$emit('dispatch', 'add_child', scope.row)"
          >新增子项</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { mapGetters } from 'vuex';

  export default {
    props: {
      columns: {
        type: Array,
        default: function () {
          return [];
        }
      },
      list: {
        type: Array,
        default: function () {
          return [];
        }
      },
      type: {
        type: String,
        default: 'pc'
      }
    },
    computed: {
      ...mapGetters(['userInfo', 'permission']),
      permissionWrapper() {
        switch (this.type) {
          case 'pc':
            return {
              add: this.permission.menu_add,
              edit: this.permission.menu_edit,
              del: this.permission.menu_delete
            };
          case 'app':
            return {
              add: this.permission.app_menu_add,
              edit: this.permission.app_menu_edit,
              del: this.permission.app_menu_delete
            };
        }
      }
    },
    filters: {
      menuType(val) {
        switch (val) {
          case 1:
            return '菜单';
          case 2:
            return '按钮';
          default:
            return '';
        }
      },
      hideType(val) {
        switch (val) {
          case 1:
            return '显示';
          case 2:
            return '隐藏';
          default:
            return '';
        }
      },
      keepAliveType(val) {
        switch (val) {
          case 1:
            return '缓存';
          case 2:
            return '不缓存';
          default:
            return '';
        }
      }
    },
    methods: {
      handleSelections(rows) {
        const ids = rows.map((item) => item.id);
        this.$emit('dispatch', 'selections', ids);
      }
    }
  };
</script>
