<template>
  <div>
    <el-dialog
      width="900px"
      :visible="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="考核周期"
      @close="handleCancel"
    >
      <!-- 主体 -->
      <el-form
        ref="form"
        :model="queryParams"
        size="mini"
        label-width="90px"
        label-suffix=": "
      >
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="周期类型" prop="type">
              <el-select
                v-model="queryParams.type"
                placeholder="请选择考核维度"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in serviceDicts.type.period_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="期间名称" prop="name">
              <el-input
                placeholder="请输入期间名称"
                v-model="queryParams.name"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="getPeriodListData"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >清空</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-table
        v-loading="loading"
        :data="tableData"
        ref="periodTable"
        size="small"
        style="width: 100%"
        height="500px"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column align="center" type="index" label="序号" width="60">
        </el-table-column>
        <el-table-column
          align="center"
          prop="typeName"
          label="周期类型"
          width="144px"
        />
        <el-table-column align="center" prop="yearly" label="年份" width="60">
        </el-table-column>
        <el-table-column align="center" prop="itemName" label="期间名称">
        </el-table-column>
        <el-table-column align="center" prop="startTime" label="开始时间">
        </el-table-column>
        <el-table-column align="center" prop="endTime" label="结束时间">
        </el-table-column>
        <el-table-column align="center" prop="deadline" label="考评截止日期">
          <template slot-scope="{ row }">
            {{ row.deadline || '-' }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 弹窗按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button class="cancle" size="small" @click="handleCancel"
          >返回</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handleSubmit"
          class="confirm"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { getPeriodList } from '@/api/examine/evaluation-scheme';

  export default {
    name: 'period-select',
    serviceDicts: ['period_type'],
    props: {
      selectPeriodId: String
    },
    data() {
      return {
        queryParams: {
          deptId: 1,
          type: undefined,
          name: undefined
        },
        loading: false,
        tableData: [],
        // 选中行
        currentRow: {}
      };
    },
    created() {
      this.getPeriodListData();
    },
    methods: {
      // 获取周期数据
      async getPeriodListData() {
        try {
          this.loading = true;
          const res = await getPeriodList(this.queryParams);
          const data = res.data.data;
          this.tableData = data;

          if (this.selectPeriodId) {
            this.setCurrent(
              data.find((item) => item.id === this.selectPeriodId)
            );
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.getPeriodListData();
      },
      // 设置选中行
      setCurrent(row) {
        this.$refs.periodTable.setCurrentRow(row);
      },
      // 行选中事件
      handleCurrentChange(val) {
        this.currentRow = val;
      },
      // 返回
      handleCancel() {
        this.$emit('dialogClose');
      },
      // 保存
      handleSubmit() {
        this.$emit('dialogSave', this.currentRow);
      }
    }
  };
</script>
<style lang="scss" scoped>
  ::v-deep .el-table__row {
    &.current-row {
      box-shadow: 0 0 10px 1px rgba(0, 0, 0, 20%);

      td.el-table__cell {
        background: #e7dbca;
      }
    }
  }
</style>
