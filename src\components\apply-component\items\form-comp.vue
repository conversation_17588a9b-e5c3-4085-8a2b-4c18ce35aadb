<template>
  <div class="bc-apply-component-form">
    <el-card
      v-for="(item, index) in value"
      :key="index"
      class="box-card"
      shadow="never"
    >
      <div slot="header" class="clearfix">{{ data.name }}{{ index + 1 }}</div>
      <apply-component
        :value="value[index]"
        :component-list="componentList"
        :accept="accept"
        :total-value="totalValue"
      ></apply-component>
    </el-card>
  </div>
</template>

<script>
  export default {
    name: 'FormComp',
    components: {
      ApplyComponent: () => import('@/components/apply-component')
    },
    props: {
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      accept: String,
      totalValue: Object
    }
  };
</script>

<style lang="scss" scoped>
  .bc-apply-component-form {
    .el-card {
      border-color: #d9d9d9;
      margin-bottom: 18px;
    }
  }
</style>

<style lang="scss">
  .bc-apply-component-form {
    .el-card {
      .el-card__header {
        padding: 16px 12px !important;
        background: #fafafa;
        border-color: #d9d9d9;
        .clearfix {
          line-height: 15px;
        }
      }
      .el-card__body {
        padding: 18px 18px 0 !important;
      }
    }
  }
</style>
