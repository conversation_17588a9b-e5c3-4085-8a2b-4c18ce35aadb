<template>
  <el-table
    :data="source"
    size="small"
    border
    style="width: 100%; margin-top: 10px"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="50" />
    <el-table-column type="index" label="序号" align="center" width="60">
    </el-table-column>
    <el-table-column prop="code" label="打分方式编码" align="center">
    </el-table-column>
    <el-table-column prop="name" label="打分方式名称" align="center">
    </el-table-column>
    <el-table-column prop="type" label="类型" align="center">
      <template slot-scope="scope">
        {{ scope.row.type | filterDafenType }}
      </template>
    </el-table-column>
    <el-table-column prop="status" label="是否生效" align="center">
      <template slot-scope="scope">
        {{ scope.row.status | filterDafenStatus }}
      </template>
    </el-table-column>
    <el-table-column prop="action" align="center" label="操作" width="200px">
      <template slot-scope="scope">
        <el-button
          type="text"
          icon="el-icon-view"
          size="mini"
          @click="() => dispatch('detail', scope.row)"
          >查看</el-button
        >
        <el-button
          type="text"
          icon="el-icon-edit"
          size="mini"
          @click="() => dispatch('edit', scope.row)"
          >编辑</el-button
        >
        <el-popconfirm
          title="确定删除此条打分方式吗？"
          size="mini"
          @confirm="() => dispatch('delete', scope.row)"
        >
          <el-button
            type="text"
            icon="el-icon-delete"
            slot="reference"
            style="margin-left: 10px; color: red"
            >删除</el-button
          >
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-list',
    props: {
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    methods: {
      dispatch(type, row) {
        this.$emit('dispatch', type, row);
      },
      handleSelectionChange(arr) {
        this.$emit('dispatch', 'multiple', arr);
      }
    }
  };
</script>
