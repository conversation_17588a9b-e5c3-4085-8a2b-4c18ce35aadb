import website from '@/config/website';
export default {
  bind: (el) => {
    let src = el.getAttribute('src');
    if (!src.startsWith('http://') && !src.startsWith('https://')) {
      let newSrc = `${website.imgOssPath}${src}`;
      el.setAttribute('src', newSrc);
    }
  },
  update: (el) => {
    let src = el.getAttribute('src');
    if (!src.startsWith('http://') && !src.startsWith('https://')) {
      let newSrc = `${website.imgOssPath}${src}`;
      el.setAttribute('src', newSrc);
    }
  }
};
