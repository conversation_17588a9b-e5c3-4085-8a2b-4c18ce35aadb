<template>
  <div>
    <edit-apply-log
      v-if="users.length"
      ref="selectPeople"
      :id="id"
      :apply-log-list="users"
      @change="updateLogList"
    />
    <el-empty v-else description="暂无审批流程" :image-size="60" />
  </div>
</template>

<script>
  import { getFlowInfo } from '@/api/flow/process';
  import EditApplyLog from '@/components/apply-log/edit-apply-log';
  export default {
    name: 'Apply',
    components: {
      EditApplyLog
    },
    data() {
      return {
        users: []
      };
    },
    props: {
      id: {
        type: String,
        default: ''
      }
    },
    mounted() {
      this.init();
    },
    methods: {
      // 流程定义ID
      init() {
        this.loading = true;
        getFlowInfo(this.id)
          .then((res) => {
            let { processName, hasCondition, usersNodeinfos, id } =
              res.data.data || {};
            this.processName = processName || '';
            this.hasCondition = hasCondition || 0;
            this.processId = id;
            if (!hasCondition) {
              this.users = usersNodeinfos || [];
            }
          })
          .catch(() => {
            this.loading = false;
          });
      },
      updateLogList(v) {
        this.users = v;
      }
    }
  };
</script>
