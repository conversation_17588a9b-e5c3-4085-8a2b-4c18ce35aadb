import request from '@/router/axios';

export const getPage = (current, size, params) => {
  return request({
    url: '/api/demo/product/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getList = (current, size, params) => {
  return request({
    url: '/api/demo/product/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getDetail = (id) => {
  return request({
    url: '/api/demo/product/fetchById',
    method: 'get',
    params: {
      id
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/demo/product/remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const save = (row) => {
  return request({
    url: '/api/demo/product/save',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/demo/product/save',
    method: 'post',
    data: row
  });
};
