<template>
  <div class="wrapper" v-loading="loading">
    <!--  基础信息  -->
    <el-collapse style="border: 0" v-model="collapse">
      <el-collapse-item name="one">
        <h2 slot="title" class="title">基础信息</h2>
        <basic-info
          v-model="detailForm.evaluationDate"
          :form="detailForm"
          :isDetail="isDetail"
        />
      </el-collapse-item>
    </el-collapse>
    <section>
      <div class="title_wrapper">
        <h2 class="title">指标完成情况</h2>
        <el-tooltip effect="dark" content="全屏展示" placement="top-start">
          <i
            class="el-icon-full-screen icon-full"
            @click.stop="visited = true"
          ></i>
        </el-tooltip>
      </div>
      <!--   详情   -->
      <evaluate-list-detail v-if="isDetail" :list="listData" />
      <!--   评价   -->
      <evaluate-list
        v-else
        :key="1"
        :list="listData"
        :max-height="300"
        :period-num="detailForm.periodNumber"
        @sync="(val) => (listData = val)"
      />
      <div class="end-btn">
        <el-button
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </section>
    <!--   全屏操作   -->
    <full-screen-table v-model="visited" :title="title">
      <!--   详情   -->
      <evaluate-list-detail
        v-if="isDetail"
        full
        :max-height="0"
        :list="listData"
      />
      <evaluate-list
        v-else
        full
        :key="2"
        :list="listData"
        :max-height="0"
        :period-num="detailForm.periodNumber"
        @sync="(val) => (listData = val)"
      />
      <div class="end-btn">
        <el-button
          icon="el-icon-circle-close"
          size="small"
          :disabled="disabled"
          @click="$emit('close')"
          >返回</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          :disabled="disabled"
          :loading="loadingSave"
          @click="() => save('save')"
          >保存</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          icon="el-icon-circle-check"
          size="small"
          :disabled="disabled"
          :loading="loadingSubmit"
          @click="() => save('submit')"
          >提交</el-button
        >
      </div>
    </full-screen-table>
  </div>
</template>

<script>
  import { BasicInfo, EvaluateList, EvaluateListDetail } from './components';
  import { FullScreenTable } from '@/views/examine-service/components/full-screen';
  import {
    getKpiEvaluateDetail,
    postKpiEvaluateSave
  } from '@/api/examine/examine-evaluate';

  export default {
    name: 'evaluate',
    components: {
      BasicInfo,
      EvaluateList,
      EvaluateListDetail,
      FullScreenTable
    },
    props: {
      rowCode: {
        type: String,
        default: ''
      },
      isDetail: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        listData: [],
        collapse: 'one',
        visited: false,
        detailForm: {},
        loadingSave: false,
        loadingSubmit: false,
        disabled: false,
        loading: false
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      // 页面初始化
      async init() {
        try {
          this.loading = true;
          const res = await getKpiEvaluateDetail(this.rowCode);
          this.detailForm = res.data.data;
          this.listData = this.detailForm.resultList || [];
          this.listData.map((item) => {
            if (typeof item.calculatePmScore === 'string') {
              item.calculatePmScore = Number(item.calculatePmScore);
            }
            return item;
          });
          if (!this.detailForm.evaluationDate) {
            this.detailForm.evaluationDate = new Date()
              .toLocaleDateString()
              .replaceAll('/', '-');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      save(type) {
        // 保存
        if (type === 'save') {
          this.postData(type);
        }
        // 提交
        if (type === 'submit') {
          // 校验是否所有机构都为已填报
          if (this.listData.some((item) => item.fillingProgress === 0)) {
            return this.$message.warning('存在机构未提报, 请核对');
          }

          if (!this.detailForm.evaluationDate) {
            return this.$message.warning('评价日期未选择，请选择！');
          }

          // 校验必填项
          // const isNull = this.listData.some((item) => {
          //   if (typeof item.calculatePmScore !== 'number') {
          //     return true;
          //   }
          // });
          // if (isNull) {
          //   return this.$message.warning('存在未填项，请核对填写');
          // }
          this.postData(type);
        }
      },
      // 提交数据
      async postData(type) {
        try {
          this.disabled = true;
          if (type === 'save') {
            this.loadingSave = true;
          } else {
            this.loadingSubmit = true;
          }
          const formData = Object.assign({}, this.detailForm, {
            action: type,
            resultList: this.listData
          });
          const res = await postKpiEvaluateSave(formData);
          const data = res.data;
          if (data.code === 200) {
            this.$message.success(data.msg);
            this.$emit('close');
            this.$emit('refresh');
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.disabled = false;
          this.loadingSave = false;
          this.loadingSubmit = false;
        }
      }
    },
    computed: {
      title() {
        return this.isDetail ? '查看详情' : '评价详情';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    padding-left: 15px;
    font-weight: 400;
    font-size: 15px;
    line-height: 30px;

    &::before {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 6px;
      height: 24px;
      background-color: #51a2ff;
      border-radius: 15px;
      content: '';
    }
  }

  .wrapper {
    margin-top: 10px;
  }

  .title_wrapper {
    position: relative;

    .icon-full {
      position: absolute;
      right: 0;
      bottom: 0;
      margin-right: 10px;
      font-size: 20px;
      line-height: 32px;
      cursor: pointer;
    }
  }

  .end-btn {
    padding: 20px 0;
    text-align: center;
  }
</style>
