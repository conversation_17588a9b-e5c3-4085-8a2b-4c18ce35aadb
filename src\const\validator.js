// 表单校验项
import {
  validateMobilephone,
  validateTelephone,
  // validateNaturalNumber,
  validateNaturalFloat2,
  // validateSpecialCharacters,
  // validatePositiveInteger
  validatePassword,
  // validateCreditCode,
  cardid,
  bankCardCode,
  number,
  lettersAndNumbers,
  isEmail,
  macAddress
} from '@/util/validate';
import { userCheck } from '@/api/user';

// 电话
export const PHONE = {
  validator: (rule, value, callback) => {
    if (value && !validateMobilephone(value) && !validateTelephone(value)) {
      callback(new Error('请输入正确的固话或手机号码'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 座机
export const TELEPHONE = {
  validator: (rule, value, callback) => {
    if (value && !validateTelephone(value)) {
      callback(new Error('请输入正确的固话号码'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};
// 手机号
export const MOBILE_PHONE = {
  validator: (rule, value, callback) => {
    if (value && !validateMobilephone(value)) {
      callback(new Error('请输入正确的手机号码'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};
// 注册手机号
export const REGISTER_MOBILE_PHONE = {
  validator: (rule, value, callback) => {
    if (!value) {
      callback();
    } else if (value !== '' && !validateMobilephone(value)) {
      callback(new Error('请输入正确的手机号码'));
    } else {
      try {
        userCheck(value)
          .then((res) => {
            if (!res.data.data.accountExisted) {
              callback(new Error('手机号未注册,请前往注册'));
            } else if (!res.data.data.hasSetPassword) {
              callback(
                new Error('你的账户未设置密码，请采用短信验证码登录的方式')
              );
            } else {
              callback();
            }
          })
          .catch(() => {
            callback();
          });
      } catch {
        callback();
      }
    }
  },
  trigger: 'blur'
};
// 注册手机号
export const REGISTER_OK_MOBILE_PHONE = {
  validator: (rule, value, callback) => {
    if (!value) {
      callback();
    } else if (value !== '' && !validateMobilephone(value)) {
      callback(new Error('请输入正确的手机号码'));
    } else {
      userCheck(value).then((res) => {
        if (res && res.data && res.data.data && res.data.data.accountExisted) {
          callback(new Error('手机号已注册,请前往登录'));
        } else {
          callback();
        }
      });
    }
  },
  trigger: 'blur'
};

// 社会信用代码
export const CREDIT_CODE = {
  validator: (rule, value, callback) => {
    value = value.trim();
    if (!value || (value.length !== 15 && value.length !== 18)) {
      callback(new Error('请输入正确的统一信用代码'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 银行卡
export const BANK_CARD_CODE = {
  validator: (rule, value, callback) => {
    if (value !== '' && !bankCardCode(value)) {
      callback(new Error('请输入正确银行卡号'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// // 自然数
// export const NATURAL_NUMBER = {
//   validator: (rule, value, callback) => {
//     if (value !== "" && !validateNaturalNumber(value)) {
//       callback(new Error("请输入自然数"));
//     } else {
//       callback();
//     }
//   }
// };

// // 正整数
// export const POSITIVE_INTEGER = {
//   validator: (rule, value, callback) => {
//     if (value !== "" && !validatePositiveInteger(value)) {
//       callback(new Error("请输入正整数"));
//     } else {
//       callback();
//     }
//   }
// };

// 自然小数（含0，保留2位小数）
export const NATURAL_FLOAT_2 = {
  validator: (rule, value, callback) => {
    if (value !== '' && !validateNaturalFloat2(value)) {
      callback(new Error('请输入自然数或正小数，小数点后最多2位'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 密码
export const PASSWORD = {
  validator: (rule, value, callback) => {
    if (!value) {
      callback();
    } else if (!validatePassword(value)) {
      callback(
        new Error('密码最少6位，必须包含小写字母、大写字母、数字任意2项')
      );
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 身份证号
export const ID_CARD = {
  validator: (rule, value, callback) => {
    let [result, msg] = cardid(value);
    callback(result ? new Error(msg) : undefined);
  },
  trigger: 'blur'
};
// 身份证号
export const ID_CARD_NO_IMPORT = {
  validator: (rule, value, callback) => {
    if (!value) {
      callback();
      return;
    }
    let [result, msg] = cardid(value, true);
    callback(result ? new Error(msg) : undefined);
  },
  trigger: 'blur'
};

// 数字
export const NUMBER = {
  validator: (rule, value, callback) => {
    if (!number(value)) {
      callback(new Error('请输入数字'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 字母+数字
export const LETTERS_AND_NUMBERS = {
  validator: (rule, value, callback) => {
    if (!lettersAndNumbers(value)) {
      callback(new Error('请输入字母或数字'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 邮箱
export const EMAIL = {
  validator: (rule, value, callback) => {
    if (value && !isEmail(value)) {
      callback(new Error('请输入正确的邮箱'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// MAC地址
export const MAC_ADDRESS = {
  validator: (rule, value, callback) => {
    if (!macAddress(value)) {
      callback(new Error('请输入正确的MAC地址'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 全部空格
export const NO_ALL_SPACE = {
  validator: (rule, value, callback) => {
    if (typeof value === 'string' && value.trim() === '') {
      callback(new Error('请勿全部输入空格'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};

// 日期+上午/下午
export const DATE_AND_HALF_DAY = {
  validator: (rule, value, callback) => {
    if (value) {
      let arr = value.split(' ');
      if (arr.length === 1) {
        if (value.indexOf('午') > -1) {
          callback(new Error('请继续选择日期'));
        } else {
          callback(new Error('请继续选择上午/下午'));
        }
        return;
      }
    }
    callback();
  }
};
