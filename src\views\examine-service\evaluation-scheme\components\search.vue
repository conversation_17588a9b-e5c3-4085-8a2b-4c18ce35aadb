<template>
  <el-form
    :model="form"
    size="small"
    ref="form"
    :inline="true"
    label-width="80px"
    label-suffix=":"
  >
    <el-form-item label="方案名称" prop="name">
      <el-input placeholder="请输入方案名称" v-model.trim="form.name" />
    </el-form-item>
    <el-form-item label="考核维度" prop="dimension">
      <el-select
        v-model="form.dimension"
        placeholder="请选择考核维度"
        clearable
        size="small"
        style="width: 100%"
      >
        <el-option
          v-for="dict in indexDict.type.assess_dimension"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        icon="el-icon-search"
        size="mini"
        @click="handleQuery"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >清空</el-button
      >
    </el-form-item>
  </el-form>
</template>

<script>
  import { cloneDeep } from 'lodash';

  export default {
    name: 'search',
    inject: ['indexDict'],
    data() {
      return {
        // 查询参数
        form: {
          name: undefined,
          dimension: undefined
        }
      };
    },
    methods: {
      // 重置
      resetQuery() {
        this.$refs['form'].resetFields();
        this.handleQuery();
      },
      // 查询
      handleQuery() {
        const param = cloneDeep(this.form);
        this.$emit('search', param);
      }
    }
  };
</script>
