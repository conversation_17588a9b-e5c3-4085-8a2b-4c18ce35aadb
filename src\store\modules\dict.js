import { getStore, setStore } from '@/util/store';

import { getDictionary, getDictByCodeFlow } from '@/api/system/dict';

const dict = {
  state: {
    flowRoutes: getStore({ name: 'flowRoutes' }) || {},
    dictMap: getStore({ name: 'dictMap' }) || []
  },
  actions: {
    GetDictByCode: ({ state, commit }, code) => {
      return new Promise((resolve, reject) => {
        if (state.dictMap[code]) {
          resolve(state.dictMap[code]);
          return;
        }
        getDictByCodeFlow(code)
          .then((res) => {
            let dict = res.data.data || [];
            commit('ADD_DICT', { code, dict });
            resolve(dict);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    FlowRoutes: ({ commit }) => {
      return new Promise((resolve, reject) => {
        getDictionary({ code: 'flow' })
          .then((res) => {
            commit('SET_FLOW_ROUTES', res.data.data);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    }
  },
  mutations: {
    ADD_DICT: (state, data) => {
      state.dictMap[data.code] = data.dict;
      setStore({ name: 'dictMap', content: state.dictMap, type: 'session' });
    },
    SET_FLOW_ROUTES: (state, data) => {
      state.flowRoutes = data.map((item) => {
        return {
          routeKey: `${item.code}_${item.dictKey}`,
          routeValue: item.remark
        };
      });
      setStore({ name: 'flowRoutes', content: state.flowRoutes });
    }
  }
};

export default dict;
