import { wsUrl } from '@/config/env';
import website from '@/config/website';
import ElementUI from 'element-ui';

let countReconnect = website.WS.maxReconnect;
function initWebSocket(e) {
  // console.log(wsUrl + e);
  const wsUri = wsUrl + e;
  this.socket = new WebSocket(wsUri); //这里面的this都指向vue
  this.socket.onerror = webSocketOnError;
  this.socket.onmessage = webSocketOnMessage;
  this.socket.onclose = closeWebsocket;
}
function webSocketOnError(e) {
  ElementUI.Notification({
    title: '',
    message: 'WebSocket连接发生错误' + e,
    type: 'error',
    duration: 0
  });
}
function webSocketOnMessage(e) {
  const data = JSON.parse(e.data);
  if (data.msgType === 'ERROR') {
    ElementUI.Notification({
      title: '',
      message: data.msg,
      type: 'error',
      duration: 0
    });
  } else if (data.type) {
    ElementUI.Notification({
      title: '通知',
      dangerouslyUseHTMLString: true,
      message: `<div class="notice_wrapper">
        <div class="notice_item">
          <div class="item_title">消息标题: </div>
          <div class="item_content">${data.title}</div>
        </div>
        <div class="notice_item">
          <div class="item_title">推送时间: </div>
          <div class="item_content">${data.scheduleTime}</div>
        </div>
        <div class="notice_item">
          <div class="item_title">消息内容: </div>
          <div class="item_content">${data.content}</div>
        </div>
      </div>`,
      position: 'bottom-right',
      customClass: 'msg_notification',
      duration: 10000
    });
  }
}
// 关闭websiocket
function closeWebsocket() {
  console.log('连接已关闭...');
  console.log('尝试重连');
  if (this.lockReconnect || countReconnect <= 0) return;
  setTimeout(() => {
    countReconnect--; // 不做限制 连不上一直重连
    this.initWebSocket();
  }, 5 * 1000);
}
function close() {
  this.socket.close(); // 关闭 websocket
  this.socket.onclose = function (e) {
    console.log(e); //监听关闭事件
    console.log('关闭');
  };
}
// function webSocketSend(agentData) {
//   this.socket.send(agentData);
// }
export default {
  initWebSocket,
  close
};
