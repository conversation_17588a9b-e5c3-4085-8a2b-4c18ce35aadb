<template>
  <div>
    <avue-form :option="option" v-model="form" />
  </div>
</template>

<script>
  export default {
    props: {
      form: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      form: {
        handler(val) {
          this.$emit('upDateFormData', val);
        },
        deep: true
      }
    },
    data() {
      return {
        option: {
          menuBtn: false,
          group: [
            {
              icon: '',
              label: '',
              prop: 'group1',
              column: [
                {
                  label: '项目名称',
                  prop: 'proName',
                  type: 'input',
                  span: 24,
                  rules: [
                    {
                      required: true,
                      message: '请输入项目名称',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '开始时间',
                  prop: 'startTime',
                  type: 'datetime',
                  valueFormat: 'yyyy-MM-dd HH:mm:ss',
                  rules: [
                    {
                      required: true,
                      message: '请选择开始时间',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '结束时间',
                  prop: 'endTime',
                  type: 'datetime',
                  valueFormat: 'yyyy-MM-dd HH:mm:ss',
                  rules: [
                    {
                      required: true,
                      message: '请选择结束时间',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '备注',
                  prop: 'remark',
                  max: 300,
                  type: 'textarea',
                  span: 24
                }
              ]
            }
          ]
        }
      };
    },
    methods: {}
  };
</script>
