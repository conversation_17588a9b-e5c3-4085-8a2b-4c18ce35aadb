<template>
  <el-dialog
    v-loading="changeLoading"
    @open="open"
    :title="title"
    :visible.sync="changeApplyLogVisible"
    width="30%"
    :before-close="handleClose"
    append-to-body
    custom-class="change-apply-log"
  >
    <div class="content">
      <header>{{ dialogHeader }}</header>
      <div class="box-cell">
        <div
          v-if="isAddPeople"
          @click="addPeople"
          class="selectPeople add-people"
        >
          <i class="el-icon-plus"></i>
        </div>
        <div
          v-for="(li, i) of peopleList"
          :key="li.userId"
          class="selectPeople userPeople"
        >
          <i @click="deletePeople(i)" class="iconfont icon-01"></i>
          <img v-if="li.avatar" :src="li.avatar" alt="" />
          <img v-else v-oss src="/launch/default-photo.png" />
          <p>{{ li.label }}</p>
        </div>
      </div>
    </div>
    <div v-if="isAddDesign" class="content">
      <header>加签方式：</header>
      <el-select v-model="formData.type" placeholder="请选择" size="small">
        <el-option
          v-for="item in applyList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
    <div v-if="isType" class="content">
      <header>审批方式：</header>
      <el-select
        v-model="formData.approverType"
        placeholder="请选择"
        size="small"
      >
        <el-option
          v-for="item in approverList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
    <div v-if="isReason" class="content">
      <header>原因(选填)：</header>
      <el-input
        v-model="formData.oprateReason"
        type="textarea"
        :maxlength="200"
        show-word-limit
        placeholder="请输入"
      >
      </el-input>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返 回</el-button>
      <el-button @click="submit" type="primary">确 定</el-button>
    </span>
    <select-people
      @rangeSave="rangeSave"
      :range-visible.sync="rangeVisible"
      :is-radio="isRadio"
      :exclude="exclude"
    ></select-people>
  </el-dialog>
</template>
<script>
  import selectPeople from '@/components/select-tree/select-people';
  import {
    commonDetail,
    transmit,
    maillAdd,
    signAdd
  } from '@/api/flow/process';
  export default {
    components: { selectPeople },
    props: {
      changeApplyLogVisible: {
        type: Boolean,
        default: false
      },
      applyLogStatus: {
        type: String,
        default: ''
      },
      dataObj: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        title: '',
        exclude: [],
        applyList: [
          { label: '向前加签', value: 5 },
          { label: '向后加签', value: 6 },
          { label: '并行加签', value: 7 }
        ],
        approverList: [
          { label: '会签', value: 1 },
          { label: '或签', value: 2 }
        ],
        rangeVisible: false,
        peopleList: [],
        isRadio: false,
        formData: {
          oprateReason: '',
          approverType: 1,
          type: ''
        },
        isApplyType: false,
        changeLoading: false
      };
    },
    computed: {
      dialogHeader() {
        return `${this.title}${
          this.applyLogStatus === 'transfer' ? '给' : ''
        }：`;
      },
      isAddPeople() {
        return !(this.peopleList.length && this.applyLogStatus === 'transfer');
      },
      isAddDesign() {
        return this.applyLogStatus === 'addSign';
      },
      isReason() {
        return ['transfer', 'addSign'].includes(this.applyLogStatus);
      },
      isType() {
        return (
          ([5, 6].includes(this.formData.type) && this.peopleList.length > 1) ||
          (this.formData.type === 7 && this.isApplyType)
        );
      }
    },
    methods: {
      open() {
        commonDetail(this.dataObj.processInstanceId).then((res) => {
          if (res && res.data && res.data.success) {
            res.data.data.forEach(({ status, users, nodeType }) => {
              if (status === 0) {
                this.isApplyType = users.length <= 1;
                this.exclude = users.map(({ userId }) => userId);
              }
              if (this.applyLogStatus === 'send' && nodeType === 3) {
                this.exclude = users.map(({ userId }) => userId);
              }
            });
          }
        });

        // transfer 转交 addSign 加签 send 抄送
        this.peopleList = [];

        const title = { transfer: '转交', addSign: '加签', send: '抄送' };
        this.title = title[this.applyLogStatus];

        this.isRadio = this.applyLogStatus === 'transfer';
      },
      deletePeople(i) {
        this.peopleList.splice(i, 1);
      },
      addPeople() {
        this.rangeVisible = true;
      },
      rangeSave(v) {
        v.forEach((item) => {
          if (this.peopleList.findIndex((a) => a.id === item.id) === -1) {
            this.peopleList.push(item);
          }
        });
      },
      submit() {
        let url = transmit;
        const userIds = this.peopleList.map((p) => p.id).join(',');
        const { flowableTaskId, processInstanceId, currentNodeId } =
          this.dataObj;
        let params = {};
        if (!userIds.length) {
          this.$message.error(`${this.title}人不能为空`);
          return;
        }
        // 转交
        if (this.applyLogStatus === 'transfer') {
          params = {
            currentNodeId,
            userId: userIds,
            flowableTaskId,
            oprateReason: this.formData.oprateReason,
            flowProcessInstanceId: this.dataObj.flowProcessInstanceId
          };
        }
        // 加签
        if (this.applyLogStatus === 'addSign') {
          url = signAdd;
          params = {
            userIds,
            processInstanceId,
            flowableTaskId,
            currentNodeId,
            ...this.formData
          };
          if (!this.formData.type) {
            this.$message.error('请选择加签方式');
            return;
          }
          if (this.isType && !this.formData.approverType) {
            this.$message.error('请选择审批方式');
            return;
          }
        }
        // 抄送
        if (this.applyLogStatus === 'send') {
          url = maillAdd;
          params = {
            userIds,
            flowableTaskId,
            processInstanceId,
            currentNodeId
          };
        }
        this.changeLoading = true;
        url(params)
          .then((res) => {
            if (res && res.data && res.data.success) {
              this.$message.success('操作成功');
              if (this.applyLogStatus === 'transfer') {
                this.$router.push({ name: 'approval' });
                return;
              }
              this.$emit('status-success');
              this.handleClose();
            }
          })
          .finally(() => {
            this.changeLoading = false;
          });
      },
      handleClose() {
        this.formData = {
          oprateReason: '',
          approverType: 1,
          type: ''
        };
        this.$emit('update:changeApplyLogVisible', false);
      }
    }
  };
</script>
<style scoped lang="scss">
  .change-apply-log {
    .content {
      display: flex;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      &:first-child {
        margin-bottom: 8px;
      }

      .add-people {
        margin-left: 7px;
      }

      .box-cell {
        display: flex;
        flex-wrap: wrap;
      }

      header {
        width: 81px;
        line-height: 32px;
        text-align: right;
      }

      & > div:not(.selectPeople) {
        flex: 1;
      }

      /deep/ textarea {
        height: 132px;
      }

      .selectPeople {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        margin-right: 20px;
        margin-bottom: 16px;
        border: 1px dashed #999;
        border-radius: 50%;
        cursor: pointer;

        &:hover {
          color: #409eff;
          border-color: #409eff;
        }
      }

      .userPeople {
        position: relative;
        display: flex;
        flex-direction: column;
        width: auto;
        height: auto;
        border: 0;
        cursor: inherit;

        &:hover {
          color: #333;
        }

        i {
          cursor: pointer;
        }

        img {
          width: 30px;
          height: 30px;
          border-radius: 50%;
        }

        p {
          width: 40px;
          margin-top: 6px;
          margin-bottom: 0;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .iconfont {
          position: absolute;
          top: -3px;
          right: -8px;
          color: #f24d4d;
        }
      }
    }
  }
</style>
