// 生产CND
const pro_cdn = {
  script: [
    '/cdn/vue/2.6.10/vue.min.js',
    '/cdn/vuex/3.1.1/vuex.min.js',
    '/cdn/axios/1.0.0/axios.min.js',
    '/cdn/element-ui/2.15.6/index.js',
    '/cdn/avue/2.9.5/avue.min.js',
    '/cdn/vue-router/3.0.1/vue-router.min.js'
  ]
};

const icon =
  process.env.NODE_ENV === 'production'
    ? [process.env.VUE_APP_PUBLIC_PATH + 'favicon.ico']
    : [];

// 标题
const title = '西北矿业绩效考核系统';
const publicPath = process.env.VUE_APP_PUBLIC_PATH;
const app_env = process.env.VUE_APP_ENV;

module.exports = {
  //路径前缀
  publicPath: publicPath,
  lintOnSave: true,
  productionSourceMap: false,
  configureWebpack: {
    name: title
  },
  chainWebpack: (config) => {
    if (app_env !== 'development') {
      config.plugin('html').tap((args) => {
        args[0].cdn = pro_cdn;
        args[0].icon = icon;
        return args;
      });
      //忽略的打包文件
      config.externals({
        vue: 'Vue',
        'vue-router': 'VueRouter',
        vuex: 'Vuex',
        axios: 'axios',
        'element-ui': 'ELEMENT',
        '@smallwei/avue': 'AVUE'
      });
    }
    const entry = config.entry('app');
    entry.add('babel-polyfill').end();
    entry.add('classlist-polyfill').end();
    entry.add('@/mock').end();
  },
  css: {
    extract: { ignoreOrder: true }
  },
  //开发模式反向代理配置，生产模式请使用Nginx部署并配置反向代理
  devServer: {
    port: 1888,
    open: true,
    disableHostCheck: true,
    proxy: {
      '/api': {
        //本地服务接口地址
        // target: 'http://szyk-single.rdframework.************.nip.io:32393',
        // target: 'http://*************:8088', // 王斌ip环境
        // target: 'http://***************:30349', // 临时测试环境
        // target: 'http://*************:30953/api', // 测试环境
        target: 'http://***********/api', // 开发环境
        // target: 'http://**************:8080', // 孝义环境
        // target: 'http://**************:8080', // 太新环境
        // target: 'http://*************:8080/', // 王明华
        // target: 'http://localhost',
        //远程演示服务地址,可用于直接启动项目
        //target: 'https://saber.szyk.vip/api',
        ws: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
};
