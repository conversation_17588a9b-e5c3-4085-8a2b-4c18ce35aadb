import Vue from 'vue';
import Avue from '@smallwei/avue';
import axios from './router/axios';
import VueAxios from 'vue-axios';
import App from './App';
import router from './router/router';
import './permission'; // 权限
import './error'; // 日志
import './cache'; //页面缓存
import store from './store';
import { loadStyle } from './util/util';
import * as urls from '@/config/env';
import Element from 'element-ui';
import { iconfontUrl, iconfontVersion } from '@/config/env';
import i18n from './lang'; // Internationalization
import './styles/common.scss';
import basicBlock from './components/basic-block/main';
import basicContainer from './components/basic-container/main';
import thirdRegister from './components/third-register/main';
import Harley from '@/components/harley-ui';
import avueUeditor from 'avue-plugin-ueditor';
import website from '@/config/website';
import crudCommon from '@/mixins/crud';
// 业务组件
import tenantPackage from './views/system/tenantpackage';
import directive from './directive'; // directive
import websocket from './util/websocket.js';

import pack from '../package.json';
import { jsonp } from 'vue-jsonp';
import { MessageBox } from 'element-ui';

import './filter';

// 高德地图依赖包
import VueAMap from 'vue-amap';
Vue.use(VueAMap);

console.log(
  '%c%s',
  'color: #FF7426; font-size: 50px;font-weight:bolder',
  '平台版本:' + `${pack.version}.${pack.updateDate}`
);
if (process.env.VUE_APP_ENV !== 'development') {
  // 定时获取远程版本
  let timer = setInterval(() => {
    const url = process.env.VUE_APP_PUBLIC_PATH + 'version.js';
    jsonp(url, { ts: new Date().getTime(), callbackName: 'callback' })
      .then((json) => {
        console.log(
          ` 远程版本：${json.version} \n 本地版本：${pack.version}.${pack.updateDate} `
        );
        if (json.version !== `${pack.version}.${pack.updateDate}`) {
          clearInterval(timer);
          MessageBox.alert('发现系统更新,请重新登录', '提示', {
            confirmButtonText: '重新登录',
            showClose: false,
            callback: () => {
              window.location.reload();
            }
          });
        }
      })
      .catch((err) => {
        console.log('实时版本检测失败', err);
      });
  }, 30000);
}

function RepairProps(cmp) {
  (cmp.mixins || []).forEach((mixin) => {
    if (mixin.props && mixin.props.placement) {
      const defaultValue = mixin.props.placement.default;
      mixin.data = new Proxy(mixin.data, {
        apply(target, thisArg, argArray) {
          const res = Reflect.apply(target, thisArg, argArray);
          return {
            ...(res || {}),
            placement: defaultValue
          };
        }
      });
      delete mixin.props.placement;
    }
    if (mixin.mixins && mixin.mixins.length > 0) {
      RepairProps(mixin);
    }
  });
}

RepairProps(Element.DatePicker);
RepairProps(Element.TimePicker);
RepairProps(Element.TimeSelect);

// 注册全局crud驱动
window.$crudCommon = crudCommon;
// 加载Vue拓展
Vue.use(router);
Vue.use(Harley);
Vue.use(VueAxios, axios);
Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value)
});
if (process.env.VUE_APP_ENV !== 'development') {
  Vue.use(window.AVUE, {
    size: 'small',
    tableSize: 'small',
    calcHeight: 65,
    i18n: (key, value) => i18n.t(key, value)
  });
} else {
  Vue.use(Avue, {
    axios,
    size: 'small',
    tableSize: 'small',
    calcHeight: 65,
    i18n: (key, value) => i18n.t(key, value)
  });
}

// 注册全局容器
Vue.component('basicContainer', basicContainer);
Vue.component('basicBlock', basicBlock);
Vue.component('thirdRegister', thirdRegister);
Vue.component('avueUeditor', avueUeditor);
Vue.component('tenantPackage', tenantPackage);

// 通用组件
import YkPagination from '@/components/yk-pagination';
Vue.component('YkPagination', YkPagination);
import YkRightTool from '@/components/yk-right-tool';
Vue.component('YkRightTool', YkRightTool);
import YkImportExcel from '@/components/yk-import-excel';
Vue.component('YkImportExcel', YkImportExcel);
import YkLocalModel from '@/components/yk-local-model';
Vue.component('YkLocalModel', YkLocalModel);

// 字典数据组件
import systemDict from '@/components/dict-data/system-dict';
import serviceDict from '@/components/dict-data/service-dict';
systemDict.install();
serviceDict.install();

Vue.use(directive);

// 加载相关url地址
Object.keys(urls).forEach((key) => {
  Vue.prototype[key] = urls[key];
});
// 加载website
Vue.prototype.website = website;
Vue.prototype.$websocket = websocket;
// 动态加载阿里云字体库
iconfontVersion.forEach((ele) => {
  loadStyle(iconfontUrl.replace('$key', ele));
});

Vue.config.productionTip = false;

new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App)
}).$mount('#app');
