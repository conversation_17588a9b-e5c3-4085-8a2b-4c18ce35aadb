<template>
  <el-dialog
    append-to-body
    fullscreen
    :modal="false"
    :visible="open"
    @close="close"
  >
    <div slot="title" style="height: 35px">
      <strong style="font-size: 18px">{{ title }}</strong>
      <span class="font-btn">
        <el-button
          size="mini"
          icon="el-icon-plus"
          circle
          style="margin-right: 5px"
          :disabled="addBtn"
          @click="fontAdd"
        ></el-button>
        <strong class="strong-font"> 字号：{{ font || 0 }} </strong>
        <el-button
          size="mini"
          icon="el-icon-minus"
          circle
          :disabled="reduceBtn"
          @click="fontReduce"
        ></el-button>
      </span>
    </div>
    <slot></slot>
  </el-dialog>
</template>

<script>
  import { mapState, mapMutations } from 'vuex';
  export default {
    name: 'full-screen-table',
    props: {
      title: {
        type: String,
        default: '评价信息'
      },
      open: {
        type: Boolean,
        default: false
      }
    },
    model: {
      prop: 'open',
      event: 'change'
    },
    data() {
      return {
        addBtn: false,
        reduceBtn: false
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      })
    },
    methods: {
      ...mapMutations(['SET_ADD_FONT', 'SET_REDUCE_FONT']),
      close() {
        this.$emit('change', false);
      },
      fontAdd() {
        if (this.font < 20) {
          this.reduceBtn = false;
          this.SET_ADD_FONT();
        } else {
          this.addBtn = true;
        }
      },
      fontReduce() {
        if (this.font < 14) {
          this.reduceBtn = true;
        } else {
          this.addBtn = false;
          this.SET_REDUCE_FONT();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .font-btn {
    float: right;
    margin-right: 40px;
    font-size: 18px;
  }

  .strong-font {
    margin: 0 10px;
    font-size: 14px;
  }
</style>
