<template>
  <div class="edit-apply-log">
    <el-timeline>
      <el-timeline-item
        v-for="({ type, users, personType, scopesType }, index) in activities"
        :key="index"
      >
        <span class="node-name">{{ type === 2 ? '抄送人' : '审批人' }}</span>
        <div
          v-if="users.length > 3"
          @click="clickAll(users, type, index)"
          class="all"
        >
          <img v-oss src="/launch/viewAll.png" alt="" />
          <span class="name">查看全部</span>
        </div>
        <i v-if="users.length > 3" class="el-icon-plus"></i>

        <template v-for="node of users.slice(0, 3)">
          <div
            :key="`${node.userId}-0`"
            :title="node.username"
            class="people-list"
          >
            <i
              v-if="type === 3 || (type === 2 && node.custom)"
              @click="deleteImg(users, node.userId, index)"
              class="el-icon-error"
            ></i>
            <img v-if="node.avatar" :src="node.avatar" />
            <img v-else v-oss src="/launch/default-photo.png" />
            <span class="name">{{ node.username }}</span>
          </div>
          <i :key="`${node.userId}-1`" class="el-icon-plus"></i>
        </template>
        <div
          v-if="type === 3"
          @click="addPeople(index, personType, scopesType)"
          class="add-people"
        >
          <span v-if="type !== 2 && !users.length">*</span>
          <i class="el-icon-plus"></i>
        </div>
      </el-timeline-item>
    </el-timeline>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      append-to-body
      width="525px"
      class="select-people-all"
    >
      <div v-for="active of userAll" :key="active.userId" class="people-img">
        <div :title="active.username">
          <img v-if="active.avatar" :src="active.avatar" />
          <img v-else v-oss src="/launch/default-photo.png" />
          <span
            v-if="type === 3 || (type === 2 && active.custom)"
            @click="deleteImg(userAll, active.userId, index)"
            class="iconfont icon-01"
          ></span>
          <p>{{ active.username }}</p>
        </div>
      </div>
    </el-dialog>
    <select-people
      @rangeSave="peopleSave"
      :id="id"
      :node-id="nodeId"
      :range-visible.sync="visible"
      :max-select-length="maxSelectLength"
      :is-radio="false"
      :is-job="scopesType === 1"
    ></select-people>
  </div>
</template>
<script>
  import { deepClone } from '@/util/util.js';
  import selectPeople from '@/components/select-tree/select-people';
  export default {
    components: { selectPeople },
    props: {
      applyLogList: {
        type: Array,
        default: () => {
          return [];
        }
      },
      id: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        activities: [],
        type: undefined,
        dialogVisible: false,
        userAll: [],
        index: '',
        visible: false,
        nodeId: '',
        maxSelectLength: 50,
        scopesType: 2
      };
    },
    computed: {
      title() {
        return this.type === 2 ? '抄送人' : '审批人';
      }
    },
    watch: {
      applyLogList: {
        handler() {
          this.getHistory();
        },
        immediate: true
      }
    },
    methods: {
      clickAll(node, type, index) {
        this.dialogVisible = true;
        this.userAll = node;
        this.type = type;
        this.index = index;
      },
      addPeople(i, personType, scopesType) {
        this.maxSelectLength = personType === 1 ? 1 : 50;
        this.scopesType = scopesType;
        this.index = i;
        this.nodeId = this.applyLogList[i].nodeId;
        this.visible = true;
      },
      peopleSave(v) {
        const peopleArr = v.map((l) => {
          return {
            username: l.label,
            avatar: l.avatar,
            userId: l.id,
            custom: true
          };
        });
        peopleArr.forEach((pl) => {
          const dup = this.activities[this.index].users.filter(
            (l) => l.userId === pl.userId
          );
          if (!dup.length) {
            this.activities[this.index].users.push(pl);
          }
        });
        this.$emit('change', this.activities);
      },
      deleteImg(node, userId) {
        let i = node.findIndex((item) => item.userId === userId);
        if (i > -1) {
          node.splice(i, 1);
          this.activities[this.index].users = node;
          this.$emit('change', this.activities);
        }
      },
      // 选人组件非空校验
      validate() {
        const isUser = this.activities.every(
          (t) => t.type === 2 || t.users.length
        );
        if (!isUser) {
          this.$message.error('请选择审批人');
        }
        return isUser;
      },
      getHistory() {
        const list = deepClone(this.applyLogList);
        this.activities = list.map((h) => {
          return {
            type: h.type,
            users: h.users || [],
            nodeId: h.nodeId,
            personType: h.personType || 2,
            scopesType: h.scopesType || 1
          };
        });
      }
    }
  };
</script>
<style lang="scss">
  .select-people-all {
    .el-dialog__body {
      display: flex;
      flex-wrap: wrap;
      min-height: 144px;
      max-height: 212px;
      padding: 22px 18px !important;
      overflow-y: scroll;
    }

    .icon-01 {
      top: -2px !important;
    }

    .people-img {
      display: flex;
      flex-wrap: wrap;
      min-width: 40px;
      margin: 12px 15px;

      p {
        width: 50px;
        margin: 0;
        overflow: hidden;
        font-size: 12px;
        line-height: 12px;
        white-space: nowrap;
        text-align: center;
        text-overflow: ellipsis;
      }

      .add-people-img {
        margin-left: 20px;
        color: #333;
      }

      div {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 30px;
          height: 30px;
          margin-bottom: 6px;
          border-radius: 50%;
        }
      }

      .icon-01 {
        position: absolute;
        top: -2px;
        right: calc(50% - 17px);
        color: #ff5151;
        font-size: 12px;
        line-height: initial;
        cursor: pointer;
      }
    }
  }

  .edit-apply-log {
    .el-timeline {
      box-sizing: border-box;
      width: 100%;
    }

    .people-list {
      position: relative;

      .el-icon-error {
        position: absolute;
        right: 5px;
        color: #ff5151;
        cursor: pointer;
      }

      img {
        border-radius: 100%;
      }
    }

    .all {
      cursor: pointer;
    }

    .el-timeline-item {
      padding-bottom: 37px;

      .el-timeline-item__wrapper {
        margin-left: 32px;
        padding-left: 0;
        border-bottom: 1px solid #e9e9e9;

        .el-timeline-item__timestamp.is-bottom {
          margin-top: 15px;
        }
      }

      &:last-child {
        .el-timeline-item__wrapper {
          border-bottom: unset;
        }
      }
    }

    .el-timeline-item__content {
      display: flex;
      justify-content: space-between;

      & > .el-icon-plus {
        margin: 0 7px;
      }

      .node-name {
        flex: 1;
      }

      & > i:last-child {
        display: none;
      }

      div {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 57px;
        margin-top: -8px;

        img {
          width: 30px;
          height: 30px;
        }

        .name {
          width: 100%;
          margin-top: 6px;
          overflow: hidden;
          font-size: 12px;
          white-space: nowrap;
          text-align: center;
          text-overflow: ellipsis;
        }
      }
    }

    .add-people {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 29px !important;
      height: 29px;
      margin: 0 13px 23px;
      color: #999;
      border: 1px dashed #999;
      border-radius: 50%;
      cursor: pointer;

      span {
        position: absolute;
        top: -3px;
        right: 0;
        width: 10px;
        height: 10px;
        color: #ff5151;
        text-align: center;
        background: #fff;
      }
    }
  }
</style>
