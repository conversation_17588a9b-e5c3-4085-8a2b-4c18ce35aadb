<template>
  <el-table
    :data="source"
    size="small"
    border
    style="width: 100%; margin-top: 20px"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" />
    <el-table-column type="index" label="序号" align="center" width="60">
    </el-table-column>
    <el-table-column prop="type" label="周期类型" align="center">
      <template slot-scope="scope">
        {{ scope.row.type | filterType }}
      </template>
    </el-table-column>
    <el-table-column prop="yearly" label="年" align="center" width="120px">
    </el-table-column>
    <el-table-column prop="startTime" label="开始时间" align="center">
    </el-table-column>
    <el-table-column prop="endTime" label="结束时间" align="center">
    </el-table-column>
    <el-table-column width="180" prop="action" align="center" label="操作">
      <template slot-scope="scope">
        <el-button
          type="text"
          icon="el-icon-view"
          size="mini"
          @click="() => dispatch('detail', scope.row)"
          >查看</el-button
        >
        <el-button
          type="text"
          icon="el-icon-edit"
          size="mini"
          @click="() => dispatch('edit', scope.row)"
          >编辑</el-button
        >
        <el-popconfirm
          style="margin-left: 8px"
          title="确定 删除 此数据吗？"
          @confirm="() => dispatch('delete', scope.row)"
        >
          <el-button
            type="text"
            icon="el-icon-delete"
            size="mini"
            slot="reference"
            style="color: red"
            >删除
          </el-button>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'table-list',
    props: {
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    filters: {
      filterType(type) {
        switch (type) {
          case 1:
            return '月度';
          case 2:
            return '季度';
          case 3:
            return '半年度';
          case 4:
            return '年度';
          default:
            return '--';
        }
      }
    },
    methods: {
      dispatch(type, row) {
        this.$emit('dispatch', type, row);
      },
      // 多选
      handleSelectionChange(val) {
        this.$emit('choice', val);
      }
    }
  };
</script>
