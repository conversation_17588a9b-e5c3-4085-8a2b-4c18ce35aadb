import request from '@/router/axios';

// 分页查询
export function getDetail(params) {
  return request({
    url: '/api/examine/evaluation-form/work-detail',
    method: 'get',
    params
  });
}

// 分页查询
export function postSubmit(data) {
  return request({
    url: '/api/examine/evaluation-form/work-submit',
    method: 'post',
    data
  });
}

// 获取工作效能最高分
export function getMaxScore(params) {
  return request({
    url: '/api/szyk-system/param/getByParamKey',
    method: 'get',
    params
  });
}
