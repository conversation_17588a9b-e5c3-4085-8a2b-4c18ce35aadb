<template>
  <div>
    <!-- <el-alert :title="title" type="warning" show-icon :closable="false" /> -->
    <el-table
      style="width: 100%; margin-top: 10px"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
    >
      <el-table-column
        fixed
        type="index"
        label="序号"
        width="50"
        align="center"
      >
      </el-table-column>
      <el-table-column fixed label="部门(中心)" prop="deptName" align="center">
      </el-table-column>
      <el-table-column label="姓名" prop="userName" align="center">
      </el-table-column>
      <el-table-column label="岗位" prop="post" align="center">
      </el-table-column>
      <el-table-column label="工号" prop="employeeNumber" align="center">
      </el-table-column>
      <el-table-column label="基本分" prop="basicScore" align="center">
        <template slot-scope="{ row }">
          {{ row.basicScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="系统计算得分"
        prop="systemCalculateScore"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.systemCalculateScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column
        label="岗位绩效考核得分"
        prop="assessScore"
        align="center"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.assessScore"
            size="small"
            controls-position="right"
            style="width: 100%"
            :min="0"
            :precision="2"
            :step="0.01"
            @change="countFunction"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column
        label="备注(点击输入)"
        width="160"
        align="center"
        prop="comment"
      >
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput(scope.row)"
          >
            {{
              scope.row.comment && scope.row.comment.length
                ? scope.row.comment
                : '点击输入内容'
            }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center">
        <el-button
          type="primary"
          icon="el-icon-plus"
          circle
          size="small"
        ></el-button>
        <el-button
          type="danger"
          icon="el-icon-minus"
          circle
          size="small"
        ></el-button>
      </el-table-column> -->
    </el-table>
    <!--  同步文本输入  -->
    <el-dialog
      width="600px"
      title="备注"
      append-to-body
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        placeholder="请输入备注"
        maxlength="50"
        show-word-limit
        :rows="8"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
        <el-button size="small" type="primary" @click="inputSave"
          >完 成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { mapState } from 'vuex';
  export default {
    name: 'post-list',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      list: {
        handler(newVal) {
          this.tempList = cloneDeep(newVal);
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        tempList: [],
        count: 0,
        // 弹出框
        inputVisited: false,
        temp: null,
        tempInput: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      },
      title() {
        return `共 ${this.tempList.length} 个被评价机构 ，已评价 ${this.count} 个`;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      // 弹窗打开
      handleInput(row) {
        this.inputVisited = true;
        this.temp = row;
        this.tempInput = row.comment;
      },
      // 弹窗关闭
      inputClose() {
        this.inputVisited = false;
        this.temp = {};
      },
      // 保存
      inputSave() {
        this.tempList.map((item) => {
          if (item.id === this.temp.id) {
            item.comment = this.tempInput;
          }
          return item;
        });
        this.$emit('sync', this.tempList);
        this.inputVisited = false;
      },
      // 统计评价状态
      countFunction() {
        // let count = 0;
        // this.tempList.forEach((item) => {
        //   if (typeof item.assessScore === 'number') {
        //     count += 1;
        //   }
        // });
        // this.count = count;
        this.$emit('sync', this.tempList);
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }
  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
  }
</style>
