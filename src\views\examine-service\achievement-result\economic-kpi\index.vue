<template>
  <basic-container>
    <transition-group name="fade">
      <div v-show="!open" :key="1">
        <search @search="searchQuery" />
        <div style="margin-bottom: 10px">
          <el-button
            type="warning"
            size="small"
            icon="el-icon-download"
            @click="handleExport"
            :disabled="disabled"
            >导 出</el-button
          >
        </div>
        <table-list
          v-loading="loading"
          :tableData="tableData"
          @dispatch="handleEvent"
        />
        <yk-pagination
          small
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.current"
          :limit.sync="queryParams.size"
          @pagination="getList"
        />
      </div>
      <yk-local-model :key="2" :title="title" :open="open" @close="close">
        <exam
          :is-detail="isDetail"
          :is-approval="isApproval"
          :code="code"
          @close="close"
          @refresh="refresh"
        />
      </yk-local-model>
    </transition-group>
  </basic-container>
</template>

<script>
  import { getPageList } from '@/api/examine/economic-kpi-result';
  import { Search, TableList } from './components';
  import { downloadUrl, downloadFile } from '@/util/download';
  import Exam from './exam.vue';
  export default {
    name: 'economicKpi',
    components: {
      Search,
      TableList,
      Exam
    },
    data() {
      return {
        tableData: [],
        open: false,
        queryParams: {
          current: 1,
          size: 10
        },
        total: 0,
        loading: false,
        title: '',
        isDetail: false,
        isApproval: false,
        code: '',
        selections: [],
        disabled: true
      };
    },
    watch: {
      selections: {
        handler(val) {
          if (val.length > 0) {
            this.disabled = false;
          } else {
            this.disabled = true;
          }
        },
        deep: true
      }
    },
    methods: {
      // 查询
      searchQuery(params) {
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          params
        );
        this.getList();
      },
      // 请求列表数据
      async getList() {
        try {
          this.loading = true;
          const res = await getPageList(this.queryParams);
          const { total, records } = res.data.data;
          this.total = total || 0;
          this.tableData = records;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 导出
      handleExport() {
        const url = downloadUrl('economy_download', '', {
          codes: this.selections.toString()
        });
        downloadFile(url);
      },
      // 监听表格事件
      handleEvent(type, { code, selections }) {
        if (code) {
          this.open = true;
          this.code = code;
        }
        switch (type) {
          case 'detail':
            this.title = '查看';
            this.isDetail = true;
            break;
          case 'exam':
            this.title = '审核';
            this.isDetail = true;
            this.isApproval = true;
            break;
          case 'edit':
            this.title = '编辑';
            this.isDetail = false;
            break;
          case 'selection':
            this.selections = selections;
            break;
          default:
            break;
        }
      },
      // model关闭
      close() {
        this.open = false;
        this.code = '';
        this.isApproval = false;
      },
      // 刷新
      refresh() {
        this.close();
        this.getList();
      }
    }
  };
</script>
