<template>
  <el-dialog
    title="评论"
    :visible.sync="visible"
    width="586px"
    :before-close="handleClose"
    append-to-body
    custom-class="apply-confirm"
  >
    <div class="apply-confirm-context-box">
      <el-input
        v-model="formData.description"
        type="textarea"
        :maxlength="100"
        show-word-limit
        placeholder="请输入评论内容"
      >
      </el-input>
      <div class="upload-file-box" style="padding-top: 12px">
        <file-comp
          v-model="formData.hValue"
          :max-length="5"
          btnsize="small"
          :accept="'.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .pdf, .rar, .zip'"
        >
          <template slot="button">
            <span>选择附件</span>
          </template>
          <!-- <span slot="trigger" class="fileTip">
            附件支持word、pdf、图片、zip、rar，单个文件不能超过20MB
          </span> -->
        </file-comp>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返 回</el-button>
      <el-button @click="submit" type="primary">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
  import fileComp from '@/components/form-build-new/items/file-comp';

  export default {
    name: 'CommentDialog',
    components: {
      fileComp
    },
    props: {
      visible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        formData: {
          description: '',
          hValue: []
        }
      };
    },
    watch: {
      visible() {
        this.formData.description = '';
        this.formData.hValue = [];
      }
    },
    methods: {
      updateFileList(arr) {
        this.formData.hValue = arr;
      },
      submit() {
        if (!this.formData.description) {
          this.$message.error('请输入评论内容！');
          return;
        }
        const file = this.formData.hValue.length ? this.formData.hValue : [];
        this.$emit('submit', { ...this.formData, file });
      },
      handleClose() {
        this.$emit('update:visible', false);
      }
    }
  };
</script>
