<template>
  <div>
    <el-col :span="12">
      <el-form-item :label="`${data.name}(元)：`">
        <common-comp :value="value.value" />
      </el-form-item>
    </el-col>
    <el-col v-if="data.capital" :span="12">
      <el-form-item label="大写：">
        <common-comp :value="value.text" />
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
  import CommonComp from './common-comp';
  export default {
    name: 'InputMoneyComp',
    components: { CommonComp },
    props: {
      value: {
        type: Object,
        default() {
          return {};
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      }
    }
  };
</script>
