<template>
  <basic-container>
    <h-block title="固定业务demo" class="approval">
      <template v-if="!$store.getters.isVisitor">
        <h-operation-bar :button-list="buttonList">
          <h-table
            ref="table"
            v-loading="loading"
            @size-change="sizeChange"
            @pagination-current-change="currentChange"
            :show-overflow-tooltip="true"
            :data="data"
            :columns="tableColumns"
            reserve-selection
            row-key="id"
            show-pagination
            :page-size="size"
            :current-page="current"
            :total="total"
            :all-selectable="false"
          >
            <template slot="left" slot-scope="{ row }">
              <span @click="goApply(row)" class="formName">
                {{ row.proName }}
              </span>
            </template>
            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="{ row }">
                <el-button @click="goApply(row)" type="text" size="small"
                  >查看</el-button
                >
              </template>
            </el-table-column>
          </h-table>
        </h-operation-bar>
      </template>
      <create-organization-page v-else></create-organization-page>
    </h-block>
  </basic-container>
</template>
<script>
  import { fixedFormList } from '@/api/flow/process';
  import createOrganizationPage from './approval/create-organization-page.vue';

  import './approval/index.scss';

  const TABLE_COLUMNS = [
    {
      label: '项目名称',
      value: 'proName',
      slotName: 'left',
      showOverflowTooltip: true
    },
    { label: '开始时间', value: 'startTime' },
    { label: '结束时间', value: 'endTime' },
    {
      label: '备注',
      value: 'remark',
      showOverflowTooltip: true
    }
  ];

  export default {
    components: { createOrganizationPage },
    data() {
      return {
        buttonList: [
          {
            caption: '发起',
            icon: 'el-icon-plus',
            callback: this.goNewApproval,
            type: 'primary'
          }
        ],
        data: [],
        tableColumns: TABLE_COLUMNS,
        size: 10,
        current: 1,
        total: 0,
        loading: false
      };
    },
    mounted() {
      if (!this.$store.getters.isVisitor) {
        this.search();
      }
    },
    methods: {
      goApply({ instanceId }) {
        this.$router.push({
          name: 'fixedFormDetail',
          query: {
            id: instanceId
          }
        });
      },
      onSearch() {
        this.current = 1;
        this.search();
      },
      search() {
        const params = {
          current: this.current,
          size: this.size
        };
        this.loading = true;
        fixedFormList(params)
          .then((res) => {
            if (res && res.data && res.data.data && res.data.data.records) {
              this.data = res.data.data.records;
              this.total = res.data.data.total;
              if (this.current > 1 && !this.data.length) {
                this.current = parseInt(this.total / this.size, 10);
                this.search();
              }
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      goNewApproval() {
        this.$router.push({ name: 'fixedNewApproval' });
      },
      currentChange(currentPage) {
        this.current = currentPage;
        this.search();
      },
      sizeChange(size) {
        this.size = size;
        this.search();
      }
    }
  };
</script>
