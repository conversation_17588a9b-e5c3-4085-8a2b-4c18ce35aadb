<template>
  <el-form label-suffix=": " label-width="100px" size="small">
    <el-row :gutter="15">
      <el-col :span="24" v-if="website.tenantMode">
        <el-form-item label="所属租户">
          {{ tenantName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="机构名称" prop="deptName">
          {{ formData.deptName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="机构全称" prop="fullName">
          {{ formData.fullName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="上级机构" prop="parentName">
          {{ formData.parentName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="机构类型" prop="deptCategoryName">
          {{ formData.deptCategoryName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          {{ formData.sort || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="备注" prop="remark">
          {{ formData.remark || '--' }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
  import { findByvalue } from '@/util/util';

  export default {
    name: 'dept-detail',
    props: {
      formData: {
        type: Object,
        default() {
          return {};
        }
      },
      tenantData: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    computed: {
      tenantName() {
        return findByvalue(this.tenantData, this.formData.tenantId);
      }
    }
  };
</script>
<style lang="scss"></style>
