import request from '@/router/axios';

// 分页查询
export function getPageList(params) {
  return request({
    url: '/api/examine/evaluation-form/page',
    method: 'get',
    params
  });
}

// 部门建设评价单详情
export function getDetail(params) {
  return request({
    url: '/api/examine/evaluation-form/dept-build-detail',
    method: 'get',
    params
  });
}

// 保存-提交部门建设评价
export function postSubmit(data) {
  return request({
    url: '/api/examine/evaluation-form/dept-build-submit',
    method: 'post',
    data
  });
}
