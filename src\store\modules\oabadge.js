import { cornerMark } from '@/api/flow/process';

const dict = {
  state: {
    oabadge: {}
  },
  actions: {
    cornerMark({ commit }) {
      cornerMark().then((res) => {
        if (res && res.data && res.data.success) {
          commit('SET_BADGE', res.data.data);
        }
      });
    }
  },
  mutations: {
    SET_BADGE: (state, data) => {
      state.oabadge = data;
    }
  }
};

export default dict;
