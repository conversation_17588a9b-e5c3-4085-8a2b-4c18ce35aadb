<template>
  <div class="wrapper">
    <div class="nav_header">
      <h3>基本信息</h3>
    </div>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-suffix="："
      size="small"
    >
      <el-row :gutter="15">
        <el-col :span="6">
          <el-form-item label="期间类型" prop="type" style="margin-bottom: 0">
            <el-select
              v-model="form.type"
              placeholder="请选类型"
              style="width: 100%"
              :clearable="true"
              @change="handleChange"
              :disabled="isEdit"
            >
              <el-option
                v-for="dict in serviceDicts.type.period_type"
                :key="dict.value"
                :label="dict.label"
                :value="+dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年" prop="yearly" style="margin-bottom: 0">
            <el-date-picker
              v-model="form.yearly"
              type="year"
              value-format="yyyy"
              style="width: 100%"
              :editable="false"
              placeholder="选择年"
              :disabled="isEdit"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="开始时间"
            prop="startTime"
            style="margin-bottom: 0"
          >
            <el-date-picker
              v-model="form.startTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width: 100%"
              :editable="false"
              @change="handleChange"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结束时间" style="margin-bottom: 0">
            {{ form.endTime || '--' }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="nav_header">
      <h3>周期明细</h3>
    </div>
    <el-table border :data="source" size="small" style="width: 100%">
      <el-table-column type="index" label="序号" align="center" width="60" />
      <el-table-column prop="itemName" label="期间名称" align="center" />
      <el-table-column prop="startTime" label="开始时间" align="center" />
      <el-table-column prop="endTime" label="结束时间" align="center" />
      <el-table-column prop="deadline" label="考评截止日期" align="center">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.deadline"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            style="width: 100%"
            :editable="false"
            @change="handleDeadlin(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <div class="footer_button">
      <el-button size="small" @click="back">返回</el-button>
      <el-button type="primary" size="small" @click="submit">保存</el-button>
    </div>
  </div>
</template>
<script>
  import {
    getDetail,
    postExamineDetail,
    postAdd,
    postEdit,
    postEndTime
  } from '@/api/examine/cycle';

  export default {
    name: 'addEdit',
    serviceDicts: ['period_type'],
    props: {
      id: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        form: {
          type: undefined, // 类型
          yearly: undefined, // 年度
          startTime: undefined, // 开始时间
          endTime: undefined // 结束时间
        },
        source: [],
        rules: {
          type: [{ required: true, message: '请选择类型', trigger: 'change' }],
          yearly: [{ required: true, message: '请选择年', trigger: 'change' }],
          startTime: [
            { required: true, message: '请选择开始时间', trigger: 'change' }
          ]
        },
        isEdit: false
      };
    },
    mounted() {
      if (this.id) {
        this.editInfo();
        this.isEdit = true;
      }
    },
    methods: {
      // 选择开始时间或者期间类型
      async handleChange() {
        // 获取周期
        if (this.form.type && this.form.startTime) {
          const param = {
            startTime: new Date(this.form.startTime),
            type: this.form.type
          };
          const { data } = await postExamineDetail(param);
          if (data.code == 200) {
            this.source = data.data;
          }
        } else {
          this.source = [];
        }
        // 获取结束时间
        if (this.form.startTime) {
          const param = {
            startTime: new Date(this.form.startTime)
          };
          const { data } = await postEndTime(param);
          if (data.code == 200) {
            this.form.endTime = data.data;
          }
        } else {
          this.form.endTime = undefined;
        }
      },
      // 判断考评时间
      handleDeadlin(row) {
        if (row.deadline) {
          if (new Date(row.deadline) <= new Date(row.endTime)) {
            this.$message.warning('考评截止日期大于结束时间');
            row.deadline = '';
          }
        }
      },
      // 详情
      async editInfo() {
        const res = await getDetail(this.id);
        const data = res.data.data;
        this.source = data.items;
        this.form = {
          type: data.type,
          yearly: data.yearly.toString(),
          startTime: data.startTime,
          endTime: data.endTime
        };
      },
      // 保存
      submit() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            this.form.items = this.source;
            const param = {
              type: this.form.type,
              startTime: new Date(this.form.startTime),
              yearly: this.form.yearly,
              items: this.source
            };
            if (this.id) {
              param.id = this.id;
              const { data } = await postEdit(param);
              if (data.success) {
                this.$message.success('修改成功');
                this.$emit('close', true);
              } else {
                this.$message.warning(data.msg);
              }
            } else {
              const { data } = await postAdd(param);
              if (data.success) {
                this.$message.success('新增成功');
                this.$emit('close', true);
              } else {
                this.$message.warning(data.msg);
              }
            }
          }
        });
      },
      // 关闭弹窗
      back() {
        this.$emit('close', false);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 10px 30px;

    .nav_header {
      position: relative;
      margin-bottom: 20px;
      border-bottom: 1px solid #dfdfdf;

      h3 {
        margin: 10px 0;
        padding-left: 12px;
        font-size: 17px;
        border-left: 5px solid #1e9fff;
      }
    }

    .footer_button {
      margin: 30px 0 0;
      text-align: center;
    }
  }
</style>
