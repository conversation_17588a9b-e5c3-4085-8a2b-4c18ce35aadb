<template>
  <div>
    <el-table
      style="width: 100%"
      border
      size="small"
      v-bind="bindProps()"
      :data="tempList"
      :cell-style="fontStyle"
      :header-cell-style="fontStyle"
      :span-method="spanMethod"
    >
      <el-table-column
        fixed
        prop="serialNumber"
        label="序号"
        width="50"
        align="center"
      >
      </el-table-column>
      <el-table-column fixed label="被评价机构" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.deptName || '---' }}
        </template>
      </el-table-column>
      <el-table-column fixed label="指标名称" width="150" align="center">
        <template slot-scope="scope">
          {{ scope.row.indexName || '---' }}
        </template>
      </el-table-column>
      <el-table-column label="基本分值" width="80" align="center">
        <template slot-scope="scope">
          {{ scope.row.basicScore || '---' }}
        </template>
      </el-table-column>
      <el-table-column
        label="考核目标及计分标准(点击查看)"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <div
            style="cursor: pointer"
            class="show-text"
            @click="() => handleDetail(scope.row)"
          >
            {{ scope.row.standard || '---' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" width="100">
        <template slot-scope="scope">
          <el-input
            size="small"
            v-model.trim="scope.row.unit"
            @change="handleChange"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="完成情况(点击输入)"
        width="140"
        align="center"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <div
            v-show="
              scope.row.completionSituation &&
              scope.row.completionSituation.length
            "
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput(scope.row)"
          >
            {{ scope.row.completionSituation || '---' }}
          </div>
          <div
            v-show="
              !scope.row.completionSituation ||
              !scope.row.completionSituation.length
            "
            class="placeholder_style"
            @click="() => handleInput(scope.row)"
          >
            点击输入内容
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="累计完成"
        align="center"
        width="160"
        label-class-name="label_required"
      >
        <template slot-scope="scope">
          <el-input-number
            size="small"
            v-model.number="scope.row.accumulatedCompletion"
            controls-position="right"
            style="width: 100%"
            :precision="2"
            :step="0.01"
            @change="() => handleChangeNum2(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="考核结果(点击输入)" width="140" align="center">
        <template slot-scope="scope">
          <div
            v-show="
              scope.row.assessmentResult && scope.row.assessmentResult.length
            "
            style="cursor: pointer"
            class="show-text"
            @click="() => handleInput1(scope.row)"
          >
            {{ scope.row.assessmentResult || '---' }}
          </div>
          <div
            v-show="
              !scope.row.assessmentResult || !scope.row.assessmentResult.length
            "
            class="placeholder_style"
            @click="() => handleInput1(scope.row)"
          >
            点击输入内容
          </div>
        </template>
      </el-table-column>
      <el-table-column label="年度累计考核加减分" width="140" align="center">
        <template slot-scope="scope">
          <el-input-number
            size="small"
            v-model.number="scope.row.accumulatedPmScore"
            controls-position="right"
            style="width: 100%"
            :precision="2"
            :step="0.01"
            @change="() => handleChangeNum(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="截止上周期累计考核加减分"
        align="center"
        width="140"
        prop="lastMonthAccumulatedPmScore"
      >
        <template slot-scope="scope">
          <el-input-number
            size="small"
            v-model.number="scope.row.lastMonthAccumulatedPmScore"
            controls-position="right"
            style="width: 100%"
            :precision="2"
            :step="0.01"
            @change="() => handleChangeNum(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="本周期考核加减分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentPmScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column label="本周期考核得分" align="center">
        <template slot-scope="scope">
          {{ scope.row.currentScore | scoreFilter }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="80" align="center">
        <template slot-scope="scope">
          <el-button
            size="small"
            type="text"
            @click="() => setTarget(scope.row)"
            >设置目标</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!--  设置目标  -->
    <evaluate-list-window
      :visited="visited"
      :temp="temp"
      @save="save"
      @close="close"
    />
    <!--  同步文本输入  -->
    <el-dialog
      width="600px"
      append-to-body
      :title="dialogTitle"
      :visible="inputVisited"
      :close-on-click-modal="false"
      @close="inputClose"
    >
      <el-input
        v-model.trim="tempInput"
        type="textarea"
        :placeholder="placeholder"
        maxlength="1000"
        show-word-limit
        :rows="8"
        :disabled="disabled"
      />
      <div slot="footer">
        <el-button size="small" @click="inputClose">返 回</el-button>
        <el-button
          v-if="!disabled"
          size="small"
          type="primary"
          @click="inputSave"
          >完 成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { decimalPointFn } from '@/util/util';
  import { cloneDeep } from 'lodash';
  import { mapState } from 'vuex';
  import EvaluateListWindow from './evaluate-list-window';
  export default {
    name: 'evaluate-list',
    components: {
      EvaluateListWindow
    },
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      },
      full: {
        type: Boolean,
        default: false
      },
      periodNum: {
        type: Number,
        default: 1
      }
    },
    watch: {
      list: {
        handler(newVal) {
          this.tempList = cloneDeep(newVal);
          this.tempList.map((item) => {
            item.accumulatedCompletion =
              item.accumulatedCompletion || item.accumulatedCompletion == 0
                ? Number(item.accumulatedCompletion)
                : undefined;
            item.calculatePmScore =
              item.calculatePmScore || item.calculatePmScore == 0
                ? Number(item.calculatePmScore)
                : undefined;
            return item;
          });
          this.rowspan();
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        inputVisited: false,
        visited: false,
        tempInput: '',
        dialogTitle: '',
        temp: {},
        tempList: {},
        spanArr: [],
        position: 0,
        placeholder: '',
        disabled: false,
        tempField: ''
      };
    },
    computed: {
      ...mapState({
        font: (state) => state.commonComp.font
      }),
      fontStyle() {
        return this.full
          ? {
              fontSize: `${this.font}px`
            }
          : undefined;
      }
    },
    mounted() {
      document.addEventListener('resize', this.bindProps, false);
      this.rowspan();
    },
    destroyed() {
      document.removeEventListener('resize', this.bindProps);
    },
    methods: {
      setTarget(row) {
        this.temp = row;
        this.visited = true;
      },
      bindProps() {
        if (this.maxHeight === 300) {
          return {
            maxHeight: 300
          };
        }
        if (this.maxHeight === 0) {
          return {
            maxHeight: document.body.clientHeight - 200
          };
        }
      },
      // 完成情况
      handleInput(row) {
        this.dialogTitle = '完成情况';
        this.inputVisited = true;
        this.temp = row;
        this.tempInput = row.completionSituation;
        this.tempField = 'completionSituation';
      },
      // 考核结果
      handleInput1(row) {
        this.dialogTitle = '考核结果';
        this.inputVisited = true;
        this.temp = row;
        this.tempInput = row.assessmentResult;
        this.tempField = 'assessmentResult';
      },
      // 关闭弹窗
      close() {
        this.visited = false;
        this.temp = {};
      },
      // 保存
      save(data, id) {
        this.tempList.map((item) => {
          if (item.id === id) {
            item.target = data;
          }
          return item;
        });
        this.$emit('sync', this.tempList);
        this.close();
      },
      handleDetail(row) {
        this.dialogTitle = '考核目标及计分标准';
        this.tempInput = row.standard;
        this.inputVisited = true;
        this.disabled = true;
      },
      // 完成情况弹窗关闭
      inputClose() {
        this.inputVisited = false;
        this.temp = {};
        this.tempField = '';
        this.placeholder = '请输入完成情况';
        this.tempInput = '';
        this.disabled = false;
      },
      // 完成情况弹窗保存
      inputSave() {
        this.tempList.map((item) => {
          if (item.id === this.temp.id) {
            item[this.tempField] = this.tempInput;
          }
          return item;
        });
        this.$emit('sync', this.tempList);
        this.inputVisited = false;
      },
      handleChange() {
        this.$emit('sync', this.tempList);
      },
      handleChangeNum2(row) {
        if (typeof row.accumulatedCompletion !== 'number') {
          row.accumulatedCompletion = null;
        }
        this.handleChange();
      },
      handleChangeNum(row) {
        if (typeof row.accumulatedPmScore !== 'number') {
          row.accumulatedPmScore = null;
        }
        if (typeof row.lastMonthAccumulatedPmScore !== 'number') {
          row.lastMonthAccumulatedPmScore = null;
        }
        // 本周期考核加减分
        row.currentPmScore =
          Number(row.accumulatedPmScore) -
          Number(row.lastMonthAccumulatedPmScore);
        // 本周期考核得分
        row.currentScore = Number(row.currentPmScore) + Number(row.basicScore);

        row.currentPmScore = decimalPointFn(row.currentPmScore);
        row.currentScore = decimalPointFn(row.currentScore);
        this.handleChange();
      },
      // handleChangeNum4(row) {
      //   if (typeof row.lastMonthAccumulatedPmScore !== 'number') {
      //     row.lastMonthAccumulatedPmScore = null;
      //   }
      //   this.handleChange();
      // },
      rowspan() {
        this.list.forEach((item, index) => {
          if (index === 0) {
            this.spanArr.push(1);
            this.position = 0;
          } else {
            if (
              this.list[index].serialNumber ===
              this.list[index - 1].serialNumber
            ) {
              this.spanArr[this.position] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.position = index;
            }
          }
        });
      },
      spanMethod({ rowIndex, columnIndex }) {
        if (columnIndex === 0 || columnIndex === 1) {
          const _row = this.spanArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col
          };
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .cell.label_required {
    width: auto !important;
    overflow: visible;

    &::before {
      position: absolute;
      left: 0;
      display: block;
      color: red;
      content: '*';
    }
  }

  .show-text {
    max-height: 70px;
    overflow: hidden;
    line-height: 1;
    text-align: left;
  }

  .placeholder_style {
    color: #b0b0b0;
    cursor: pointer;
  }
</style>
