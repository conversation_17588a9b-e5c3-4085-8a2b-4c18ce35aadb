<template>
  <el-table :data="list" border style="width: 100%">
    <el-table-column label="排名" width="180" align="center">
      <template slot-scope="scope">
        {{ `第${scope.row.rank}名` }}
      </template>
    </el-table-column>
    <el-table-column prop="deptName" label="被评价机构" align="center">
    </el-table-column>
    <el-table-column prop="score" label="得分" align="center">
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'evaluate-list-detail',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      },
      maxHeight: {
        type: Number,
        default: 300
      }
    }
  };
</script>
