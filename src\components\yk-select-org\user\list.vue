<template>
  <el-table
    v-loading="loading"
    :data="tableData"
    size="small"
    style="width: 100%"
  >
    <el-table-column type="index" label="序号" width="50"> </el-table-column>
    <el-table-column
      prop="realName"
      label="姓名"
      align="center"
      min-width="100"
    >
    </el-table-column>
    <el-table-column
      prop="deptName"
      label="机构"
      align="center"
      min-width="100"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="postName"
      label="岗位"
      align="center"
      min-width="100"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column prop="phone" label="手机号" align="center">
    </el-table-column>
    <el-table-column
      align="center"
      label="是否选择"
      fixed="right"
      min-width="80"
    >
      <template slot-scope="scope">
        <el-switch
          v-model="scope.row.status"
          @change="() => $emit('selectEmit', scope.row, scope.row.status)"
        >
        </el-switch>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'list',
    props: {
      tableData: {
        type: Array,
        default: function () {
          return [];
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    }
  };
</script>
