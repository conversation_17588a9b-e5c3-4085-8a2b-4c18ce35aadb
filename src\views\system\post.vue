<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.post_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="category">
        <el-tag>{{ row.categoryName }}</el-tag>
      </template>
    </avue-crud>
    <!-- 批量删除结果弹窗 -->
    <del-res
      :resVisible="resVisible"
      :resData="resData"
      @close="handleModalClose"
    />
  </basic-container>
</template>

<script>
  import { getList, getDetail, add, update, remove } from '@/api/system/post';
  import { mapGetters } from 'vuex';
  import website from '@/config/website';
  import DelRes from '@/components/batch-delete-result';

  export default {
    components: { DelRes },
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: '所属租户',
              prop: 'tenantId',
              type: 'tree',
              dicUrl: '/api/szyk-system/tenant/select',
              addDisplay: false,
              editDisplay: false,
              viewDisplay: website.tenantMode,
              span: 24,
              props: {
                label: 'tenantName',
                value: 'tenantId'
              },
              hide: !website.tenantMode,
              rules: [
                {
                  required: true,
                  message: '请输入所属租户',
                  trigger: 'click'
                }
              ]
            },
            {
              label: '岗位类型',
              prop: 'category',
              type: 'select',
              dicUrl: '/api/szyk-system/dict/dictionary?code=post_category',
              props: {
                label: 'dictValue',
                value: 'dictKey'
              },
              dataType: 'number',
              slot: true,
              search: true,
              rules: [
                {
                  required: true,
                  message: '请选择岗位类型',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '岗位编号',
              prop: 'postCode',
              search: true,
              rules: [
                {
                  required: true,
                  message: '请输入岗位编号',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '岗位名称',
              prop: 'postName',
              search: true,
              rules: [
                {
                  required: true,
                  message: '请输入岗位名称',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '岗位排序',
              prop: 'sort',
              type: 'number',
              rules: [
                {
                  required: true,
                  message: '请输入岗位排序',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '岗位描述',
              prop: 'remark',
              type: 'textarea',
              span: 24,
              minRows: 6,
              hide: true
            }
          ]
        },
        data: [],
        // 删除结果
        resData: {},
        resVisible: false
      };
    },
    computed: {
      ...mapGetters(['permission']),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.system_post_add, false),
          viewBtn: this.vaildData(this.permission.post_view, false),
          delBtn: this.vaildData(this.permission.post_delete, false),
          editBtn: this.vaildData(this.permission.post_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(',');
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            done();
          },
          (error) => {
            window.console.log(error);
            loading();
          }
        );
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            done();
          },
          (error) => {
            window.console.log(error);
            loading();
          }
        );
      },
      rowDel(row) {
        this.$confirm('确定删除选择的数据吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            const res = await remove(row.id);
            const data = res.data.data;
            if (data.failureNumber === 0) {
              this.$message({
                type: 'success',
                message: '操作成功!'
              });
              // 数据回调进行刷新
              this.onLoad(this.page);
            } else {
              this.$message({
                type: 'error',
                message: data.detailVOList[0].message
              });
            }
          } catch (e) {
            console.error(e);
          }
        });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定删除选择的数据吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            const res = await remove(this.ids);
            this.resData = res.data.data;
            this.resVisible = true;
          } catch (e) {
            console.error(e);
          }
        });
      },
      // 删除结果弹窗关闭
      handleModalClose() {
        this.resVisible = false;
        // 表格数据重载
        this.onLoad(this.page);
        // 刷新表格数据并重载
        this.$refs.crud.toggleSelection();
      },
      beforeOpen(done, type) {
        if (['edit', 'view'].includes(type)) {
          getDetail(this.form.id).then((res) => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style></style>
