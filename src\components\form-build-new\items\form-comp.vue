<template>
  <div class="bc-form-build-form">
    <el-card
      v-for="(item, index) in hValue"
      :key="index"
      class="box-card"
      shadow="never"
    >
      <div slot="header" class="clearfix">
        {{ data.name }}{{ index + 1 }}
        <el-button
          v-if="value.length > 1"
          @click="onRemove(index)"
          type="text"
          icon="el-icon-delete"
          class="delete-button"
        ></el-button>
      </div>
      <c-form-build
        ref="formBuild"
        v-model="hValue[index]"
        :component-list="componentList"
        :disabled="disabled"
        :accept="accept"
        :total-value="totalValue"
      ></c-form-build>
    </el-card>
    <div @click="onAdd" class="add-button">
      <i class="el-icon-plus" />
      {{ data.action }}
    </div>
  </div>
</template>

<script>
  export default {
    name: 'FormComp',
    components: {
      CFormBuild: () => import('@/components/form-build-new')
    },
    props: {
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      data: {
        type: Object,
        default() {
          return {};
        }
      },
      componentList: {
        type: Array,
        default() {
          return [];
        }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      accept: String,
      totalValue: Object
    },
    computed: {
      hValue: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    created() {
      if (!this.value.length) {
        this.hValue = [{}];
      }
    },
    methods: {
      onAdd() {
        this.hValue.push({});
      },
      onRemove(index) {
        this.hValue.splice(index, 1);
      },
      validate() {
        return new Promise((resolve, reject) => {
          let list = this.$refs.formBuild;
          if (!list) {
            resolve();
            return;
          }
          if (!(list instanceof Array)) {
            list = [list];
          }
          Promise.all(Array.from(list, (item) => item.validate()))
            .then(() => {
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-ui';

  .bc-form-build-form {
    .el-card {
      margin-bottom: 18px;
      border-color: #d9d9d9;

      .delete-button {
        &.el-button--text {
          // color: $--color-text-placeholder;
          float: right;
          padding: 0;
        }
      }
    }

    .add-button {
      width: fit-content;
      line-height: 1;

      // color: $--button-color-active;
      cursor: pointer;

      &:hover {
        // color: $--color-primary;
      }
    }
  }
</style>

<style lang="scss">
  .bc-form-build-form {
    .el-card {
      .el-card__header {
        padding: 16px 12px !important;
        background: #fafafa;
        border-color: #d9d9d9;

        .clearfix {
          line-height: 15px;
        }
      }

      .el-card__body {
        padding: 18px 18px 0 !important;
      }
    }
  }
</style>
